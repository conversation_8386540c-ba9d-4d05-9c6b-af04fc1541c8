# Design Document

## Overview

本设计文档描述了Wave页面目标值发送功能的技术实现方案。该功能通过集成现有的LVGL UI事件处理和CANopen通信协议栈，实现用户通过滑块设置目标值并通过CAN总线发送给电机控制器的完整流程。

## Architecture

### 系统架构图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Wave UI Page  │    │  Event Handler   │    │  CAN Interface  │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   Slider    │ │───▶│ │ Button Click │ │───▶│ │ RPDO Send   │ │
│ └─────────────┘ │    │ │   Handler    │ │    │ │  Function   │ │
│ ┌─────────────┐ │    │ └──────────────┘ │    │ └─────────────┘ │
│ │  Dropdown   │ │───▶│ ┌──────────────┐ │    │ ┌─────────────┐ │
│ └─────────────┘ │    │ │ Mode Select  │ │    │ │ TWAI Driver │ │
│ ┌─────────────┐ │    │ │   Logic      │ │    │ └─────────────┘ │
│ │Send Button  │ │───▶│ └──────────────┘ │    └─────────────────┘
│ └─────────────┘ │    └──────────────────┘
└─────────────────┘
```

### 数据流程

1. 用户通过滑块设置目标值
2. 用户通过下拉框选择控制模式
3. 用户点击发送按钮触发事件
4. 事件处理函数获取滑块值和模式选择
5. 根据模式映射到对应的RPDO类型
6. 构造CAN消息并通过TWAI驱动发送
7. 记录发送结果到日志

## Components and Interfaces

### UI事件处理组件

**文件位置:** `main/guider/user/ui_setup_wave.c`

**新增函数:**
```c
/**
 * @brief 发送按钮点击事件处理函数
 * @param e LVGL事件对象
 */
static void Wave_Page_btn_send_event_handler(lv_event_t *e);
```

**修改函数:**
```c
void events_init_Wave_Page(lv_ui *ui)
{
    // 现有代码...
    
    // 新增：发送按钮事件绑定
    lv_obj_add_event_cb(ui->Wave_Page_btn_send, Wave_Page_btn_send_event_handler, LV_EVENT_ALL, ui);
}
```

### CAN通信组件

**文件位置:** `main/bsp/canopen.c` 和 `main/bsp/inc/canopen.h`

**新增函数声明 (canopen.h):**
```c
/**
 * @brief 发送RPDO消息到指定节点
 * @param rpdo_type RPDO类型 (0x180-0x184)
 * @param target_node_id 目标节点ID
 * @param target_value 目标值
 * @return esp_err_t 发送结果
 */
esp_err_t canopen_send_rpdo(uint16_t rpdo_type, uint8_t target_node_id, int32_t target_value);
```

**新增函数实现 (canopen.c):**
```c
esp_err_t canopen_send_rpdo(uint16_t rpdo_type, uint8_t target_node_id, int32_t target_value);
```

### 模式映射组件

**控制模式到RPDO类型的映射关系:**

| 下拉框索引 | 模式名称 | RPDO类型 | CAN ID计算 | 目标变量 |
|-----------|----------|----------|------------|----------|
| 0 | 力矩环 | 0x180 | 0x180 + node_id | target_iq |
| 1 | 速度环 | 0x181 | 0x181 + node_id | target_speed |
| 2 | 位置环 | 0x182 | 0x182 + node_id | target_position |
| 3 | 速度轨迹环 | 0x183 | 0x183 + node_id | target_speed |
| 4 | 位置轨迹环 | 0x184 | 0x184 + node_id | target_position |

## Data Models

### CAN消息数据结构

```c
typedef struct {
    uint32_t identifier;        // CAN ID
    uint8_t data_length_code;   // 数据长度
    uint8_t data[8];           // 数据内容
    bool extd;                 // 扩展帧标志
    bool rtr;                  // 远程帧标志
} rpdo_message_t;
```

### 目标值数据打包格式

**RPDO消息数据格式 (8字节):**
- 字节0-3: 保留字段 (设为0)
- 字节4-7: 目标值 (32位，大端序)

```c
// 数据打包示例
data[0] = 0x00;  // 保留
data[1] = 0x00;  // 保留  
data[2] = 0x00;  // 保留
data[3] = 0x00;  // 保留
data[4] = (target_value >> 24) & 0xFF;  // 目标值高字节
data[5] = (target_value >> 16) & 0xFF;
data[6] = (target_value >> 8) & 0xFF;
data[7] = target_value & 0xFF;          // 目标值低字节
```

## Error Handling

### 错误类型和处理策略

1. **UI组件访问错误**
   - 检查UI对象指针有效性
   - 记录错误日志并安全返回

2. **CAN发送错误**
   - 使用twai_transmit返回值判断发送状态
   - 超时处理：设置10ms发送超时
   - 记录详细错误信息到ESP_LOG

3. **数据获取错误**
   - 滑块值获取失败：使用默认值0
   - 下拉框选择获取失败：使用默认模式（力矩环）

4. **模式映射错误**
   - 未知模式索引：默认映射到力矩环模式
   - 记录警告日志

### 错误日志格式

```c
// 成功日志
ESP_LOGI(TAG, "Target sent successfully - Mode: %d, Value: %ld, RPDO: 0x%03X", mode, value, rpdo_type);

// 错误日志
ESP_LOGE(TAG, "Failed to send target - Mode: %d, Value: %ld, Error: %s", mode, value, esp_err_to_name(ret));
```

## Testing Strategy

### 单元测试

1. **UI事件处理测试**
   - 模拟按钮点击事件
   - 验证滑块值和下拉框选择获取
   - 测试UI对象空指针处理

2. **CAN发送功能测试**
   - 测试不同RPDO类型的消息构造
   - 验证数据打包格式正确性
   - 测试发送超时和错误处理

3. **模式映射测试**
   - 验证所有模式索引的正确映射
   - 测试边界条件和异常输入

### 集成测试

1. **端到端功能测试**
   - 完整的用户操作流程测试
   - 验证CAN总线上的消息格式
   - 测试与电机控制器的通信

2. **性能测试**
   - 测试发送响应时间
   - 验证UI响应性能
   - 测试并发操作处理

### 测试工具

1. **CAN分析仪**
   - 监控CAN总线消息
   - 验证消息格式和时序

2. **ESP32日志监控**
   - 实时查看发送日志
   - 监控错误和警告信息

3. **模拟电机控制器**
   - 接收和解析RPDO消息
   - 验证目标值设置效果