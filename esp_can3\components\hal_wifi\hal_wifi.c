#include <stdio.h>
#include "hal_wifi.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

#define TAG "hal_wifi"

#define WIFI_MAX_STA_CONN 1
#define WIFI_MAXIMUM_RETRY 5

#define WIFI_SSID CONFIG_WIFI_SSID
#define WIFI_PASSWD CONFIG_WIFI_PASSWD

#define DEFAULT_SCAN_LIST_SIZE CONFIG_WIFI_SCAN_LIST_SIZE

typedef struct
{
    QueueHandle_t serv_q;
    bool running;
    uint32_t delay;

    hal_wifi_event_callback event_cb;
} wifi_service_t;

TaskHandle_t wifi_conn_service;
TaskHandle_t wifi_scan_service;

wifi_service_t wifi_conn_serv_param;
wifi_service_t wifi_scan_serv_param;

static hal_wifi_credentials_t wifi_credentials;

static int conn_retry_num = 0;
static wifi_mode_t mode;
static esp_netif_t *sta_netif;

volatile bool wifi_conn_flag;
volatile bool wifi_scan_flag;
static bool wifi_initialized = false;

// STATIC
static void wifi_conn_task(void *pvParameters);
static void wifi_scan_task(void *pvParameters);
static void wifi_init_sta(void);
static void disconnect_handler(void *arg, esp_event_base_t event_base,
                               int32_t event_id, void *event_data);
static void connect_handler(void *arg, esp_event_base_t event_base,
                            int32_t event_id, void *event_data);


void hal_wifi_init()
{
#if CONFIG_WIFI_ENABLE
#if (ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(4, 1, 0))
    ESP_ERROR_CHECK(esp_netif_init());
#else
    tcpip_adapter_init();
#endif

    ESP_ERROR_CHECK(esp_event_loop_create_default());

#endif
}

void hal_wifi_start()
{
#if CONFIG_WIFI_ENABLE
    // 检查是否已经初始化
    if (wifi_initialized) {
        ESP_LOGI(TAG, "WiFi already initialized");
        return;
    }

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    mode = WIFI_MODE_STA; // 改为 STA 模式，只支持 Station

    // 只创建 STA 网络接口
    sta_netif = esp_netif_create_default_wifi_sta();
    esp_wifi_set_default_wifi_sta_handlers();

    ESP_ERROR_CHECK(esp_wifi_set_ps(WIFI_PS_MAX_MODEM));
    ESP_ERROR_CHECK(esp_wifi_set_mode(mode));

    ESP_ERROR_CHECK(esp_wifi_start());

    wifi_initialized = true;
    ESP_LOGI(TAG, "WiFi started in STA mode only");

    wifi_scan_serv_param.running = false;
    wifi_scan_serv_param.delay = 1000;
    wifi_scan_serv_param.serv_q = xQueueCreate(8, sizeof(hal_wifi_scan_msg_t));

    xTaskCreatePinnedToCore(wifi_scan_task, "wifi_scan_task", 8 * 1024, (void *)&wifi_scan_serv_param, 3, &wifi_scan_service, 0);

    // 自动连接WiFi
    hal_wifi_sta_do_connect();
#endif
}

void hal_wifi_stop()
{
#if CONFIG_WIFI_ENABLE
    // 停止服务
    if (wifi_scan_service)
    {
        hal_wifi_scan_msg_t scan_msg = {
            .msg_id = WIFI_SCAN_MSG_STOP,
            .user_data = NULL,
        };
        xQueueSend(wifi_scan_serv_param.serv_q, &scan_msg, 0);

        vTaskDelete(wifi_scan_service);
        wifi_scan_service = NULL;
    }

    esp_err_t err = esp_wifi_stop();
    if (err == ESP_ERR_WIFI_NOT_INIT)
    {
        return;
    }

    ESP_ERROR_CHECK(err);
    ESP_ERROR_CHECK(esp_wifi_deinit());
    ESP_ERROR_CHECK(esp_wifi_clear_default_wifi_driver_and_handlers(sta_netif));
    esp_netif_destroy(sta_netif);
    sta_netif = NULL;

    wifi_initialized = false;
#endif
}

void hal_wifi_shutdown()
{
#if CONFIG_WIFI_ENABLE
    hal_wifi_sta_do_disconnect();
    hal_wifi_stop();
#endif
}

void hal_wifi_sta_do_connect()
{
#if CONFIG_WIFI_ENABLE
#if CONFIG_WIFI_LOAD_FROM_STORE
    if ((wifi_credentials.ssid == NULL && wifi_credentials.passwd == NULL) ||
        (strlen(wifi_credentials.ssid) == 0 || strlen(wifi_credentials.passwd) == 0))
    {
        ESP_LOGW(TAG, "wifi credentials is null");
        return;
    }
#endif

    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &connect_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, WIFI_EVENT_STA_DISCONNECTED, &disconnect_handler, NULL));

    if (wifi_conn_serv_param.event_cb != NULL)
    {
#if CONFIG_WIFI_LOAD_FROM_STORE
        if (strlen(wifi_credentials.ssid) == 0 || strlen(wifi_credentials.passwd) == 0)
        {
            // wifi名称或密码为空时不进行连接
            wifi_conn_serv_param.event_cb(WIFI_MODE_STA, EVENT_STA_DISCONNECT);
            return;
        }
        else
#endif
        {
            wifi_conn_serv_param.event_cb(WIFI_MODE_STA, EVENT_STA_CONNECT);
        }
    }

    wifi_init_sta();
    esp_wifi_connect();

    wifi_conn_serv_param.running = false;
    wifi_conn_serv_param.delay = 15000; // s

    xTaskCreatePinnedToCore(wifi_conn_task, "wifi_conn_task", 4 * 1024, (void *)&wifi_conn_serv_param, 3, &wifi_conn_service, 0);
#endif
}

void hal_wifi_sta_do_disconnect()
{
#if CONFIG_WIFI_ENABLE
    if (wifi_conn_service)
    {
        wifi_conn_serv_param.running = false;
        vTaskDelete(wifi_conn_service);
        wifi_conn_service = NULL;
    }

    if (wifi_conn_serv_param.event_cb != NULL)
        wifi_conn_serv_param.event_cb(WIFI_MODE_STA, EVENT_STA_DISCONNECT);
    ESP_ERROR_CHECK(esp_event_handler_unregister(IP_EVENT, IP_EVENT_STA_GOT_IP, &connect_handler));
    ESP_ERROR_CHECK(esp_event_handler_unregister(WIFI_EVENT, WIFI_EVENT_STA_DISCONNECTED, &disconnect_handler));
    esp_wifi_disconnect();

    wifi_conn_flag = false;
    wifi_conn_serv_param.event_cb = NULL;
#endif
}

void hal_wifi_scan(hal_wifi_scan_msg_t *msg)
{
    if (mode == WIFI_MODE_AP)
        return;

    wifi_scan_flag = true;

    if (wifi_conn_flag == false && wifi_conn_service)
    {
        hal_wifi_sta_do_disconnect();
    }

#if CONFIG_WIFI_ENABLE
    BaseType_t xReturn = pdPASS;
    xReturn = xQueueSend(wifi_scan_serv_param.serv_q, msg, 0);
    (void)xReturn; // 避免未使用变量警告
#endif
}

/*******************************SET*********************************/
void hal_wifi_power_save(bool en)
{
    if (en)
        esp_wifi_set_ps(WIFI_PS_MIN_MODEM);
    else
        esp_wifi_set_ps(WIFI_PS_MAX_MODEM);
}

void hal_wifi_set_mode(wifi_mode_t _mode)
{
    mode = _mode;
}

void hal_wifi_set_credentials(const char *ssid, const char *passwd)
{
    wifi_credentials.ssid = ssid;
    wifi_credentials.passwd = passwd;
}

void hal_wifi_conn_event_cb_set(hal_wifi_event_callback cb)
{
    wifi_conn_serv_param.event_cb = cb;
}

/*******************************GET*********************************/
bool hal_wifi_is_scan()
{
    return wifi_scan_flag;
}

wifi_mode_t hal_wifi_get_mode()
{
    return mode;
}

bool hal_wifi_get_connect_state()
{
    return wifi_conn_flag;
}

char *hal_wifi_get_ip()
{
    static char buff[33];
#if CONFIG_WIFI_ENABLE
    memset(buff, 0x0, sizeof(buff));

    // 优先获取STA模式的IP（如果已连接）
    if (wifi_conn_flag && sta_netif != NULL)
    {
        esp_netif_ip_info_t info_t = {0};
        esp_netif_get_ip_info(sta_netif, &info_t);
        esp_ip4addr_ntoa(&info_t.ip, buff, sizeof(buff));
        return buff;
    }

    // STA模式下，如果未连接则返回NULL
#endif
    return NULL;
}

/*******************************STATIC*********************************/
static void wifi_conn_task(void *pvParameters)
{
    wifi_service_t *service = (wifi_service_t *)pvParameters;

    service->running = true;
    while (service->running)
    {
        if (!wifi_conn_flag &&
            (mode == WIFI_MODE_STA || mode == WIFI_MODE_APSTA) &&
            conn_retry_num > 0)
        {
            ESP_LOGW(TAG, "wifi connect retry");

            // 尝试连接
            conn_retry_num = 0;
            esp_wifi_connect();
        }

        // const char *ip_addr = hal_wifi_get_ip();
        // if (ip_addr != NULL)
        //     ESP_LOGI(TAG, "local ip: %s", ip_addr);

        vTaskDelay(pdMS_TO_TICKS(service->delay));
    }

    ESP_LOGI(TAG, "wifi connect service stopped!");
    vTaskDelete(NULL);
}

static void wifi_scan_task(void *pvParameters)
{
    wifi_service_t *service = (wifi_service_t *)pvParameters;

    BaseType_t xReturn = pdFALSE;

    uint8_t number = DEFAULT_SCAN_LIST_SIZE;
    uint16_t ap_count = 0;

    hal_wifi_scan_msg_t msg = {0};
    wifi_ap_record_t *ap_info = (wifi_ap_record_t *)malloc(DEFAULT_SCAN_LIST_SIZE * sizeof(wifi_ap_record_t));

    service->running = true;
    while (service->running)
    {
        xReturn = xQueueReceive(service->serv_q, &msg, portMAX_DELAY);
        if (xReturn == pdTRUE)
        {
            switch (msg.msg_id)
            {
            case WIFI_SCAN_MSG_START:
            {
                memset(ap_info, 0, sizeof(ap_info));

                ESP_ERROR_CHECK(esp_wifi_set_mode(mode));
                ESP_ERROR_CHECK(esp_wifi_start());
                esp_wifi_scan_start(NULL, true);

                ESP_ERROR_CHECK(esp_wifi_scan_get_ap_num(&ap_count));
                ESP_ERROR_CHECK(esp_wifi_scan_get_ap_records(&number, ap_info));
                ESP_LOGI(TAG, "Total APs scanned = %u", ap_count);

                memset(msg.scan_result, 0x0, sizeof(hal_wifi_scan_result_t));

                // for (int i = 0; (i < DEFAULT_SCAN_LIST_SIZE) && (i < ap_count); i++)
                // {
                //     ESP_LOGI(TAG, "SSID \t\t%s", ap_info[i].ssid);
                //     ESP_LOGI(TAG, "RSSI \t\t%d", ap_info[i].rssi);
                // }

                // 完成回调
                msg.scan_result->wifi_cnt = ap_count;
                msg.scan_result->state = ap_count > 0;
                msg.scan_result->ap_info = ap_info;
                wifi_scan_flag = false;

                if (msg.scan_cb)
                    msg.scan_cb(&msg);
            }
            break;
            case WIFI_SCAN_MSG_STOP:
            {
                service->running = false;
                if (service->serv_q)
                {
                    vQueueDelete(service->serv_q);
                }
            }
            break;
            }
        }
    }

    if (ap_info)
    {
        free(ap_info);
        ap_info = NULL;
    }

    ESP_LOGI(TAG, "wifi scan service stopped!");
    vTaskDelete(NULL);
}



static void wifi_init_sta()
{
    wifi_config_t wifi_config;
    memset(&wifi_config, 0, sizeof(wifi_config_t));
#if CONFIG_WIFI_LOAD_FROM_STORE
    snprintf((char *)wifi_config.sta.ssid, 32, "%s", wifi_credentials.ssid);
    snprintf((char *)wifi_config.sta.password, 64, "%s", wifi_credentials.passwd);
    ESP_LOGI(TAG, "connect to ap SSID:|%s| password:|%s|", wifi_credentials.ssid, wifi_credentials.passwd);
#else
    snprintf((char *)wifi_config.sta.ssid, 32, "%s", WIFI_SSID);
    snprintf((char *)wifi_config.sta.password, 64, "%s", WIFI_PASSWD);
    ESP_LOGI(TAG, "connect to ap SSID:|%s| password:|%s|", WIFI_SSID, WIFI_PASSWD);
#endif
    ESP_ERROR_CHECK(esp_wifi_set_config(ESP_IF_WIFI_STA, &wifi_config));
    ESP_LOGI(TAG, "wifi_init_sta finished.");
}

static void disconnect_handler(void *arg, esp_event_base_t event_base,
                               int32_t event_id, void *event_data)
{
    if (wifi_scan_flag)
        return;

    ESP_LOGE(TAG, "connect to the AP fail");

    if (conn_retry_num < WIFI_MAXIMUM_RETRY)
    {
        ESP_LOGI(TAG, "retry to connect to the AP");
        conn_retry_num++;
        esp_wifi_connect();
    }

    if (conn_retry_num == WIFI_MAXIMUM_RETRY)
    {
        wifi_conn_flag = false;

        if (wifi_conn_serv_param.event_cb != NULL)
            wifi_conn_serv_param.event_cb(WIFI_MODE_STA, EVENT_STA_CONNECT_LOST);
    }
}

static void connect_handler(void *arg, esp_event_base_t event_base,
                            int32_t event_id, void *event_data)
{
    if (wifi_conn_serv_param.event_cb != NULL)
        wifi_conn_serv_param.event_cb(WIFI_MODE_STA, EVENT_STA_GOT_IP);

    conn_retry_num = 0;
    wifi_conn_flag = true;

    ESP_LOGI(TAG, "connect to the AP success");
}
