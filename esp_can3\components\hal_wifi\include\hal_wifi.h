#ifndef _HAL_WIFI_H
#define _HAL_WIFI_H

#include "esp_http_server.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_netif.h"
#include "esp_wifi_types_generic.h"
// #include "cJSON.h"

// #if (ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(4, 1, 0))

// #else
// #include "tcpip_adapter.h"
// #endif

#define WIFI_SERVER_IP "***********"
#define WIFI_AP_SSID "FlipClock-AP"
#define WIFI_AP_PASS "12345678"

struct _hal_wifi_scan_msg_t;
struct _hal_wifi_scan_result_t;
struct _hal_wifi_credentials_t;

typedef enum
{
    EVENT_STA_DISCONNECT = 0,
    EVENT_STA_CONNECT,
    EVENT_STA_GOT_IP,
    EVENT_STA_CONNECT_LOST,
    EVENT_AP_JOIN,
    EVENT_AP_LEAVE,
} hal_wifi_event_t;

typedef void (*hal_wifi_event_callback)(wifi_mode_t mode, hal_wifi_event_t event);
typedef void (*hal_wifi_scan_callback)(struct _hal_wifi_scan_msg_t *msg);

typedef struct _hal_wifi_credentials_t
{
    const char *ssid;
    const char *passwd;
    int8_t rssi;
} hal_wifi_credentials_t;

typedef struct _hal_wifi_scan_result_t
{
    uint16_t wifi_cnt;
    bool state;

    wifi_ap_record_t *ap_info;
} hal_wifi_scan_result_t;

typedef struct _hal_wifi_scan_msg_t
{
    enum
    {
        WIFI_SCAN_MSG_START,
        WIFI_SCAN_MSG_STOP,
    } msg_id;

    hal_wifi_scan_result_t *scan_result;

    hal_wifi_scan_callback scan_cb;

    void *user_data;
} hal_wifi_scan_msg_t;

void hal_wifi_init(void);
void hal_wifi_start(void);
void hal_wifi_stop(void);
void hal_wifi_shutdown(void);
void hal_wifi_sta_do_connect(void);
void hal_wifi_sta_do_disconnect(void);
void hal_wifi_scan(hal_wifi_scan_msg_t *msg);

void hal_wifi_power_save(bool en);
void hal_wifi_set_mode(wifi_mode_t _mode);
void hal_wifi_set_credentials(const char *ssid, const char *passwd);
void hal_wifi_conn_event_cb_set(hal_wifi_event_callback cb);

bool hal_wifi_is_scan(void);
wifi_mode_t hal_wifi_get_mode(void);
bool hal_wifi_get_connect_state(void);
char *hal_wifi_get_ip(void);

#endif


