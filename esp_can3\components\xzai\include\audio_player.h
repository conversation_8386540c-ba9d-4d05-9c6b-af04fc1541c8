#ifndef _AUDIO_PLAYER_H
#define _AUDIO_PLAYER_H

typedef struct audio_player *audio_player_t;

typedef void (*audio_player_callback)(audio_player_t player);

typedef struct
{
    int sample_rate;
    int channels;
    int bits;
    int duration_ms;
    int volume;
} audio_player_cfg_t;

audio_player_t audio_player_create(audio_player_cfg_t *cfg);
void audio_player_destroy(audio_player_t player);
void audio_player_pause(audio_player_t player);
void audio_player_run(audio_player_t player);
void audio_player_add_data(audio_player_t player, uint8_t *data, size_t len);
void audio_player_clear_data(audio_player_t player);

void audio_player_set_samplerate(audio_player_t player, int sample_rate);
void audio_player_set_volume(audio_player_t, int volume);
void audio_player_set_callback(audio_player_t player, audio_player_callback cb);
void audio_player_set_user_data(audio_player_t player, void *user_data);

void *audio_player_get_user_data(audio_player_t player);

#endif
