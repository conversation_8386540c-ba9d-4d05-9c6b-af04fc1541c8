#ifndef _XZAI_H
#define _XZAI_H

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"

// 简化版本，注释掉不存在的头文件
// #include "recorder.h"  // 不存在，暂时注释
// #include "player.h"    // 不存在，暂时注释
// #include "xzai_api.h"
// #include "xzai_board_info.h"
// #include "mqtt_protocol.h"
// #include "websocket_protocol.h"
// #include "xzai_config.h"
// #include "xzai_things.h"
// #include "list.h"      // 不存在，暂时注释
// #include "drv_key.h"   // 不存在，暂时注释

#ifdef __cplusplus
extern "C" {
#endif

#define SCHEDULE_EVENT BIT0
#define AUDIO_INPUT_READY_EVENT BIT1
#define AUDIO_OUTPUT_READY_EVENT BIT2
#define AUDIO_CHANNEL_CLOSED_EVENT BIT3

#define XZAI_MSG_ROLE_STATUS "status"
#define XZAI_MSG_ROLE_SYSTEM "system"
#define XZAI_MSG_ROLE_EMOTION "emotion"
#define XZAI_MSG_ROLE_USER "user"
#define XZAI_MSG_ROLE_ASSISTANT "assistant"

#define XZAI_STATUS_NETWORK_DISCONNECT "网络未连接"
#define XZAI_STATUS_NETWORK_ERROR "网络错误"
#define XZAI_STATUS_UNKNOWN "状态未知"
#define XZAI_STATUS_STARTING "启动中..."
#define XZAI_STATUS_IDLE "待命"
#define XZAI_STATUS_CONNECTING "连接中..."
#define XZAI_STATUS_LISTENING "聆听中..."
#define XZAI_STATUS_SPEAKING "说话中..."
#define XZAI_STATUS_ACTIVATING "未激活"

typedef enum
{
    XZAI_STATE_UNKNOWN,
    XZAI_STATE_STARTING,
    XZAI_STATE_IDLE,
    XZAI_STATE_CONNECTING,
    XZAI_STATE_LISTENING,
    XZAI_STATE_SPEAKING,
    XZAI_STATE_ACTIVATING,
    XZAI_STATE_FATAL_ERROR,
    XZAI_STATE_VOICE_STOP,
    XZAI_STATE_VOICE_RUN,
} xzai_state_t;

typedef enum
{
    XZAI_NOTIF_AI_INIT_BEGIN,
    XZAI_NOTIF_AI_INIT_OK,
    XZAI_NOTIF_AI_INIT_FAIL,
    XZAI_NOTIF_START_VOICE_BEGIN,
    XZAI_NOTIF_START_VOICE_END,
    XZAI_NOTIF_STOP_VOICE_BEGIN,
    XZAI_NOTIF_STOP_VOICE_END,
    XZAI_NOTIF_WAKE_UP,
    XZAI_NOTIF_TEXT_UPDATE,
    XZAI_NOTIF_STATE_UPDATE,
    XZAI_NOTIF_AUDIO_CLOSED,
    XZAI_NOTIF_THINGS_SPEAK_VOLUME_STORAGE,
    XZAI_NOTIF_THINGS_BKL_STORAGE,
    XZAI_NOTIF_THINGS_BKL_TURN_ON,
    XZAI_NOTIF_THINGS_BKL_TURN_OFF,
    XZAI_NOTIF_THINGS_BT_TURN_ON,
} xzai_notif_msg_t;

typedef struct xzai_t *xzai_handle_t;

typedef void (*xzai_check_ver_callback)(xzai_handle_t xzai);
typedef void (*xzai_mqttt_cfg_update_callback)(xzai_handle_t xzai);
typedef void(*xzai_key_callback)(xzai_handle_t xzai, bool state);
typedef void (*xzai_notif_callback)(xzai_handle_t xzai, xzai_notif_msg_t msg);
typedef void (*xzai_task_exec)(xzai_handle_t xzai);

typedef struct
{
    const char *role;
    char *content;
} xzai_text_data_t;

typedef struct
{
    xzai_check_ver_callback check_ver;
    xzai_mqttt_cfg_update_callback mqtt_cfg_update;
    xzai_key_callback key_update;
    xzai_notif_callback notif;
} xzai_callbacks_t;

typedef struct
{
    bool has_mqtt_config;
    char endpoint[128];
    char client_id[128];
    char username[128];
    char password[128];
    char publish_topic[128];
    char subscribe_topic[128];
} xzai_mqtt_t;

typedef struct
{
    bool has_activation_code;
    char code[32];
    char message[128];
} xzai_activation_t;

typedef struct
{
    void *recorder; // 简化类型定义
    // mqtt_protocol_cfg_t mqtt_setting_cfg;
    // xzai_callbacks_t callbacks;
    const char *voice_model_path;
    int speak_volume;
} xzai_cfg_t;

xzai_handle_t xzai_create(xzai_cfg_t *cfg);
void xzai_destroy(xzai_handle_t xzai);
void xzai_schedule(xzai_handle_t xzai, xzai_task_exec exec);
bool xzai_stop_chat(xzai_handle_t xzai);
void xzai_start_listening(xzai_handle_t xzai);
void xzai_stop_listening(xzai_handle_t xzai);
void xzai_input_audio(xzai_handle_t xzai);
void xzai_output_audio(xzai_handle_t xzai);
void xzai_text_list_clear(xzai_handle_t xzai);
void xzai_update_iot_states(xzai_handle_t xzai);
void xzai_alert_msg(xzai_handle_t xzai, const char *status, const char *message, const char *emotion);

void xzai_set_check_ver_callback(xzai_handle_t xzai, xzai_check_ver_callback cb);
void xzai_set_cfg_update_callback(xzai_handle_t xzai, xzai_mqttt_cfg_update_callback cb);
void xzai_set_notif_callback(xzai_handle_t xzai, xzai_notif_callback cb);
void xzai_set_state(xzai_handle_t xzai, xzai_state_t state);
void xzai_set_event_bit(xzai_handle_t xzai, EventBits_t bit);
void xzai_set_speak_volume(xzai_handle_t xzai, int volume);

xzai_mqtt_t *xzai_get_mqtt_cfg(xzai_handle_t xzai);
xzai_activation_t *xzai_get_activation(xzai_handle_t xzai);
char *xzai_get_boardinfo(xzai_handle_t xzai);
xzai_callbacks_t *xzai_get_callbacks(xzai_handle_t xzai);
xzai_state_t xzai_get_state(xzai_handle_t xzai);
const char *xzai_get_status_desc(xzai_handle_t xzai);
EventGroupHandle_t xzai_get_event_group(xzai_handle_t xzai);
xzai_text_data_t *xzai_get_one_text(xzai_handle_t xzai);
QueueHandle_t xzai_get_task_queue(xzai_handle_t xzai);
bool xzai_is_running(xzai_handle_t xzai);

extern xzai_handle_t xzai;

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
