#include "thing.h"
#include "board.h"
#include "drv_battery.h"

#include <esp_log.h>

#define TAG "Battery"

namespace iot {

// 这里仅定义 Battery 的属性和方法，不包含具体的实现
class Battery : public Thing {
private:
    int level_ = 0;
    bool charging_ = false;
    bool discharging_ = false;

public:
    Battery() : Thing("Battery", "电池管理") {
        // 定义设备的属性
        properties_.AddNumberProperty("level", "当前电量百分比", [this]() -> int {
            level_ = battery_info.pct;
            charging_ = battery_info.state_chrg == BATTERY_STATE_CHRG;
            return level_;
        });
        properties_.AddBooleanProperty("charging", "是否充电中", [this]() -> bool {
            return charging_;
        });
        methods_.AddMethod("GetPercentage", "电量百分比", ParameterList(), [this](const ParameterList& parameters) {
            level_ = battery_info.pct;
            return level_;
        });
    }
};

} // namespace iot

DECLARE_THING(Battery);

