#include "thing.h"
#include "board.h"
#include "lcd_i80.h"
#include "xzai.h"

#include <esp_log.h>

#define TAG "Backlight"

namespace iot {

// 这里仅定义 Backlight 的属性和方法，不包含具体的实现
class Backlight : public Thing {
public:
    Backlight() : Thing("Backlight", "屏幕背光") {
        // 定义设备的属性
        properties_.AddNumberProperty("brightness", "当前亮度值", [this]() -> int {
            // 这里可以添加获取当前亮度的逻辑
            return lcd_i80_get_backlight(&lcd);
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("SetBrightness", "设置亮度", ParameterList({
            Parameter("brightness", "10到100之间的整数", kValueTypeNumber, true)
        }), [this](const ParameterList& parameters) {
            uint8_t brightness = static_cast<uint8_t>(parameters["brightness"].number());

            lcd_i80_backlight_set(&lcd, brightness);

            xzai_callbacks_t *xzai_cbs = xzai_get_callbacks(xzai);
            if(xzai_cbs->notif)
                xzai_cbs->notif(xzai, XZAI_NOTIF_THINGS_BKL_STORAGE);
        });

        methods_.AddMethod("TurnOn", "打开背光", ParameterList(), [this](const ParameterList& parameters) {
            xzai_callbacks_t *xzai_cbs = xzai_get_callbacks(xzai);
            if(xzai_cbs->notif)
                xzai_cbs->notif(xzai, XZAI_NOTIF_THINGS_BKL_TURN_ON);
        });

        methods_.AddMethod("TurnOff", "关闭背光", ParameterList(), [this](const ParameterList& parameters) {
            xzai_callbacks_t *xzai_cbs = xzai_get_callbacks(xzai);
            if(xzai_cbs->notif)
                xzai_cbs->notif(xzai, XZAI_NOTIF_THINGS_BKL_TURN_OFF);
        });

    }
};

} // namespace iot

DECLARE_THING(Backlight);
