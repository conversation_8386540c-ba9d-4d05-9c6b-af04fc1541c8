#include "thing.h"
#include "board.h"
#include "xzai.h"
#include "drv_bt.h"

#include <esp_log.h>

#define TAG "Bluetooth"

namespace iot {

// 这里仅定义 Bluetooth 的属性和方法，不包含具体的实现
class Bluetooth : public Thing {
public:
    Bluetooth() : Thing("Bluetooth", "蓝牙模块") {
        // 定义设备的属性
        properties_.AddBooleanProperty("status", "蓝牙模块是否打开", [this]() -> bool {
            return drv_bt_isenable();
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("TurnOn", "打开蓝牙", ParameterList(), [this](const ParameterList& parameters) {
            xzai_callbacks_t *xzai_cbs = xzai_get_callbacks(xzai);
            if(xzai_cbs->notif)
                xzai_cbs->notif(xzai, XZAI_NOTIF_THINGS_BT_TURN_ON);
        });

    }
};

} // namespace iot

DECLARE_THING(Bluetooth);
