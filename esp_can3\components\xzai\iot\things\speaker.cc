#include "thing.h"
#include "board.h"
#include "player.h"
#include "xzai.h"

#include <esp_log.h>

#define TAG "Speaker"

namespace iot {

// 这里仅定义 Speaker 的属性和方法，不包含具体的实现
class Speaker : public Thing {
public:
    Speaker() : Thing("Speaker", "扬声器") {
        // 定义设备的属性
        properties_.AddNumberProperty("volume", "当前音量值", [this]() -> int {
            return player_get_volume_direct(player);
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("SetVolume", "设置音量", ParameterList({
            Parameter("volume", "0到100之间的整数", kValueTypeNumber, true)
        }), [this](const ParameterList& parameters) {
            // auto codec = Board::GetInstance().GetAudioCodec();
            // codec->SetOutputVolume(static_cast<uint8_t>(parameters["volume"].number()));

            uint8_t vol = static_cast<uint8_t>(parameters["volume"].number());
            player_set_volume(player, vol);

            if(xzai)
                xzai_set_speak_volume(xzai, player_get_volume(player));

            xzai_callbacks_t *xzai_cbs = xzai_get_callbacks(xzai);
            if(xzai_cbs->notif)
                xzai_cbs->notif(xzai, XZAI_NOTIF_THINGS_SPEAK_VOLUME_STORAGE); //存储配置

            ESP_LOGI(TAG, "set speaker volume to %d", vol);
        });
    }
};

} // namespace iot

DECLARE_THING(Speaker);
