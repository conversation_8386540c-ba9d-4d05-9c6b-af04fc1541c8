#ifndef _MQTT_PROTOCOL_H
#define _MQTT_PROTOCOL_H

#include "protocol.h"
#include "mqtt_client.h"
#include "udp_client.h"
#include <mbedtls/aes.h>

#define MQTT_CONNECT_TIMEOUT_MS 10000

#define MQTT_PROTOCOL_VER MQTT_PROTOCOL_V_3_1_1
#define MQTT_ADDRESS_PORT 8883
#define MQTT_KEEPALIVE_TIME 90

#define MQTT_INITIALIZED_EVENT BIT0
#define MQTT_CONNECTED_EVENT BIT1
#define MQTT_DISCONNECTED_EVENT BIT2
#define MQTT_ERROR_EVENT BIT3
#define MQTT_SERVER_HELLO_EVENT BIT4

struct mqtt_protocol;

typedef struct
{
    const char *endpoint;
    const char *client_id;
    const char *username;
    const char *password;
    const char *publish_topic;
    const char *subscribe_topic;
} mqtt_protocol_cfg_t;

typedef struct mqtt_protocol
{
    protocol_base_t base;

    EventGroupHandle_t event_group_handle;

    esp_mqtt_client_handle_t client;
    udp_client_handle_t udp;

    mqtt_protocol_cfg_t mqtt_cfg;
    char *mqtt_msg_buff;
    int mqtt_msg_len;

    char udp_host[32];
    int udp_port;

    mbedtls_aes_context aes_ctx;
    char aes_nonce[32];
    int aes_nonce_size;

    uint32_t local_sequence;
    uint32_t remote_sequence;

    bool connected;

} mqtt_protocol_t;

mqtt_protocol_t *mqtt_protocol_create(mqtt_protocol_cfg_t *cfg);
void mqtt_protocol_destroy(struct mqtt_protocol *protocol);

#endif
