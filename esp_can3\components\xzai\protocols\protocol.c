#include <stdio.h>
#include "protocol.h"
#include "esp_log.h"

#define TAG "protocol"

static void _send_abort_speaking(struct protocol_base *base, abort_reason_t reason)
{
    char message[512];

    sprintf(message,
            "{\"session_id\":\"%s\",\"type\":\"abort\"%s}",
            base->session_id,
            (reason == ABORT_REASON_WAKEWORD_DETECTED) ? ",\"reason\":\"wake_word_detected\"" : "");

    if (base->send_text)
    {
        base->send_text(base, message);
    }
}

static void _send_wakeword_detected(struct protocol_base *base, const char *word)
{
    char json[512];

    sprintf(json,
            "{\"session_id\":\"%s\",\"type\":\"listen\",\"state\":\"detect\",\"text\":\"%s\"}",
            base->session_id,
            word);

    if (base->send_text)
    {
        base->send_text(base, json);
    }
}

static void _send_start_listening(struct protocol_base *base, listening_mode_t mode)
{
    char message[512];

    sprintf(message,
            "{\"session_id\":\"%s\",\"type\":\"listen\",\"state\":\"start\"%s}",
            base->session_id,
            (mode == LISTENING_MODE_ALWAYS_ON) ? ",\"mode\":\"realtime\"" : ((mode == LISTENING_MODE_AUTOSTOP) ? ",\"mode\":\"auto\"" : ",\"mode\":\"manual\""));

    if (base->send_text)
    {
        base->send_text(base, message);
    }
}

static void _send_stop_listening(struct protocol_base *base)
{
    char message[512];

    sprintf(message, "{\"session_id\":\"%s\",\"type\":\"listen\",\"state\":\"stop\"}", base->session_id);

    if (base->send_text)
    {
        base->send_text(base, message);
    }
}

static void _set_error(struct protocol_base *base, const char *msg)
{
    base->error_occurred = true;
    if (base->network_error_cb)
    {
        base->network_error_cb(base->user_data, msg);
    }
}

static void _send_iot_descriptors(struct protocol_base *base, const char *descriptors)
{
    cJSON *root = cJSON_Parse(descriptors);
    if (root == NULL)
    {
        ESP_LOGE(TAG, "Failed to parse IoT descriptors: %s", descriptors);
        return;
    }

    if (!cJSON_IsArray(root))
    {
        ESP_LOGE(TAG, "IoT descriptors should be an array");
        cJSON_Delete(root);
        return;
    }

    int arraySize = cJSON_GetArraySize(root);
    for (int i = 0; i < arraySize; ++i)
    {
        cJSON *descriptor = cJSON_GetArrayItem(root, i);
        if (descriptor == NULL)
        {
            ESP_LOGE(TAG, "Failed to get IoT descriptor at index %d", i);
            continue;
        }

        cJSON *messageRoot = cJSON_CreateObject();
        cJSON_AddStringToObject(messageRoot, "session_id", (const char*)base->session_id);
        cJSON_AddStringToObject(messageRoot, "type", "iot");
        cJSON_AddBoolToObject(messageRoot, "update", true);

        cJSON *descriptorArray = cJSON_CreateArray();
        cJSON_AddItemToArray(descriptorArray, cJSON_Duplicate(descriptor, 1));
        cJSON_AddItemToObject(messageRoot, "descriptors", descriptorArray);

        char *message = cJSON_PrintUnformatted(messageRoot);
        if (message == NULL)
        {
            ESP_LOGE(TAG, "Failed to print JSON message for IoT descriptor at index %d", i);
            cJSON_Delete(messageRoot);
            continue;
        }

        if (base->send_text)
        {
            base->send_text(base, message);
        }

        cJSON_free(message);
        cJSON_Delete(messageRoot);
    }

    cJSON_Delete(root);
}

static void _send_iot_states(struct protocol_base *base, const char *states)
{
    char message[1024];
    sprintf(message, "{\"session_id\":\"%s\",\"type\":\"iot\",\"update\":true,\"states\":%s}", base->session_id, states);

    if (base->send_text)
    {
        base->send_text(base, message);
    }
}

static bool _is_timeout(struct protocol_base *base)
{
    const int timeout_seconds = 120;
    time_t now = time(NULL);

    double seconds_diff = difftime(now, base->last_incoming_time);

    bool timeout = seconds_diff > timeout_seconds;
    if(timeout)
    {
        ESP_LOGE(TAG, "Channel timeout %lld seconds", seconds_diff);
    }

    return timeout;
}

void protocol_base_init(struct protocol_base *base)
{
    base->send_start_listening = _send_start_listening;
    base->send_stop_listening = _send_stop_listening;
    base->send_wakeword_detected = _send_wakeword_detected;
    base->send_abort_speaking = _send_abort_speaking;
    base->set_error = _set_error;

    base->send_iot_descriptors = _send_iot_descriptors;
    base->send_iot_states = _send_iot_states;

    base->is_timeout = _is_timeout;
}

void protocol_start(struct protocol_base *base)
{
    if (base->start)
    {
        base->start(base);
    }
}

bool protocol_open_audio_channel(struct protocol_base *base)
{
    if (base->open_audio_channel)
    {
        return base->open_audio_channel(base);
    }

    return false;
}

void protocol_close_audio_channel(struct protocol_base *base)
{
    if (base->close_audio_channel)
    {
        base->close_audio_channel(base);
    }
}

bool protocol_is_audio_channel_opened(struct protocol_base *base)
{
    if (base->is_audio_channel_opened)
    {
        return base->is_audio_channel_opened(base);
    }

    return false;
}

void protocol_send_audio(struct protocol_base *base, audio_stream_packet_t *packet)
{
    if (base->send_audio)
    {
        base->send_audio(base, packet);
    }
}

void protocol_set_user_data(struct protocol_base *base, void *user_data)
{
    base->user_data = user_data;
}
