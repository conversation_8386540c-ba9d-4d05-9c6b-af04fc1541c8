#ifndef _PROTOCOL_H
#define _PROTOCOL_H

/**
 * 基类通信协议文件
 * 使用结构体和函数指针实现继承
 */

#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include "cJSON.h"

#define OPUS_FRAME_DURATION_MS 60
#define PROTOCOL_VERSION 1

typedef struct{
    uint32_t timestamp;
    uint8_t *payload;
    uint32_t size;
}audio_stream_packet_t;

struct protocol_base;

typedef void (*protocol_incoming_audio_cb)(void *user_data, audio_stream_packet_t *packet);
typedef void (*protocol_incoming_json_cb)(void *user_data, const cJSON *root);
typedef void (*protocol_audio_channel_opened_cb)(void *user_data);
typedef void (*protocol_audio_channel_closed_cb)(void *user_data);
typedef void (*protocol_network_error_cb)(void *user_data, const char *msg);

typedef enum
{
    ABORT_REASON_NONE,
    ABORT_REASON_WAKEWORD_DETECTED,
} abort_reason_t;

typedef enum
{
    LISTENING_MODE_AUTOSTOP,
    LISTENING_MODE_MANUALSTOP,
    LISTENING_MODE_ALWAYS_ON, // 需要 AEC 支持
} listening_mode_t;

typedef struct protocol_base
{
    int server_sample_rate;
    char session_id[64];

    bool error_occurred;

    time_t last_incoming_time;

    // 基类回调函数
    protocol_incoming_audio_cb incoming_audio_cb;
    protocol_incoming_json_cb incoming_json_cb;
    protocol_audio_channel_opened_cb audio_channel_opened_cb;
    protocol_audio_channel_closed_cb audio_channel_closed_cb;
    protocol_network_error_cb network_error_cb;

    // 基类函数
    void (*send_start_listening)(struct protocol_base *base, listening_mode_t mode);
    void (*send_stop_listening)(struct protocol_base *base);
    void (*send_abort_speaking)(struct protocol_base *base, abort_reason_t reason);
    void (*send_wakeword_detected)(struct protocol_base *base, const char *word);
    void (*send_text)(struct protocol_base *base, const char *text);
    void (*set_error)(struct protocol_base *base, const char *msg);
    void (*send_iot_descriptors)(struct protocol_base *base, const char *descriptors);
    void (*send_iot_states)(struct protocol_base *base, const char *states);
    bool (*is_timeout)(struct protocol_base *base);

    // 子类需要实现的函数
    void (*start)(struct protocol_base *base);
    bool (*open_audio_channel)(struct protocol_base *base);
    void (*close_audio_channel)(struct protocol_base *base);
    bool (*is_audio_channel_opened)(struct protocol_base *base);
    void (*send_audio)(struct protocol_base *base, audio_stream_packet_t *packet);

    void *user_data;
} protocol_base_t;

void protocol_base_init(struct protocol_base *base);

void protocol_start(struct protocol_base *base);
bool protocol_open_audio_channel(struct protocol_base *base);
void protocol_close_audio_channel(struct protocol_base *base);
bool protocol_is_audio_channel_opened(struct protocol_base *base);
void protocol_send_audio(struct protocol_base *base, audio_stream_packet_t *packet);
void protocol_set_user_data(struct protocol_base *base, void *user_data);

#endif
