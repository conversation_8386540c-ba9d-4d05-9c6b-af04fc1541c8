#ifndef _UDP_CLIENT_H
#define _UDP_CLIENT_H

#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>

typedef struct udp_client_t *udp_client_handle_t;

typedef void(*udp_mesage_callback)(void *arg, const char* data, size_t len);

udp_client_handle_t udp_client_create();
void udp_client_destroy(udp_client_handle_t handle);
bool udp_client_connect(udp_client_handle_t handle, const char* host, int port);
void udp_client_disconnect(udp_client_handle_t handle);
int udp_client_send(udp_client_handle_t handle, const void* data, int len);

void udp_client_set_msg_callback(udp_client_handle_t handle, udp_mesage_callback cb);
void udp_client_set_user_data(udp_client_handle_t handle, void *user_data);

#endif

