#include "websocket_client.h"
#include "web_socket.h"
#include "tcp_transport.h"
#include "tls_transport.h"
#include "esp_log.h"

#define TAG "ws_client"

struct ws_client
{
    WebSocket *ws;
};

ws_client_t ws_client_create(const char *ws_url)
{
    ws_client_t client = new struct ws_client;
    std::string url(ws_url);

    if (url.find("wss://") == 0)
    {
        client->ws = new WebSocket(new TlsTransport());
    }
    else
    {
        client->ws = new WebSocket(new TcpTransport());
    }

    ESP_LOGI(TAG, "ws client create");
    return client;
}

void ws_client_destroy(ws_client_t client)
{    
    delete client->ws;
    client->ws = NULL;

    delete client;
    client = NULL;
    ESP_LOGI(TAG, "ws client destroy");
}

bool ws_client_connect(ws_client_t client, const char *url)
{
    return client->ws->Connect(url);
}

void ws_client_set_user_data(ws_client_t client, void *user_data)
{
    client->ws->SetUserData(user_data);
}

void ws_client_set_header(ws_client_t client, const char *key, const char *value)
{
    client->ws->SetHeader(key, value);
}

bool ws_client_send_string(ws_client_t client, const char *str)
{
    std::string message(str);
    return client->ws->Send(message);
}

bool ws_client_send_data(ws_client_t client, const void *data, size_t len)
{
    if (client->ws == nullptr)
        return false;

    return client->ws->Send(data, len, true);
}

bool ws_client_is_connected(ws_client_t client)
{
    return client->ws->IsConnected();
}

void ws_client_register_onconnected(ws_client_t client, ws_client_on_connected_cb cb)
{
    std::function<void(void *)> on_connected = cb;
    client->ws->OnConnected(on_connected);
}

void ws_client_register_ondisconnected(ws_client_t client, ws_client_on_disconnected_cb cb)
{
    std::function<void(void *)> on_disconnected = cb;
    client->ws->OnDisconnected(on_disconnected);
}

void ws_client_register_ondata(ws_client_t client, ws_client_on_data_cb cb)
{
    std::function<void(void *, const char *, size_t, bool binary)> on_data = cb;
    client->ws->OnData(on_data);
}

void ws_client_register_onerror(ws_client_t client, ws_client_on_error_cb cb)
{
    std::function<void(void *, int)> on_error = cb;
    client->ws->OnError(on_error);
}
