#ifndef _WEBSOCKET_CLIENT_H
#define _WEBSOCKET_CLIENT_H

#include <stdint.h>
#include <stdlib.h>

#ifdef __cplusplus
extern "C"
{
#endif

    typedef struct ws_client *ws_client_t;

    typedef void (*ws_client_on_connected_cb)(void *);
    typedef void (*ws_client_on_disconnected_cb)(void *);
    typedef void (*ws_client_on_data_cb)(void *, const char *, size_t, bool binary);
    typedef void (*ws_client_on_error_cb)(void *, int);

    ws_client_t ws_client_create(const char *ws_url);
    void ws_client_destroy(ws_client_t client);
    bool ws_client_connect(ws_client_t client, const char *url);
    bool ws_client_send_string(ws_client_t client, const char *str);
    bool ws_client_send_data(ws_client_t client, const void *data, size_t len);
    void ws_client_register_onconnected(ws_client_t client, ws_client_on_connected_cb cb);
    void ws_client_register_ondisconnected(ws_client_t client, ws_client_on_disconnected_cb cb);
    void ws_client_register_ondata(ws_client_t client, ws_client_on_data_cb cb);
    void ws_client_register_onerror(ws_client_t client, ws_client_on_error_cb cb);

    void ws_client_set_user_data(ws_client_t client, void *user_data);
    void ws_client_set_header(ws_client_t client, const char *key, const char *value);

    bool ws_client_is_connected(ws_client_t client);

#ifdef __cplusplus
}
#endif

#endif
