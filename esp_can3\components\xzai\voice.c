#include "voice.h"

#include "freertos/FreeRTOS.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/task.h"

#include "esp_log.h"

#include "amrnb_encoder.h"
#include "amrwb_encoder.h"
#include "audio_element.h"
#include "audio_mem.h"
#include "audio_pipeline.h"
#include "audio_thread.h"
#include "audio_recorder.h"
#include "board.h"
#include "esp_audio.h"
#include "fatfs_stream.h"
#include "filter_resample.h"
#include "i2s_stream.h"
#include "mp3_decoder.h"
#include "pcm_decoder.h"
#include "raw_stream.h"
#include "recorder_encoder.h"
#include "recorder_sr.h"

#include "model_path.h"
#include "esp_mn_speech_commands.h"

#include "res_binary.h"
#include "drv_beep.h"

#define TAG "voice"

#define NO_ENCODER (0)
#define ENC_2_AMRNB (1)
#define ENC_2_AMRWB (2)

#define RECORDER_ENC_ENABLE (NO_ENCODER)
#define WAKENET_ENABLE (true)
#define MULTINET_ENABLE (true)
#define SPEECH_CMDS_RESET (false)

enum _rec_msg_id
{
    REC_START = 1,
    REC_STOP,
    REC_CANCEL,
};

struct voice_t
{
    audio_pipeline_handle_t pipeline;
    audio_rec_handle_t recorder;
    recorder_sr_handle_t recorder_sr;

    audio_element_handle_t pcm_decoder;
    audio_element_handle_t raw_stream;

    recorder_handle_t raw_read;

    int sample_rate;
    int channels;
    int rec_duration;

    const char *model_path;

    bool rec_reading;
    int16_t *rec_buff;
    int rec_buff_size;
    int rec_read_size;

    voice_wakeup_cb wakeup_cb;
    voice_rec_ready_cb rec_ready_cb;

    void *user_data;
};

static int pcm_read_cb(audio_element_handle_t el, char *buf, int len, TickType_t wait_time, void *ctx)
{
    int read_size = len;
    voice_handle_t voice = (voice_handle_t)ctx;

    if (voice == NULL || voice->raw_read == NULL || voice->recorder == NULL)
        return read_size;

    do
    {
        read_size = recorder_read_data(voice->raw_read, buf, len);
        vTaskDelay(1);
    } while (read_size == 0);

    if (read_size == 0)
    {
        return AEL_IO_DONE;
    }
    else
    {
        read_size = len;
    }

    return read_size;
}

static int input_cb_for_afe(int16_t *buffer, int buf_sz, void *user_ctx, TickType_t ticks)
{
    int read_len = 0;
    voice_handle_t voice = (voice_handle_t)user_ctx;

    if (voice)
    {
        read_len = raw_stream_read(voice->raw_stream, (char *)buffer, buf_sz);

        if (voice->rec_buff && voice->rec_reading)
        {
            if (read_len > 0 && voice->rec_read_size < voice->rec_buff_size)
            {
                int mem_len = (voice->rec_read_size + (read_len / sizeof(int16_t))) > voice->rec_buff_size ? (voice->rec_buff_size - voice->rec_read_size) * sizeof(int16_t) : read_len;
                memcpy(voice->rec_buff + voice->rec_read_size, buffer, mem_len);
                voice->rec_read_size += (mem_len / sizeof(int16_t));

                if (voice->rec_read_size == voice->rec_buff_size && voice->rec_ready_cb)
                {
                    ESP_LOGD(TAG, "voice buff full");
                    voice->rec_ready_cb(voice);
                }
            }
        }
    }
    else
    {
        read_len = buf_sz;
    }

    return read_len;
}

static esp_err_t rec_engine_cb(audio_rec_evt_t *event, void *user_data)
{
    voice_handle_t voice = (voice_handle_t)user_data;

    if (AUDIO_REC_WAKEUP_START == event->type)
    {
        recorder_sr_wakeup_result_t *wakeup_result = event->event_data;

        ESP_LOGI(TAG, "rec_engine_cb - REC_EVENT_WAKEUP_START");
        ESP_LOGI(TAG, "wakeup: vol %f, mod idx %d, word idx %d", wakeup_result->data_volume, wakeup_result->wakenet_model_index, wakeup_result->wake_word_index);
        // 唤醒提示音
        drv_beep_msg_t beep_msg = {
            .count = 1,
            .delay = 50,
        };
        drv_beep_send(&beep_msg);

        if (voice->wakeup_cb)
        {
            voice->wakeup_cb(voice);
        }

    }
    else if (AUDIO_REC_VAD_START == event->type)
    {
        ESP_LOGI(TAG, "rec_engine_cb - REC_EVENT_VAD_START");
    }
    else if (AUDIO_REC_VAD_END == event->type)
    {
        ESP_LOGI(TAG, "rec_engine_cb - REC_EVENT_VAD_STOP");
    }
    else if (AUDIO_REC_WAKEUP_END == event->type)
    {
        ESP_LOGI(TAG, "rec_engine_cb - REC_EVENT_WAKEUP_END");
        AUDIO_MEM_SHOW(TAG);
    }
    else
    {
        ESP_LOGE(TAG, "Unkown event");
    }
    return ESP_OK;
}

esp_err_t voice_recorder_start(voice_handle_t voice)
{
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    voice->pipeline = audio_pipeline_init(&pipeline_cfg);
    if (NULL == voice->pipeline)
    {
        return ESP_FAIL;
    }

    pcm_decoder_cfg_t pcm_dec_cfg = DEFAULT_PCM_DECODER_CONFIG();
    pcm_dec_cfg.rate = voice->sample_rate;
    pcm_dec_cfg.channels = voice->channels;
    pcm_dec_cfg.task_core = 1;
    voice->pcm_decoder = pcm_decoder_init(&pcm_dec_cfg);

    raw_stream_cfg_t raw_cfg = RAW_STREAM_CFG_DEFAULT();
    raw_cfg.type = AUDIO_STREAM_READER;
    voice->raw_stream = raw_stream_init(&raw_cfg);

    audio_pipeline_register(voice->pipeline, voice->pcm_decoder, "pcm");
    audio_pipeline_register(voice->pipeline, voice->raw_stream, "raw");

    audio_element_set_read_cb(voice->pcm_decoder, pcm_read_cb, voice);

    const char *link_tag[3] = {"pcm", "raw"};
    audio_pipeline_link(voice->pipeline, &link_tag[0], 2);

    audio_pipeline_run(voice->pipeline);

    recorder_sr_cfg_t recorder_sr_cfg = DEFAULT_RECORDER_SR_CFG("RM", voice->model_path, AFE_TYPE_SR, AFE_MODE_LOW_COST);
    recorder_sr_cfg.afe_cfg->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    recorder_sr_cfg.afe_cfg->wakenet_init = WAKENET_ENABLE;
    recorder_sr_cfg.afe_cfg->vad_mode = VAD_MODE_4;
    recorder_sr_cfg.multinet_init = MULTINET_ENABLE;
#if !defined(CONFIG_SR_MN_CN_NONE)
    recorder_sr_cfg.mn_language = ESP_MN_CHINESE;
#elif !defined(CONFIG_SR_MN_EN_NONE)
    recorder_sr_cfg.mn_language = ESP_MN_ENGLISH;
#else
    recorder_sr_cfg.mn_language = "";
#endif
    recorder_sr_cfg.afe_cfg->aec_init = false;
    recorder_sr_cfg.afe_cfg->agc_mode = AFE_MN_PEAK_NO_AGC;

    recorder_sr_cfg.afe_cfg->pcm_config.mic_num = 1;
    recorder_sr_cfg.afe_cfg->pcm_config.ref_num = 0;
    recorder_sr_cfg.afe_cfg->pcm_config.total_ch_num = 1;
    recorder_sr_cfg.afe_cfg->wakenet_mode = DET_MODE_90;

    recorder_sr_cfg.partition_label = voice->model_path;

#if RECORDER_ENC_ENABLE
    recorder_encoder_cfg_t recorder_encoder_cfg = {0};
#if RECORDER_ENC_ENABLE == ENC_2_AMRNB
    rsp_filter_cfg_t filter_cfg = DEFAULT_RESAMPLE_FILTER_CONFIG();
    filter_cfg.src_ch = 1;
    filter_cfg.src_rate = 16000;
    filter_cfg.dest_ch = 1;
    filter_cfg.dest_rate = 8000;
    filter_cfg.stack_in_ext = true;
    filter_cfg.max_indata_bytes = 1024;

    amrnb_encoder_cfg_t amrnb_cfg = DEFAULT_AMRNB_ENCODER_CONFIG();
    amrnb_cfg.contain_amrnb_header = true;
    amrnb_cfg.stack_in_ext = true;

    recorder_encoder_cfg.resample = rsp_filter_init(&filter_cfg);
    recorder_encoder_cfg.encoder = amrnb_encoder_init(&amrnb_cfg);
#elif RECORDER_ENC_ENABLE == ENC_2_AMRWB
    amrwb_encoder_cfg_t amrwb_cfg = DEFAULT_AMRWB_ENCODER_CONFIG();
    amrwb_cfg.contain_amrwb_header = true;
    amrwb_cfg.stack_in_ext = true;
    amrwb_cfg.out_rb_size = 4 * 1024;

    recorder_encoder_cfg.encoder = amrwb_encoder_init(&amrwb_cfg);
#endif
#endif

    audio_rec_cfg_t cfg = AUDIO_RECORDER_DEFAULT_CFG();
    cfg.task_prio = 5;
    cfg.read = (recorder_data_read_t)&input_cb_for_afe;
    cfg.user_data = voice;
    cfg.sr_handle = recorder_sr_create(&recorder_sr_cfg, &cfg.sr_iface);
#if SPEECH_CMDS_RESET
    char err[200];
    recorder_sr_reset_speech_cmd(cfg.sr_handle, SPEECH_COMMANDS, err);
#endif
#if RECORDER_ENC_ENABLE
    cfg.encoder_handle = recorder_encoder_create(&recorder_encoder_cfg, &cfg.encoder_iface);
#endif
    cfg.event_cb = rec_engine_cb;
    cfg.vad_off = 1000;
    voice->recorder = audio_recorder_create(&cfg);

    cfg.sr_iface->base.set_read_cb(cfg.sr_handle, cfg.read, cfg.user_data);
    voice->recorder_sr = cfg.sr_handle;

    ESP_LOGI(TAG, "voice recorder start");

    return ESP_OK;
}

void voice_recorder_stop(voice_handle_t voice)
{
    voice_input_audio_stop(voice);

    if (voice->recorder)
    {
        audio_recorder_destroy(voice->recorder);
        voice->recorder = NULL;
    }
    if (voice->recorder_sr)
    {
        recorder_sr_destroy(voice->recorder_sr);
        voice->recorder_sr = NULL;
    }
    if (voice->pipeline)
    {
        audio_pipeline_stop(voice->pipeline);
        audio_pipeline_wait_for_stop(voice->pipeline);
        audio_pipeline_terminate(voice->pipeline);

        audio_pipeline_deinit(voice->pipeline);

        voice->pipeline = NULL;
        voice->pcm_decoder = NULL;
        voice->raw_stream = NULL;
    }

    ESP_LOGI(TAG, "voice recorder stop");
}

voice_handle_t voice_create(voice_cfg_t *cfg)
{
    voice_handle_t voice = (voice_handle_t)audio_malloc(sizeof(struct voice_t));

    if (voice == NULL)
        return NULL;

    voice->raw_read = cfg->recorder_handle;
    voice->sample_rate = cfg->sample_rate;
    voice->channels = cfg->channels;
    voice->rec_duration = cfg->rec_duration;
    voice->model_path = cfg->model;

    voice->rec_read_size = 0;
    voice->rec_buff_size = cfg->sample_rate / 1000 * cfg->rec_duration * cfg->channels;
    voice->rec_buff = (int16_t *)audio_malloc(sizeof(int16_t) * voice->rec_buff_size);
    ESP_LOGI(TAG, "rec sample rate: %d, duration: %d", voice->sample_rate, voice->rec_duration);

    if (voice->rec_buff == NULL)
    {
        voice_destroy(voice);
        return NULL;
    }

    if (voice_recorder_start(voice) == ESP_FAIL)
    {
        voice_destroy(voice);
        return NULL;
    }

    return voice;
}

void voice_destroy(voice_handle_t voice)
{
    if (voice)
    {
        voice_recorder_stop(voice);

        if (voice->rec_buff)
        {
            audio_free(voice->rec_buff);
            voice->rec_buff = NULL;
        }

        audio_free(voice);
        voice = NULL;
    }
}

void voice_input_audio_start(voice_handle_t voice)
{
    voice->rec_read_size = 0;
    voice->rec_reading = true;
}

void voice_input_audio_stop(voice_handle_t voice)
{
    voice->rec_read_size = 0;
    voice->rec_reading = false;
}

bool voice_read_audio_data(voice_handle_t voice, int16_t **buff, int *size)
{
    if (voice->rec_buff_size == 0)
        return false;

    int buff_size = sizeof(int16_t) * voice->rec_buff_size;

    if (*buff == NULL)
        *buff = (int16_t *)audio_calloc(1, buff_size);

    if (*buff)
    {
        memcpy(*buff, voice->rec_buff, buff_size);
        *size = voice->rec_buff_size;
    }

    return (*buff && *size);
}

void voice_set_read_handle(voice_handle_t voice, recorder_handle_t handle)
{
    voice->raw_read = handle;
}

void voice_set_user_data(voice_handle_t voice, void *user_data)
{
    voice->user_data = user_data;
}

void voice_set_wakeup_cb(voice_handle_t voice, voice_wakeup_cb cb)
{
    voice->wakeup_cb = cb;
}
void voice_set_rec_ready_cb(voice_handle_t voice, voice_rec_ready_cb cb)
{
    voice->rec_ready_cb = cb;
}

void *voice_get_user_data(voice_handle_t voice)
{
    return voice->user_data;
}

void voice_set_rec_duration(voice_handle_t voice, int duration)
{
    voice->rec_duration = duration;
}
