#include <stdio.h>
#include "xzai.h"
#include "voice.h"
#include "audio_player.h"
#include "audio_encoder.h"
#include "esp_websocket_client.h"

#include "audio_thread.h"
#include "audio_mem.h"

#include "hal_wifi.h"
#include "res_binary.h"

#include "esp_log.h"

#define TAG "xzai"

#define IOT_JOSN_BUFF_SIZE 1024

// static functions
static bool xzai_text_list_push(xzai_handle_t xzai, const char *role, const char *text);
static void xzai_toggle_chat_state(xzai_handle_t xzai);
static void xzai_abort_speaking(xzai_handle_t xzai, abort_reason_t reason);

// task functions (使用函数xzai_schedule()添加到任务队列)
static void xzai_task_start_voice(xzai_handle_t xzai);
static void xzai_task_stop_voice(xzai_handle_t xzai);
static void xzai_task_send_wakeword(xzai_handle_t xzai);
static void xzai_task_abort_speaking(xzai_handle_t xzai);
static void xzai_task_close_audio_channel(xzai_handle_t xzai);
static void xzai_task_start_listening_audostop(xzai_handle_t xzai);
static void xzai_task_start_listening_manualstop(xzai_handle_t xzai);
static void xzai_task_stop_listening(xzai_handle_t xzai);
static void xzai_task_abort_and_start_listening(xzai_handle_t xzai);
static void xzai_task_tts_start(xzai_handle_t xzai);
static void xzai_task_tts_stop(xzai_handle_t xzai);
static void xzai_task_update_text(xzai_handle_t xzai);
static void xzai_task_audio_channel_closed(xzai_handle_t xzai);
static void xzai_task_iot_command_process(xzai_handle_t xzai);

// callback functions
static void xzai_voice_wakeup_callback(voice_handle_t voice);
static void xzai_input_ready_callback(voice_handle_t voice);
static void xzai_output_ready_callback(audio_player_t player);
static void xzai_incoming_audio_callback(void *user_data, audio_stream_packet_t *packet);
static void xzai_incoming_json_callback(void *user_data, const cJSON *root);
static void xzai_audio_channel_opened_callback(void *user_data);
static void xzai_audio_channel_closed_callback(void *user_data);
static void xzai_network_error_callback(void *user_data, const char *msg);
static void xzai_audio_encoder_callback(void *arg, uint8_t *data, int len);

// extern functions
extern void xzai_loop_task(void *arg);
extern void xzai_check_ver_task(void *arg);
extern void xzai_key_task(void *arg);

static const char *const STATE_STRINGS[] = {
    "unknown",
    "starting",
    "idle",
    "connecting",
    "listening",
    "speaking",
    "activating",
    "fatal_error",
    "voice_stop",
    "voice_run",
};

struct xzai_t
{
    voice_handle_t voice;
    char *board_info;

    xzai_state_t state;

    xQueueHandle task_queue;
    EventGroupHandle_t event_group_handle;

    xzai_activation_t activation_data;
    xzai_mqtt_t mqtt_cfg;

#if XZAI_PROTOCOL_TYPE_MQTT
    mqtt_protocol_t *protocol;
#endif

#if XZAI_PROTOCOL_TYPE_WS
    ws_protocol_t *protocol;
#endif

    xzai_callbacks_t callbacks;

    int16_t *input_audio_data;
    int input_audio_data_len;

    audio_player_t audio_player;
    audio_encoder_t audio_encoder;

    List_Rsh_t *text_list;

    xQueueHandle iot_command_queue;

    uint32_t last_output_timestamp;

    char *iot_json_buff;

    bool run_flag;
    bool keep_listening;
    bool aborted;
    const char *status;
};

xzai_handle_t xzai_create(xzai_cfg_t *cfg)
{
    voice_cfg_t voice_cfg = {
        .recorder_handle = recorder,
        .sample_rate = XZAI_AUDIO_SAMPLE_RATE,
        .channels = XZAI_AUDIO_CHANNELS,
        .rec_duration = XZAI_AUDIO_DURATION,
        .model = cfg->voice_model_path,
    };

    audio_player_cfg_t audio_player_cfg = {
        .sample_rate = XZAI_AUDIO_SAMPLE_RATE,
        .bits = XZAI_AUDIO_BITS,
        .channels = XZAI_AUDIO_CHANNELS,
        .duration_ms = XZAI_AUDIO_DURATION,
        .volume = cfg->speak_volume,
    };

    audio_encoder_cfg_t audio_encoder_cfg = {
        .sample_rate = XZAI_AUDIO_SAMPLE_RATE,
        .bits = XZAI_AUDIO_BITS,
        .channels = XZAI_AUDIO_CHANNELS,
        .duration_ms = XZAI_AUDIO_DURATION,
    };

    xzai_handle_t xzai = (xzai_handle_t)audio_calloc(1, sizeof(struct xzai_t));

    if (xzai == NULL)
        goto XZAI_INIT_FAILED;

    xzai_set_state(xzai, XZAI_STATE_STARTING);
    memcpy(&xzai->callbacks, &cfg->callbacks, sizeof(xzai_callbacks_t));

    // Init begin
    if (xzai->callbacks.notif)
        xzai->callbacks.notif(xzai, XZAI_NOTIF_AI_INIT_BEGIN);

    xzai->iot_json_buff = (char *)audio_calloc(1, sizeof(char) * IOT_JOSN_BUFF_SIZE);
    if (xzai->iot_json_buff == NULL)
        goto XZAI_INIT_FAILED;

    xzai->text_list = list_new();
    if (xzai->text_list == NULL)
        goto XZAI_INIT_FAILED;

    xzai->task_queue = xQueueCreate(8, sizeof(xzai_task_exec));
    if (xzai->task_queue == NULL)
        goto XZAI_INIT_FAILED;

    xzai->iot_command_queue = xQueueCreate(4, sizeof(cJSON *));
    if (xzai->iot_command_queue == NULL)
        goto XZAI_INIT_FAILED;

    xzai->event_group_handle = xEventGroupCreate();
    if (xzai->event_group_handle == NULL)
        goto XZAI_INIT_FAILED;

    xzai->board_info = (char *)audio_calloc(1, sizeof(char) * 2048);
    if (xzai->board_info == NULL)
        goto XZAI_INIT_FAILED;

    xzai->audio_player = audio_player_create(&audio_player_cfg);
    if (xzai->audio_player == NULL)
        goto XZAI_INIT_FAILED;

    audio_player_set_user_data(xzai->audio_player, xzai);
    audio_player_set_callback(xzai->audio_player, xzai_output_ready_callback);

    xzai->audio_encoder = audio_encoder_create(&audio_encoder_cfg);
    if (xzai->audio_encoder == NULL)
        goto XZAI_INIT_FAILED;

    audio_encoder_set_user_data(xzai->audio_encoder, xzai);
    audio_encoder_set_callback(xzai->audio_encoder, xzai_audio_encoder_callback);

    xzai->voice = voice_create(&voice_cfg);
    if (xzai->voice == NULL)
        goto XZAI_INIT_FAILED;

    voice_set_wakeup_cb(xzai->voice, xzai_voice_wakeup_callback);
    voice_set_rec_ready_cb(xzai->voice, xzai_input_ready_callback);
    voice_set_user_data(xzai->voice, xzai);

    xzai_get_board_info(xzai->board_info);
    ESP_LOGI(TAG, "%s", xzai->board_info);

    // XZAI主循环
    xzai->run_flag = true;
    audio_thread_create(NULL, "xzai_loop", xzai_loop_task, xzai, 16 * 1024, 5, false, 0);
    // XZAI按键检测(手动触发聊天)
    audio_thread_create(NULL, "xzai_key", xzai_key_task, xzai, 4 * 1024, 5, true, 0);

    // IOT
    xzai_thing_init();

    // 初始化通信协议
#if XZAI_PROTOCOL_TYPE_MQTT
    mqtt_protocol_cfg_t protocol_cfg = {
        .endpoint = cfg->mqtt_setting_cfg.endpoint,
        .client_id = cfg->mqtt_setting_cfg.client_id,
        .username = cfg->mqtt_setting_cfg.username,
        .password = cfg->mqtt_setting_cfg.password,
        .publish_topic = cfg->mqtt_setting_cfg.publish_topic,
        .subscribe_topic = cfg->mqtt_setting_cfg.subscribe_topic,
    };
    xzai->protocol = mqtt_protocol_create(&protocol_cfg);
#else
    xzai->callbacks.mqtt_cfg_update = NULL;
#endif

#if XZAI_PROTOCOL_TYPE_WS
    ws_protocol_cfg_t protocol_cfg = {
        .ws_url = XZAI_API_WEBSOCKET_URL,
        .ws_token = XZAI_WEBSOCKET_ACCESS_TOKEN,
    };
    xzai->protocol = ws_protocol_create(&protocol_cfg);
#endif

    if (xzai->protocol == NULL)
        goto XZAI_INIT_FAILED;

    xzai->protocol->base.incoming_audio_cb = xzai_incoming_audio_callback;
    xzai->protocol->base.incoming_json_cb = xzai_incoming_json_callback;
    xzai->protocol->base.audio_channel_opened_cb = xzai_audio_channel_opened_callback;
    xzai->protocol->base.audio_channel_closed_cb = xzai_audio_channel_closed_callback;
    xzai->protocol->base.network_error_cb = xzai_network_error_callback;

    protocol_set_user_data(&xzai->protocol->base, xzai);
    protocol_start(&xzai->protocol->base);

    // XZAI设备激活检测
    audio_thread_create(NULL, "xzai_check", xzai_check_ver_task, xzai, 16 * 1024, 4, true, 0);

    xzai_set_state(xzai, XZAI_STATE_IDLE);

    if (xzai->callbacks.notif)
        xzai->callbacks.notif(xzai, XZAI_NOTIF_AI_INIT_OK);

    return xzai;

XZAI_INIT_FAILED:
    ESP_LOGE(TAG, "xzai init failed");
    if (xzai->callbacks.notif)
        xzai->callbacks.notif(xzai, XZAI_NOTIF_AI_INIT_FAIL);

    xzai_destroy(xzai);
    return NULL;
}

void xzai_destroy(xzai_handle_t xzai)
{
    if (xzai == NULL)
        return;

    xzai->run_flag = false;

    if (xzai->voice)
        voice_input_audio_stop(xzai->voice);

    if (xzai->audio_player)
    {
        audio_player_destroy(xzai->audio_player);
        xzai->audio_player = NULL;
    }

    if (xzai->audio_encoder)
    {
        audio_encoder_destroy(xzai->audio_encoder);
        xzai->audio_encoder = NULL;
    }

    if (xzai->task_queue)
    {
        vQueueDelete(xzai->task_queue);
        xzai->task_queue = NULL;
    }

    if (xzai->iot_command_queue)
    {
        vQueueDelete(xzai->iot_command_queue);
        xzai->iot_command_queue = NULL;
    }

    if (xzai->text_list)
    {
        xzai_text_list_clear(xzai);

        list_del(xzai->text_list);
        xzai->text_list = NULL;
    }

    if (xzai->iot_json_buff)
    {
        audio_free(xzai->iot_json_buff);
        xzai->iot_json_buff = NULL;
    }

    if (xzai->event_group_handle)
    {
        vEventGroupDelete(xzai->event_group_handle);
    }

    if (xzai->board_info)
    {
        audio_free(xzai->board_info);
        xzai->board_info = NULL;
    }

    if (xzai->input_audio_data)
    {
        audio_free(xzai->input_audio_data);
        xzai->input_audio_data = NULL;
    }

    if (xzai->voice)
    {
        voice_destroy(xzai->voice);
        xzai->voice = NULL;
    }

    if (xzai->protocol)
    {
#if XZAI_PROTOCOL_TYPE_MQTT
        mqtt_protocol_destroy(xzai->protocol);
#elif XZAI_PROTOCOL_TYPE_WS
        ws_protocol_destroy(xzai->protocol);
#endif

        xzai->protocol = NULL;
    }

    audio_free(xzai);
    xzai = NULL;
}

void xzai_schedule(xzai_handle_t xzai, xzai_task_exec exec)
{
    BaseType_t res = xQueueSend(xzai->task_queue, &exec, 0);
    if (res == pdTRUE)
    {
        xzai_set_event_bit(xzai, SCHEDULE_EVENT);
    }
}

void xzai_start_listening(xzai_handle_t xzai)
{
    if (xzai->callbacks.notif)
        xzai->callbacks.notif(xzai, XZAI_NOTIF_WAKE_UP);

    if (xzai->state == XZAI_STATE_ACTIVATING)
    {
        xzai_set_state(xzai, XZAI_STATE_IDLE);
        return;
    }

    if (xzai->protocol == NULL)
    {
        ESP_LOGE(TAG, "Protocol not initialized");
        return;
    }

    xzai->keep_listening = false;
    if (xzai->state == XZAI_STATE_IDLE)
    {
        xzai_schedule(xzai, xzai_task_start_listening_manualstop);
    }
    else if (xzai->state == XZAI_STATE_SPEAKING)
    {
        xzai_schedule(xzai, xzai_task_abort_and_start_listening);
    }
}

void xzai_stop_listening(xzai_handle_t xzai)
{
    xzai_schedule(xzai, xzai_task_stop_listening);
}

bool xzai_stop_chat(xzai_handle_t xzai)
{
    ESP_LOGI(TAG, "stop AI chat");

    if (xzai->state != XZAI_STATE_IDLE || protocol_is_audio_channel_opened(&xzai->protocol->base))
        return false;

    audio_player_clear_data(xzai->audio_player);
    audio_player_pause(xzai->audio_player);
    return true;
}

void xzai_input_audio(xzai_handle_t xzai)
{
    if (!voice_read_audio_data(xzai->voice, &xzai->input_audio_data, &xzai->input_audio_data_len))
        return;

    if (xzai->input_audio_data == NULL)
        return;

    // 继续读取音频数据
    voice_input_audio_start(xzai->voice);

    // 编码音频数据
    audio_encoder_send_data(xzai->audio_encoder, xzai->input_audio_data, xzai->input_audio_data_len);
}

void xzai_output_audio(xzai_handle_t xzai)
{
    if (xzai->state == XZAI_STATE_LISTENING)
    {
    }
}

void xzai_text_list_clear(xzai_handle_t xzai)
{
    if (xzai->text_list == NULL)
        return;

    ListNode_t *pos;
    LIST_FOR_EACH(pos, xzai->text_list)
    {
        if (pos->data)
        {
            free(pos->data);
            pos->data = NULL;
        }
    }
    list_clear(xzai->text_list);
}

void xzai_update_iot_states(xzai_handle_t xzai)
{
    memset(xzai->iot_json_buff, 0x0, IOT_JOSN_BUFF_SIZE);

    if (xzai_thing_get_state_json(xzai->iot_json_buff, true))
    {
        ESP_LOGD(TAG, "iot states %s", xzai->iot_json_buff);
        xzai->protocol->base.send_iot_descriptors(&xzai->protocol->base, xzai->iot_json_buff);
    }
}

void xzai_alert_msg(xzai_handle_t xzai, const char *status, const char *message, const char *emotion)
{
    ESP_LOGW(TAG, "alert %s: %s [%s]", status, message, emotion);
    if (status && xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, status))
        xzai_schedule(xzai, xzai_task_update_text);
    if (message && xzai_text_list_push(xzai, XZAI_MSG_ROLE_SYSTEM, message))
        xzai_schedule(xzai, xzai_task_update_text);
    if (emotion && xzai_text_list_push(xzai, XZAI_MSG_ROLE_EMOTION, emotion))
        xzai_schedule(xzai, xzai_task_update_text);
}

/**
 * SET
 */
void xzai_set_check_ver_callback(xzai_handle_t xzai, xzai_check_ver_callback cb)
{
    xzai->callbacks.check_ver = cb;
}

void xzai_set_cfg_update_callback(xzai_handle_t xzai, xzai_mqttt_cfg_update_callback cb)
{
    xzai->callbacks.mqtt_cfg_update = cb;
}

void xzai_set_notif_callback(xzai_handle_t xzai, xzai_notif_callback cb)
{
    xzai->callbacks.notif = cb;
}

void xzai_set_state(xzai_handle_t xzai, xzai_state_t state)
{
    if (xzai == NULL || xzai->state == state)
        return;

    xzai_state_t previous_state = xzai->state;
    xzai->state = state;

    if (xzai->callbacks.notif)
        xzai->callbacks.notif(xzai, XZAI_NOTIF_STATE_UPDATE);

    switch (state)
    {
    case XZAI_STATE_UNKNOWN:
        xzai->status = XZAI_STATUS_UNKNOWN;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        break;
    case XZAI_STATE_STARTING:
        xzai->status = XZAI_STATUS_STARTING;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        break;
    case XZAI_STATE_IDLE:
        xzai->status = XZAI_STATUS_IDLE;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_EMOTION, "neutral"))
            xzai_schedule(xzai, xzai_task_update_text);
        voice_input_audio_stop(xzai->voice);
        break;
    case XZAI_STATE_CONNECTING:
        xzai->status = XZAI_STATUS_CONNECTING;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_EMOTION, "neutral"))
            xzai_schedule(xzai, xzai_task_update_text);
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_SYSTEM, ""))
            xzai_schedule(xzai, xzai_task_update_text);
        break;
    case XZAI_STATE_LISTENING:
        // 上传IOT设备状态数据
        xzai_update_iot_states(xzai);

        xzai->status = XZAI_STATUS_LISTENING;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_EMOTION, "neutral"))
            xzai_schedule(xzai, xzai_task_update_text);

        audio_player_clear_data(xzai->audio_player);
        audio_encoder_reset(xzai->audio_encoder);

        // 开启音频输入
        voice_input_audio_start(xzai->voice);

        if (previous_state == XZAI_STATE_SPEAKING)
        {
            // FIXME: Wait for the speaker to empty the buffer
            vTaskDelay(pdMS_TO_TICKS(120));
        }

        break;
    case XZAI_STATE_SPEAKING:
        xzai->status = XZAI_STATUS_SPEAKING;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        // 关闭音频输入
        voice_input_audio_stop(xzai->voice);

        audio_player_clear_data(xzai->audio_player);
        audio_encoder_reset(xzai->audio_encoder);
        break;
    case XZAI_STATE_VOICE_STOP:
        xzai_schedule(xzai, xzai_task_stop_voice);
        break;
    case XZAI_STATE_VOICE_RUN:
        xzai_schedule(xzai, xzai_task_start_voice);
        break;
    case XZAI_STATE_ACTIVATING:
        xzai->status = XZAI_STATUS_ACTIVATING;
        if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_STATUS, xzai->status))
            xzai_schedule(xzai, xzai_task_update_text);
        break;
    default:
        break;
    }

    ESP_LOGI(TAG, "STATE: %s", STATE_STRINGS[xzai->state]);
}

void xzai_set_event_bit(xzai_handle_t xzai, EventBits_t bit)
{
    xEventGroupSetBits(xzai->event_group_handle, bit);
}

void xzai_set_speak_volume(xzai_handle_t xzai, int volume)
{
    if (xzai->audio_player == NULL)
        return;

    audio_player_set_volume(xzai->audio_player, volume);
}

/**
 * GET
 */
xzai_mqtt_t *xzai_get_mqtt_cfg(xzai_handle_t xzai)
{
    return &xzai->mqtt_cfg;
}

xzai_activation_t *xzai_get_activation(xzai_handle_t xzai)
{
    return &xzai->activation_data;
}

char *xzai_get_boardinfo(xzai_handle_t xzai)
{
    return xzai->board_info;
}

xzai_callbacks_t *xzai_get_callbacks(xzai_handle_t xzai)
{
    return &xzai->callbacks;
}

xzai_state_t xzai_get_state(xzai_handle_t xzai)
{
    if (xzai == NULL)
        return XZAI_STATE_UNKNOWN;

    return xzai->state;
}

const char *xzai_get_status_desc(xzai_handle_t xzai)
{
    if (xzai == NULL)
        return NULL;

    return xzai->status;
}

EventGroupHandle_t xzai_get_event_group(xzai_handle_t xzai)
{
    return xzai->event_group_handle;
}

xzai_text_data_t *xzai_get_one_text(xzai_handle_t xzai)
{
    void *data = list_pop_front(xzai->text_list);

    if (data == NULL)
        return NULL;

    return (xzai_text_data_t *)data;
}

xQueueHandle xzai_get_task_queue(xzai_handle_t xzai)
{
    return xzai->task_queue;
}

bool xzai_is_running(xzai_handle_t xzai)
{
    return xzai->run_flag;
}

/************************************************STATIC****************************************************/
static bool xzai_text_list_push(xzai_handle_t xzai, const char *role, const char *text)
{
    if (xzai->text_list == NULL)
        return false;

    xzai_text_data_t *text_data = (xzai_text_data_t *)malloc(sizeof(xzai_text_data_t));
    if (!text_data)
        return false;

    text_data->content = (char *)malloc(sizeof(char) * (strlen(text) + 1));
    if (!text_data->content)
    {
        free(text_data);
        text_data = NULL;
        return false;
    }

    text_data->role = role;
    strcpy(text_data->content, text);

    list_push_back(xzai->text_list, text_data);

    return true;
}

static void xzai_toggle_chat_state(xzai_handle_t xzai)
{
    if (xzai->state == XZAI_STATE_ACTIVATING)
    {
        xzai_set_state(xzai, XZAI_STATE_IDLE);
        return;
    }

    if (xzai->protocol == NULL)
    {
        ESP_LOGE(TAG, "Protocol not initialized");
        return;
    }

    if (xzai->state == XZAI_STATE_IDLE)
    {
        xzai_schedule(xzai, xzai_task_start_listening_audostop);
    }
    else if (xzai->state == XZAI_STATE_SPEAKING)
    {
        xzai_schedule(xzai, xzai_task_abort_speaking);
    }
    else if (xzai->state == XZAI_STATE_LISTENING)
    {
        xzai_schedule(xzai, xzai_task_close_audio_channel);
    }
}

static void xzai_abort_speaking(xzai_handle_t xzai, abort_reason_t reason)
{
    if (xzai->protocol == NULL)
        return;

    ESP_LOGI(TAG, "Abort speaking");
    xzai->aborted = true;
    xzai->protocol->base.send_abort_speaking(&xzai->protocol->base, reason);
}

/************************************************TASK****************************************************/
static void xzai_task_start_voice(xzai_handle_t xzai)
{
    xzai->callbacks.notif(xzai, XZAI_NOTIF_START_VOICE_BEGIN);

    if (xzai->audio_player)
        audio_player_run(xzai->audio_player);

    if (xzai->voice)
        voice_recorder_start(xzai->voice);

    xzai->callbacks.notif(xzai, XZAI_NOTIF_START_VOICE_END);
    xzai_set_state(xzai, XZAI_STATE_IDLE);
}

static void xzai_task_stop_voice(xzai_handle_t xzai)
{
    xzai->callbacks.notif(xzai, XZAI_NOTIF_STOP_VOICE_BEGIN);

    if (xzai->audio_player)
        audio_player_pause(xzai->audio_player);

    if (xzai->voice)
        voice_recorder_stop(xzai->voice);

    xzai->callbacks.notif(xzai, XZAI_NOTIF_STOP_VOICE_END);
    xzai_set_state(xzai, XZAI_STATE_IDLE);
}

static void xzai_task_send_wakeword(xzai_handle_t xzai)
{
    if (xzai->protocol)
        xzai->protocol->base.send_wakeword_detected(&xzai->protocol->base, XZAI_WAKE_WORD);
}

static void xzai_task_abort_speaking(xzai_handle_t xzai)
{
    xzai_abort_speaking(xzai, ABORT_REASON_NONE);
}

static void xzai_task_close_audio_channel(xzai_handle_t xzai)
{
    if (xzai->protocol)
        protocol_close_audio_channel(&xzai->protocol->base);
}

static void xzai_task_start_listening_audostop(xzai_handle_t xzai)
{
    if (xzai->protocol == NULL)
        return;

    xzai_set_state(xzai, XZAI_STATE_CONNECTING);

    if (!protocol_open_audio_channel(&xzai->protocol->base))
        return;

    xzai->keep_listening = true;

    xzai->protocol->base.send_start_listening(&xzai->protocol->base, LISTENING_MODE_AUTOSTOP);
    xzai_set_state(xzai, XZAI_STATE_LISTENING);
}

static void xzai_task_start_listening_manualstop(xzai_handle_t xzai)
{
    if (xzai->protocol == NULL)
        return;

    if (!protocol_is_audio_channel_opened(&xzai->protocol->base))
    {
        xzai_set_state(xzai, XZAI_STATE_CONNECTING);
        if (!protocol_open_audio_channel(&xzai->protocol->base))
            return;
    }

    xzai->protocol->base.send_start_listening(&xzai->protocol->base, LISTENING_MODE_MANUALSTOP);
    xzai_set_state(xzai, XZAI_STATE_LISTENING);
}

static void xzai_task_stop_listening(xzai_handle_t xzai)
{
    if (xzai->protocol == NULL)
        return;

    if (xzai->state == XZAI_STATE_LISTENING)
    {
        xzai->protocol->base.send_stop_listening(&xzai->protocol->base);
        xzai_set_state(xzai, XZAI_STATE_IDLE);
    }
}

static void xzai_task_abort_and_start_listening(xzai_handle_t xzai)
{
    if (xzai->protocol == NULL)
        return;

    xzai_abort_speaking(xzai, ABORT_REASON_NONE);
    xzai->protocol->base.send_start_listening(&xzai->protocol->base, LISTENING_MODE_MANUALSTOP);

    xzai_set_state(xzai, XZAI_STATE_LISTENING);
}

static void xzai_task_tts_start(xzai_handle_t xzai)
{
    ESP_LOGI(TAG, "tts start");
    xzai->aborted = false;
    if (xzai->state == XZAI_STATE_IDLE || xzai->state == XZAI_STATE_LISTENING)
    {
        xzai_set_state(xzai, XZAI_STATE_SPEAKING);
    }
}

static void xzai_task_tts_stop(xzai_handle_t xzai)
{
    ESP_LOGI(TAG, "tts stop");

    if (xzai->state == XZAI_STATE_SPEAKING)
    {
        // wait
        if (xzai->keep_listening)
        {
            xzai->protocol->base.send_start_listening(&xzai->protocol->base, LISTENING_MODE_AUTOSTOP);
            xzai_set_state(xzai, XZAI_STATE_LISTENING);
        }
        else
        {
            xzai_set_state(xzai, XZAI_STATE_IDLE);
        }
    }
}

static void xzai_task_update_text(xzai_handle_t xzai)
{
    xzai->callbacks.notif(xzai, XZAI_NOTIF_TEXT_UPDATE);
}

static void xzai_task_audio_channel_closed(xzai_handle_t xzai)
{
    ESP_LOGI(TAG, "audio channel closed");

    // 音频通道关闭
    xzai->callbacks.notif(xzai, XZAI_NOTIF_AUDIO_CLOSED);

    if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_SYSTEM, ""))
    {
        xzai_schedule(xzai, xzai_task_update_text);
    }

    xzai_set_state(xzai, XZAI_STATE_IDLE);

    xEventGroupSetBits(xzai->event_group_handle, AUDIO_CHANNEL_CLOSED_EVENT);
}

static void xzai_task_iot_command_process(xzai_handle_t xzai)
{
    cJSON *commands = NULL;

    if (xQueueReceive(xzai->iot_command_queue, &commands, pdMS_TO_TICKS(100)) == pdPASS)
    {
        for (int i = 0; i < cJSON_GetArraySize(commands); ++i)
        {
            cJSON *command = cJSON_GetArrayItem(commands, i);
            ESP_LOGI(TAG, "command %s", cJSON_Print(command));
            xzai_thing_invoke(command);
        }
        cJSON_Delete(commands);
    }
}

/************************************************CALLBACK****************************************************/
static void xzai_voice_wakeup_callback(voice_handle_t voice)
{
    xzai_handle_t xzai = (xzai_handle_t)voice_get_user_data(voice);
    ESP_LOGI(TAG, "Wakeup invoke");

    if (xzai->callbacks.notif)
        xzai->callbacks.notif(xzai, XZAI_NOTIF_WAKE_UP);

    if (xzai->state == XZAI_STATE_IDLE)
    {
        xzai_toggle_chat_state(xzai);
        xzai_schedule(xzai, xzai_task_send_wakeword);
    }
    else if (xzai->state == XZAI_STATE_SPEAKING)
    {
        xzai_schedule(xzai, xzai_task_abort_speaking);
    }
    else if (xzai->state == XZAI_STATE_LISTENING)
    {
        xzai_schedule(xzai, xzai_task_close_audio_channel);
    }
}

static void xzai_input_ready_callback(voice_handle_t voice)
{
    xzai_handle_t xzai = (xzai_handle_t)voice_get_user_data(voice);
    if (xzai)
        xzai_set_event_bit(xzai, AUDIO_INPUT_READY_EVENT);
}

static void xzai_output_ready_callback(audio_player_t player)
{
    xzai_handle_t xzai = (xzai_handle_t)audio_player_get_user_data(player);
    if (xzai)
        xzai_set_event_bit(xzai, AUDIO_OUTPUT_READY_EVENT);
}

static void xzai_incoming_audio_callback(void *user_data, audio_stream_packet_t *packet)
{
    xzai_handle_t xzai = (xzai_handle_t)user_data;

    if (xzai->state == XZAI_STATE_SPEAKING)
    {
        ESP_LOGD(TAG, "incoming audio %d, %d", packet->payload[0], packet->size);

        xzai->last_output_timestamp = packet->timestamp;
        audio_player_add_data(xzai->audio_player, packet->payload, packet->size);
    }
}

static void xzai_incoming_json_callback(void *user_data, const cJSON *root)
{
    xzai_handle_t xzai = (xzai_handle_t)user_data;

    cJSON *type = cJSON_GetObjectItem(root, "type");
    if (type == NULL)
        return;

    if (strcmp(type->valuestring, "tts") == 0)
    {
        cJSON *state = cJSON_GetObjectItem(root, "state");
        if (strcmp(state->valuestring, "start") == 0)
        {
            xzai_schedule(xzai, xzai_task_tts_start);
        }
        else if (strcmp(state->valuestring, "stop") == 0)
        {
            xzai_schedule(xzai, xzai_task_tts_stop);
        }
        else if (strcmp(state->valuestring, "sentence_start") == 0)
        {
            cJSON *text = cJSON_GetObjectItem(root, "text");
            if (text != NULL)
            {
                ESP_LOGD(TAG, "<< %s", text->valuestring);
                if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_ASSISTANT, text->valuestring))
                {
                    // 文本更新
                    xzai_schedule(xzai, xzai_task_update_text);
                }
            }
        }
    }
    else if (strcmp(type->valuestring, "stt") == 0)
    {
        cJSON *text = cJSON_GetObjectItem(root, "text");
        if (text != NULL)
        {
            ESP_LOGD(TAG, ">> %s", text->valuestring);
            if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_USER, text->valuestring))
            {
                // 文本更新
                xzai_schedule(xzai, xzai_task_update_text);
            }
        }
    }
    else if (strcmp(type->valuestring, "llm") == 0)
    {
        cJSON *emotion = cJSON_GetObjectItem(root, "emotion");
        if (emotion != NULL)
        {
            if (xzai_text_list_push(xzai, XZAI_MSG_ROLE_EMOTION, emotion->valuestring))
            {
                // 表情更新
                xzai_schedule(xzai, xzai_task_update_text);
            }
        }
    }
    else if (strcmp(type->valuestring, "iot") == 0)
    {
        cJSON *commands = cJSON_GetObjectItem(root, "commands");
        cJSON *commands_copy = cJSON_Duplicate(commands, 1);

        if (xQueueSend(xzai->iot_command_queue, &commands_copy, portMAX_DELAY) != pdPASS)
        {
            // 发送失败处理
            cJSON_Delete(commands_copy); // 释放内存
            return;
        }

        xzai_schedule(xzai, xzai_task_iot_command_process);
    }
}

static void xzai_audio_channel_opened_callback(void *user_data)
{
    ESP_LOGI(TAG, "audio channel is opened");

    xzai_handle_t xzai = (xzai_handle_t)user_data;
    audio_player_set_samplerate(xzai->audio_player, xzai->protocol->base.server_sample_rate);

    memset(xzai->iot_json_buff, 0x0, IOT_JOSN_BUFF_SIZE);
    xzai_thing_get_descriptors_json(xzai->iot_json_buff);
    ESP_LOGD(TAG, "iot desc %s", xzai->iot_json_buff);
    xzai->protocol->base.send_iot_descriptors(&xzai->protocol->base, xzai->iot_json_buff);

    memset(xzai->iot_json_buff, 0x0, IOT_JOSN_BUFF_SIZE);
    if (xzai_thing_get_state_json(xzai->iot_json_buff, false))
    {
        ESP_LOGD(TAG, "iot states %s", xzai->iot_json_buff);
        xzai->protocol->base.send_iot_descriptors(&xzai->protocol->base, xzai->iot_json_buff);
    }
}

static void xzai_audio_channel_closed_callback(void *user_data)
{
    xzai_handle_t xzai = (xzai_handle_t)user_data;
    xzai_schedule(xzai, xzai_task_audio_channel_closed);
}

static void xzai_network_error_callback(void *user_data, const char *msg)
{
    xzai_handle_t xzai = (xzai_handle_t)user_data;

    if (msg == NULL)
        return;

    ESP_LOGE(TAG, "%s", msg);

    xzai_set_state(xzai, XZAI_STATE_IDLE);
    xzai_alert_msg(xzai, XZAI_STATUS_NETWORK_ERROR, msg, "sad");
}

static void xzai_audio_encoder_callback(void *arg, uint8_t *data, int len)
{
    xzai_handle_t xzai = (xzai_handle_t)arg;

    // 发送编码后的音频数据
    if (xzai && xzai->state == XZAI_STATE_LISTENING)
    {
        ESP_LOGD(TAG, "send audio len %ld", len);

        audio_stream_packet_t packet;
        packet.payload = data;
        packet.size = len;
        packet.timestamp = xzai->last_output_timestamp;
        xzai->last_output_timestamp = 0;
        protocol_send_audio(&xzai->protocol->base, &packet);
    }
}
