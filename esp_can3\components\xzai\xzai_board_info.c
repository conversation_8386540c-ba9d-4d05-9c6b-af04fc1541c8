#include "xzai_board_info.h"
#include <string.h>
#include <esp_log.h>
#include "system_util.h"

#define TAG "xzai_board"

void get_board_json(cJSON *item)
{
    if (item == NULL)
        return;

    cJSON_AddStringToObject(item, "type", "lichuang-dev");
    cJSON_AddStringToObject(item, "name", "lichuang-dev");

#if 0
        cJSON_AddStringToObject(item, "ssid", "");
        cJSON_AddStringToObject(item, "rssi", "");
        cJSON_AddStringToObject(item, "channel", "");
        cJSON_AddStringToObject(item, "ip", "");
#endif

    cJSON_AddStringToObject(item, "mac", get_mac_address());
}

void xzai_get_board_info(const char *buff)
{
    /*
        {
            "version": 2,
            "flash_size": 4194304,
            "psram_size": 0,
            "minimum_free_heap_size": 123456,
            "mac_address": "00:00:00:00:00:00",
            "uuid": "00000000-0000-0000-0000-000000000000",
            "chip_model_name": "esp32s3",
            "chip_info": {
                "model": 1,
                "cores": 2,
                "revision": 0,
                "features": 0
            },
            "application": {
                "name": "my-app",
                "version": "1.0.0",
                "compile_time": "2021-01-01T00:00:00Z"
                "idf_version": "4.2-dev"
                "elf_sha256": ""
            },
            "partition_table": [
                "app": {
                    "label": "app",
                    "type": 1,
                    "subtype": 2,
                    "address": 0x10000,
                    "size": 0x100000
                }
            ],
            "ota": {
                "label": "ota_0"
            },
            "board": {
                ...
            }
        }
    */

    cJSON *root = NULL;
    cJSON *item_chip_info = NULL;
    cJSON *item_application = NULL;
    cJSON *array_partition_table = NULL;
    cJSON *item_ota = NULL;
    cJSON *item_board = NULL;

    if (buff == NULL)
        return;

    root = cJSON_CreateObject();

    if (root == NULL)
    {
        sprintf(buff, "{}");
    }
    else
    {
        cJSON_AddNumberToObject(root, "version", 2);
        cJSON_AddStringToObject(root, "language", "zh-CN");
        cJSON_AddNumberToObject(root, "flash_size", get_flash_size());
        cJSON_AddNumberToObject(root, "minimum_free_heap_size", get_minimum_free_heap_size());
        cJSON_AddStringToObject(root, "mac_address", get_mac_address());
        cJSON_AddStringToObject(root, "uuid", generate_uuid());
        cJSON_AddStringToObject(root, "chip_model_name", "esp32s3");

        // chip info
        item_chip_info = cJSON_CreateObject();

        esp_chip_info_t chip_info;
        esp_chip_info(&chip_info);

        cJSON_AddNumberToObject(item_chip_info, "model", chip_info.model);
        cJSON_AddNumberToObject(item_chip_info, "cores", chip_info.cores);
        cJSON_AddNumberToObject(item_chip_info, "revision", chip_info.revision);
        cJSON_AddNumberToObject(item_chip_info, "features", chip_info.features);

        cJSON_AddItemToObject(root, "chip_info", item_chip_info);

        // application
        item_application = cJSON_CreateObject();

        const esp_app_desc_t *app_desc = esp_app_get_description();
        cJSON_AddStringToObject(item_application, "name", app_desc->project_name);
        cJSON_AddStringToObject(item_application, "version", app_desc->version);
        cJSON_AddStringToObject(item_application, "compile_time", get_compile_time(app_desc));
        cJSON_AddStringToObject(item_application, "idf_version", app_desc->idf_ver);

        char sha256_str[65];
        for (int i = 0; i < 32; i++)
        {
            snprintf(sha256_str + i * 2, sizeof(sha256_str) - i * 2, "%02x", app_desc->app_elf_sha256[i]);
        }
        cJSON_AddStringToObject(item_application, "elf_sha256", sha256_str);

        cJSON_AddItemToObject(root, "application", item_application);

        // partition_table
        array_partition_table = cJSON_CreateArray();

        esp_partition_iterator_t it = esp_partition_find(ESP_PARTITION_TYPE_ANY, ESP_PARTITION_SUBTYPE_ANY, NULL);

        while (it)
        {
            cJSON *item = cJSON_CreateObject();
            const esp_partition_t *partition = esp_partition_get(it);

            cJSON_AddStringToObject(item, "label", partition->label);
            cJSON_AddNumberToObject(item, "type", partition->type);
            cJSON_AddNumberToObject(item, "subtype", partition->subtype);
            cJSON_AddNumberToObject(item, "address", partition->address);
            cJSON_AddNumberToObject(item, "size", partition->size);

            cJSON_AddItemToArray(array_partition_table, item);

            it = esp_partition_next(it);
        }

        cJSON_AddItemToObject(root, "partition_table", array_partition_table);

        // ota
        item_ota = cJSON_CreateObject();
        const esp_partition_t *ota_partition = esp_ota_get_running_partition();

        cJSON_AddStringToObject(item_ota, "label", ota_partition->label);

        cJSON_AddItemToObject(root, "ota", item_ota);

        // board
        item_board = cJSON_CreateObject();
        get_board_json(item_board);

        cJSON_AddItemToObject(root, "board", item_board);
    }

    if (root)
    {
        sprintf(buff, "%s", cJSON_Print(root));
        cJSON_Delete(root);
    }
}