#include "xzai_things.h"
#include "thing_manager.h"
#include "audio_mem.h"

#include "esp_log.h"

#define TAG "xzai_things"
#define JSON_BUFF_SIZE 1024

using namespace iot;

void xzai_thing_init()
{
    auto &thing_manager = ThingManager::GetInstance();
    thing_manager.AddThing(CreateThing("Battery"));
    thing_manager.AddThing(CreateThing("Backlight"));
    thing_manager.AddThing(CreateThing("Speaker"));
    thing_manager.AddThing(CreateThing("Bluetooth"));
}

void xzai_thing_deinit()
{

}

void xzai_thing_get_descriptors_json(char *buff)
{
    auto &thing_manager = ThingManager::GetInstance();
    strcpy(buff, thing_manager.GetDescriptorsJson().c_str());
}

bool xzai_thing_get_state_json(char *buff, bool delta)
{
    std::string states;
    bool res;

    auto &thing_manager = ThingManager::GetInstance();
    res = thing_manager.GetStatesJson(states, delta);

    strcpy(buff, states.c_str());

    return res;
}


void xzai_thing_invoke(const cJSON* command)
{
    auto &thing_manager = ThingManager::GetInstance();
    thing_manager.Invoke(command);
}