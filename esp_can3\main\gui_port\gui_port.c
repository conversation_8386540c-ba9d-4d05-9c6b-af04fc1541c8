#include "gui_port.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "device_bsp.h"
#include "lvgl.h"
// #include "lv_port_disp.h"
// #include "lv_port_indev.h"

#include "ui_user_inc.h"

#define TAG "gui_port"


void gui_port_init(int start_id)
{
    ESP_LOGI(TAG, "Initializing GUI port...");

    // 初始化显示屏和LVGL
    bsp_lvgl_start();
    ESP_LOGI(TAG, "LVGL initialized successfully");
    

    // 获取LVGL锁并初始化UI界面
    ESP_LOGI(TAG, "Locking LVGL port...");
    if (lvgl_port_lock(0)) {
        ESP_LOGI(TAG, "Initializing UI screens with start ID: %d", start_id);
        ui_scr_init(start_id);
        ESP_LOGI(TAG, "UI screens initialized successfully");

        // 释放LVGL锁
        lvgl_port_unlock();
        ESP_LOGI(TAG, "LVGL port unlocked");
    } else {
        ESP_LOGE(TAG, "Failed to lock LVGL port, UI not initialized");
    }

    ESP_LOGI(TAG, "GUI port initialization completed");
}

