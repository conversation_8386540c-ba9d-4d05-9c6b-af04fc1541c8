/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_CUSTOMER_FONT_HARMONYOS_SANS_SC_MEDIUM_16
#define LV_CUSTOMER_FONT_HARMONYOS_SANS_SC_MEDIUM_16 1
#endif

#if LV_CUSTOMER_FONT_HARMONYOS_SANS_SC_MEDIUM_16

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+4E2D "中" */
    0x0, 0x0, 0x0, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf9, 0x0, 0x0, 0x0, 0x38, 0x88,
    0x88, 0xfd, 0x88, 0x88, 0x80, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x7f, 0x10, 0x0, 0xf9,
    0x0, 0x6, 0xf1, 0x7f, 0x10, 0x0, 0xf9, 0x0,
    0x6, 0xf1, 0x7f, 0x10, 0x0, 0xf9, 0x0, 0x6,
    0xf1, 0x7f, 0x10, 0x0, 0xfa, 0x0, 0x7, 0xf1,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x37,
    0x77, 0x77, 0xfc, 0x77, 0x77, 0x70, 0x0, 0x0,
    0x0, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf9, 0x0,
    0x0, 0x0,

    /* U+4EF6 "件" */
    0x0, 0x5, 0xc1, 0x1, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0xb, 0xd0, 0x4f, 0x31, 0xf7, 0x0, 0x0,
    0x0, 0x2f, 0x70, 0x9f, 0x23, 0xf8, 0x22, 0x20,
    0x0, 0x9f, 0x10, 0xef, 0xff, 0xff, 0xff, 0xf0,
    0x2, 0xff, 0x4, 0xf7, 0x55, 0xf9, 0x55, 0x50,
    0xa, 0xff, 0xb, 0xe0, 0x1, 0xf7, 0x0, 0x0,
    0x5f, 0xdf, 0x8, 0x70, 0x1, 0xf7, 0x0, 0x0,
    0x4c, 0x6f, 0x2, 0x22, 0x23, 0xf8, 0x22, 0x21,
    0x1, 0x6f, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x6f, 0x4, 0x44, 0x45, 0xf9, 0x44, 0x42,
    0x0, 0x6f, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0x6f, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0x6f, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,

    /* U+53D1 "发" */
    0x0, 0x0, 0x0, 0x44, 0x0, 0x51, 0x0, 0x0,
    0x0, 0xab, 0x0, 0xdb, 0x1, 0xfe, 0x50, 0x0,
    0x0, 0xe9, 0x0, 0xf8, 0x0, 0x2b, 0xf4, 0x0,
    0x3, 0xf8, 0x45, 0xf8, 0x44, 0x44, 0x84, 0x40,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x22, 0x2b, 0xf2, 0x22, 0x22, 0x22, 0x20,
    0x0, 0x0, 0xf, 0xb2, 0x22, 0x22, 0x20, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0x33, 0x3b, 0xf2, 0x0,
    0x0, 0x4, 0xf9, 0xeb, 0x0, 0x4f, 0x80, 0x0,
    0x0, 0xe, 0xe0, 0x5f, 0xa4, 0xfb, 0x0, 0x0,
    0x0, 0xaf, 0x50, 0x6, 0xff, 0xc0, 0x0, 0x0,
    0x8, 0xf9, 0x0, 0x6d, 0xff, 0xf8, 0x10, 0x0,
    0x4f, 0xc3, 0x9e, 0xfc, 0x41, 0x9f, 0xfb, 0x60,
    0x8, 0x9, 0xfa, 0x40, 0x0, 0x1, 0x8e, 0xe0,
    0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x20,

    /* U+56FA "固" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8f,
    0x55, 0x55, 0x55, 0x55, 0x55, 0xf8, 0x8e, 0x0,
    0x0, 0x95, 0x0, 0x0, 0xf8, 0x8e, 0x16, 0x66,
    0xfb, 0x66, 0x61, 0xf8, 0x8e, 0x4f, 0xff, 0xff,
    0xff, 0xf4, 0xf8, 0x8e, 0x0, 0x0, 0xe8, 0x0,
    0x0, 0xf8, 0x8e, 0x1, 0x55, 0xfa, 0x55, 0x10,
    0xf8, 0x8e, 0x4, 0xfd, 0xdd, 0xdf, 0x30, 0xf8,
    0x8e, 0x4, 0xf0, 0x0, 0x1f, 0x30, 0xf8, 0x8e,
    0x4, 0xf4, 0x33, 0x4f, 0x30, 0xf8, 0x8e, 0x3,
    0xff, 0xff, 0xff, 0x30, 0xf8, 0x8e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf8, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x8e, 0x55, 0x55, 0x55, 0x55,
    0x55, 0xf8,

    /* U+5DF2 "已" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x28,
    0x88, 0x88, 0x88, 0x88, 0x8f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x90, 0xa, 0x70, 0x0,
    0x0, 0x0, 0xf, 0x90, 0xf, 0xa0, 0x0, 0x0,
    0x0, 0xf, 0x90, 0xf, 0xd7, 0x77, 0x77, 0x77,
    0x7f, 0x90, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0xf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0xa9, 0xf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xcc, 0xe, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xdb, 0xc, 0xfa, 0x88, 0x77,
    0x78, 0x9b, 0xf9, 0x3, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0xb1, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0,

    /* U+5F85 "待" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xd3, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0x0, 0x2e, 0xd0, 0x67, 0x7c, 0xe7, 0x77, 0x10,
    0x3, 0xed, 0x10, 0xef, 0xff, 0xff, 0xff, 0x40,
    0x2f, 0xd1, 0x40, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0x8, 0x17, 0xf7, 0x55, 0x5c, 0xe5, 0x55, 0x50,
    0x0, 0x3f, 0x8d, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x1, 0xef, 0x11, 0x11, 0x11, 0x17, 0xf2, 0x10,
    0x1d, 0xff, 0x13, 0x66, 0x66, 0x6a, 0xf6, 0x50,
    0x4f, 0x8f, 0x17, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x2, 0x4f, 0x10, 0x67, 0x0, 0x7, 0xf1, 0x0,
    0x0, 0x4f, 0x10, 0x9f, 0x70, 0x7, 0xf1, 0x0,
    0x0, 0x4f, 0x10, 0x9, 0xd0, 0x7, 0xf1, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0x17, 0x6c, 0xf0, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0xd, 0xff, 0x90, 0x0,

    /* U+64E6 "擦" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x8c, 0x0, 0x0, 0x1d, 0xb0, 0x0, 0x0,
    0x0, 0x8c, 0x8, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xa, 0xde, 0xa8, 0xa7, 0x42, 0x65, 0x27, 0xc0,
    0x8, 0xce, 0x83, 0x6f, 0xdd, 0x9d, 0xbd, 0x90,
    0x0, 0x8c, 0x1, 0xdb, 0x5e, 0x4f, 0x4e, 0x50,
    0x0, 0x8c, 0x1d, 0xac, 0xe7, 0xd, 0xca, 0x0,
    0x1, 0xaf, 0xb8, 0xeb, 0xd1, 0x16, 0xf2, 0x0,
    0x4f, 0xfe, 0x40, 0xcd, 0xcf, 0xff, 0xbd, 0x20,
    0x27, 0x9c, 0x2d, 0xc1, 0x0, 0x0, 0x9, 0xf2,
    0x0, 0x8c, 0x18, 0xad, 0xdd, 0xdd, 0xdd, 0x60,
    0x0, 0x8c, 0x0, 0x39, 0x59, 0xf5, 0x65, 0x0,
    0x0, 0x8c, 0x0, 0x7f, 0x36, 0xe2, 0xf7, 0x0,
    0x16, 0xcc, 0xb, 0xf5, 0x4a, 0xe0, 0x5f, 0xb1,
    0xf, 0xf6, 0xa, 0x20, 0xff, 0x80, 0x2, 0xa0,

    /* U+7B49 "等" */
    0x0, 0x7, 0x40, 0x0, 0x5, 0x60, 0x0, 0x0,
    0x0, 0x4f, 0x83, 0x32, 0x1f, 0xb3, 0x33, 0x30,
    0x0, 0xdf, 0xff, 0xfd, 0x8f, 0xff, 0xff, 0xf0,
    0xa, 0xf3, 0xae, 0x22, 0xe8, 0x1d, 0xb1, 0x10,
    0x1b, 0x40, 0x18, 0x18, 0x60, 0x3, 0x60, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x44, 0x44, 0x4e, 0xc4, 0x44, 0x43, 0x0,
    0x3, 0x33, 0x33, 0x3e, 0xb3, 0x33, 0x33, 0x30,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x1, 0x11, 0x11, 0x11, 0x11, 0xaf, 0x11, 0x10,
    0xa, 0xee, 0xee, 0xee, 0xee, 0xff, 0xee, 0x70,
    0x3, 0x57, 0x95, 0x55, 0x55, 0xbf, 0x55, 0x20,
    0x0, 0x7, 0xfa, 0x10, 0x0, 0x9e, 0x0, 0x0,
    0x0, 0x0, 0x5e, 0x93, 0x44, 0xdd, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7, 0xff, 0xe5, 0x0, 0x0,

    /* U+9001 "送" */
    0x0, 0x20, 0x0, 0x38, 0x0, 0x1, 0xa2, 0x0,
    0x8, 0xf2, 0x0, 0x5f, 0x70, 0xb, 0xe1, 0x0,
    0x1, 0xed, 0x0, 0x8, 0xb0, 0x6f, 0x40, 0x0,
    0x0, 0x3f, 0x65, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x3, 0x2, 0x66, 0x6f, 0xc6, 0x66, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x90, 0x0, 0x0,
    0x4f, 0xff, 0x3, 0x44, 0x4f, 0xa4, 0x44, 0x30,
    0x27, 0xbf, 0xe, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x6f, 0x1, 0x11, 0x6f, 0xa1, 0x11, 0x10,
    0x0, 0x6f, 0x0, 0x0, 0xcf, 0xfa, 0x10, 0x0,
    0x0, 0x6f, 0x0, 0x2c, 0xf6, 0x5f, 0xd3, 0x0,
    0x0, 0x8f, 0x17, 0xff, 0x60, 0x3, 0xef, 0x40,
    0x5, 0xff, 0xd5, 0xa2, 0x0, 0x0, 0x1b, 0x0,
    0x4f, 0xc3, 0xef, 0xc8, 0x76, 0x65, 0x66, 0x61,
    0x3d, 0x10, 0x7, 0xce, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x10, 0x0,

    /* U+9664 "除" */
    0x7f, 0xff, 0xf2, 0x0, 0x3f, 0xd0, 0x0, 0x0,
    0x7f, 0x69, 0xf1, 0x1, 0xe6, 0xaa, 0x0, 0x0,
    0x7e, 0x8, 0xc0, 0x2d, 0xb0, 0x1e, 0xa0, 0x0,
    0x7e, 0xd, 0x75, 0xfc, 0x0, 0x2, 0xec, 0x20,
    0x7e, 0x1f, 0x5f, 0xcf, 0xff, 0xff, 0xce, 0xc0,
    0x7e, 0x4f, 0x3, 0x7, 0x7e, 0xc7, 0x41, 0x20,
    0x7e, 0xd, 0x60, 0x0, 0xc, 0x90, 0x0, 0x0,
    0x7e, 0x7, 0xc7, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x7e, 0x4, 0xf4, 0x77, 0x7e, 0xc7, 0x77, 0x10,
    0x7e, 0x4, 0xf0, 0x6a, 0xc, 0x94, 0x80, 0x0,
    0x7e, 0x6b, 0xe1, 0xea, 0xc, 0x94, 0xf4, 0x0,
    0x7e, 0x8c, 0x5b, 0xe1, 0xc, 0x90, 0xae, 0x10,
    0x7e, 0x0, 0x7f, 0x34, 0x6e, 0x80, 0x1e, 0x70,
    0x7e, 0x0, 0x4, 0x8, 0xfe, 0x30, 0x2, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 256, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 98, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 210, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 338, .adv_w = 256, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 436, .adv_w = 256, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 541, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 661, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 781, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 901, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1029, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0xc9, 0x5a4, 0x8cd, 0xfc5, 0x1158, 0x16b9, 0x2d1c,
    0x41d4, 0x4837
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 20013, .range_length = 18488, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 10, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_customer_font_HarmonyOS_Sans_SC_Medium_16 = {
#else
lv_font_t lv_customer_font_HarmonyOS_Sans_SC_Medium_16 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_CUSTOMER_FONT_HARMONYOS_SANS_SC_MEDIUM_16*/

