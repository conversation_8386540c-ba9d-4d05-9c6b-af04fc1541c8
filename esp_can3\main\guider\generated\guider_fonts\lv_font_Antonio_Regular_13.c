/*
* Copyright (c) 2011-12, vernon adams (<EMAIL>), with Reserved Font Names '<PERSON>'.
* This Font Software is licensed under the SIL Open Font License, Version 1.1.
* And is also available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 13 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_ANTONIO_REGULAR_13
#define LV_FONT_ANTONIO_REGULAR_13 1
#endif

#if LV_FONT_ANTONIO_REGULAR_13

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x60, 0xf5, 0xf, 0x40, 0xe3, 0xd, 0x20,
    0xc2, 0xb, 0x10, 0xa0, 0x9, 0x0, 0x0, 0x8,
    0x30, 0xf6,

    /* U+0022 "\"" */
    0x99, 0x99, 0x78, 0x78, 0x56, 0x56, 0x45, 0x45,
    0x0, 0x0,

    /* U+0023 "#" */
    0x0, 0xb5, 0x5c, 0x0, 0xe, 0x38, 0x90, 0x1,
    0xf0, 0xb6, 0x0, 0x4d, 0xe, 0x30, 0x8e, 0xfe,
    0xfe, 0x0, 0xa7, 0x4d, 0x0, 0xc, 0x46, 0xa0,
    0xd, 0xfe, 0xff, 0x90, 0x2e, 0xc, 0x40, 0x5,
    0xb0, 0xf1, 0x0, 0x88, 0x2e, 0x0, 0xb, 0x55,
    0xb0, 0x0,

    /* U+0024 "$" */
    0x0, 0xb0, 0x0, 0x2d, 0x40, 0x1f, 0xcf, 0x26,
    0xe0, 0xc7, 0x6f, 0xa, 0x82, 0xf6, 0x33, 0x9,
    0xf4, 0x0, 0xb, 0xe1, 0x25, 0x1e, 0x75, 0xe0,
    0xab, 0x4f, 0xa, 0xa0, 0xed, 0xf5, 0x1, 0xc5,
    0x0, 0xa, 0x20,

    /* U+0025 "%" */
    0x5, 0xee, 0x40, 0x0, 0x88, 0x0, 0x0, 0xd4,
    0x5c, 0x0, 0xe, 0x20, 0x0, 0xf, 0x24, 0xe0,
    0x4, 0xc0, 0x0, 0x0, 0xf2, 0x4f, 0x0, 0xa6,
    0x0, 0x0, 0xf, 0x24, 0xf0, 0x1f, 0x16, 0xed,
    0x30, 0xf2, 0x4e, 0x6, 0xa0, 0xf5, 0x9a, 0xe,
    0x45, 0xd0, 0xc5, 0x1f, 0x15, 0xc0, 0x7f, 0xf6,
    0x2e, 0x1, 0xf1, 0x5d, 0x0, 0x0, 0x8, 0x90,
    0x1f, 0x15, 0xd0, 0x0, 0x0, 0xe3, 0x1, 0xf1,
    0x5c, 0x0, 0x0, 0x4d, 0x0, 0xf, 0x27, 0xa0,
    0x0, 0xa, 0x70, 0x0, 0x7e, 0xd3,

    /* U+0026 "&" */
    0x2, 0xde, 0x60, 0x0, 0xa9, 0x2e, 0x0, 0xb,
    0x71, 0xf0, 0x0, 0x8b, 0x6c, 0x0, 0x3, 0xfe,
    0x40, 0x0, 0xe, 0xb0, 0x0, 0x6, 0xfc, 0x3,
    0x1, 0xf6, 0xf4, 0xe1, 0x7d, 0x9, 0xf8, 0x8,
    0xa0, 0x3f, 0x20, 0x6c, 0x1a, 0xf7, 0x0, 0xbf,
    0xa5, 0xd0,

    /* U+0027 "'" */
    0x3e, 0x2d, 0xb, 0x9, 0x1,

    /* U+0028 "(" */
    0x8, 0xf0, 0xf, 0x40, 0x2f, 0x20, 0x2f, 0x20,
    0x2f, 0x20, 0x2f, 0x20, 0x2f, 0x20, 0x2f, 0x20,
    0x2f, 0x20, 0x2f, 0x20, 0x2f, 0x20, 0xf, 0x30,
    0xa, 0xf0, 0x0, 0x0,

    /* U+0029 ")" */
    0x6e, 0x30, 0xaa, 0x8, 0xc0, 0x8c, 0x8, 0xc0,
    0x8c, 0x8, 0xc0, 0x8c, 0x8, 0xc0, 0x8c, 0x8,
    0xc0, 0xaa, 0x6f, 0x40, 0x0,

    /* U+002A "*" */
    0x1, 0x68, 0x10, 0x1d, 0xab, 0xd2, 0x3, 0xef,
    0x30, 0xd, 0x89, 0xd2, 0x0, 0x57, 0x0,

    /* U+002B "+" */
    0x0, 0x20, 0x0, 0xe, 0x0, 0x4e, 0xfe, 0x60,
    0xe, 0x0, 0x0, 0xe0, 0x0,

    /* U+002C "," */
    0x28, 0x34, 0xf6, 0x8, 0x30, 0xb0, 0x5, 0x0,

    /* U+002D "-" */
    0x6e, 0xe6,

    /* U+002E "." */
    0x40, 0xe3,

    /* U+002F "/" */
    0x0, 0xc, 0x60, 0x0, 0xf2, 0x0, 0x3e, 0x0,
    0x7, 0xa0, 0x0, 0xb7, 0x0, 0xe, 0x30, 0x2,
    0xf0, 0x0, 0x6c, 0x0, 0xa, 0x80, 0x0, 0xd4,
    0x0, 0x1f, 0x0, 0x5, 0xd0, 0x0,

    /* U+0030 "0" */
    0x8, 0xee, 0x50, 0x2f, 0x58, 0xe0, 0x5f, 0x3,
    0xf1, 0x5f, 0x2, 0xf2, 0x5f, 0x2, 0xf2, 0x5f,
    0x2, 0xf2, 0x5f, 0x2, 0xf2, 0x5f, 0x2, 0xf2,
    0x5f, 0x2, 0xf2, 0x4f, 0x3, 0xf1, 0x2f, 0x58,
    0xe0, 0x8, 0xee, 0x50,

    /* U+0031 "1" */
    0x4, 0xf7, 0xff, 0x46, 0xf0, 0x5f, 0x5, 0xf0,
    0x5f, 0x5, 0xf0, 0x5f, 0x5, 0xf0, 0x5f, 0x5,
    0xf0, 0x5f,

    /* U+0032 "2" */
    0x3e, 0xe3, 0xba, 0x8b, 0xe5, 0x4e, 0xf5, 0x4f,
    0xc4, 0x6d, 0x0, 0xb9, 0x3, 0xf3, 0xa, 0xa0,
    0x2f, 0x30, 0x8c, 0x0, 0xc8, 0x0, 0xef, 0xfd,

    /* U+0033 "3" */
    0x7, 0xed, 0x40, 0x1f, 0x69, 0xd0, 0x3f, 0x3,
    0xf0, 0x14, 0x3, 0xf1, 0x0, 0x7, 0xe0, 0x0,
    0xbf, 0x50, 0x0, 0x1a, 0xb0, 0x0, 0x4, 0xf0,
    0x3e, 0x3, 0xf1, 0x3f, 0x3, 0xf0, 0x1f, 0x59,
    0xd0, 0x7, 0xed, 0x40,

    /* U+0034 "4" */
    0x0, 0xf, 0x70, 0x0, 0x5f, 0x70, 0x0, 0x9f,
    0x70, 0x0, 0xdc, 0x70, 0x1, 0xcc, 0x70, 0x6,
    0x8c, 0x70, 0xa, 0x4c, 0x70, 0xe, 0x1c, 0x70,
    0x3d, 0xc, 0x70, 0x6f, 0xff, 0xf5, 0x2, 0x2c,
    0x80, 0x0, 0xc, 0x70,

    /* U+0035 "5" */
    0x1f, 0xff, 0x71, 0xf5, 0x31, 0x1f, 0x20, 0x1,
    0xf2, 0x0, 0x1f, 0xde, 0x21, 0xf6, 0xc8, 0x4,
    0x9, 0xa0, 0x0, 0x9b, 0x16, 0x9, 0xb2, 0xf1,
    0x9a, 0x1f, 0x5c, 0x80, 0x8f, 0xd1,

    /* U+0036 "6" */
    0x7, 0xed, 0x30, 0x1f, 0x6a, 0xb0, 0x4f, 0x5,
    0xe0, 0x5f, 0x4, 0xd0, 0x5f, 0x0, 0x0, 0x5f,
    0xbe, 0x70, 0x5f, 0x57, 0xf0, 0x5f, 0x1, 0xf2,
    0x5f, 0x1, 0xf3, 0x4f, 0x1, 0xf2, 0x1f, 0x67,
    0xf0, 0x6, 0xee, 0x60,

    /* U+0037 "7" */
    0x4f, 0xff, 0xf2, 0x2, 0x25, 0xf0, 0x0, 0x9,
    0xb0, 0x0, 0xd, 0x60, 0x0, 0x2f, 0x20, 0x0,
    0x5e, 0x0, 0x0, 0x9b, 0x0, 0x0, 0xd7, 0x0,
    0x0, 0xf4, 0x0, 0x2, 0xf2, 0x0, 0x4, 0xf1,
    0x0, 0x6, 0xf0, 0x0,

    /* U+0038 "8" */
    0x8, 0xee, 0x40, 0x1f, 0x59, 0xd0, 0x3f, 0x15,
    0xf0, 0x3f, 0x15, 0xe0, 0xf, 0x49, 0xb0, 0x9,
    0xff, 0x40, 0xf, 0x59, 0xa0, 0x3f, 0x5, 0xe0,
    0x5f, 0x4, 0xf0, 0x4f, 0x5, 0xf0, 0x1f, 0x59,
    0xc0, 0x7, 0xee, 0x40,

    /* U+0039 "9" */
    0xb, 0xfc, 0x16, 0xe4, 0xd7, 0x8c, 0xa, 0xa9,
    0xb0, 0x9b, 0x8b, 0x9, 0xb6, 0xe0, 0xab, 0x1d,
    0xff, 0xb0, 0x1, 0x9b, 0x4a, 0x9, 0xb4, 0xe0,
    0x9a, 0x2f, 0x4c, 0x70, 0x9f, 0xc1,

    /* U+003A ":" */
    0xf, 0x90, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa5, 0xf, 0x90,

    /* U+003B ";" */
    0x2f, 0xa2, 0xd8, 0x0, 0x0, 0x0, 0xa, 0x71,
    0xfb, 0x5, 0x80, 0x94, 0x5, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x2, 0xc1, 0x6, 0xfa, 0x8,
    0xd4, 0x0, 0x8d, 0x30, 0x0, 0x7f, 0x80, 0x0,
    0x3d, 0x10, 0x0, 0x0,

    /* U+003D "=" */
    0x6e, 0xee, 0x30, 0x11, 0x10, 0x7f, 0xff, 0x30,
    0x11, 0x10,

    /* U+003E ">" */
    0x0, 0x0, 0x7, 0x80, 0x0, 0x2d, 0xc2, 0x0,
    0x8, 0xf2, 0x0, 0x6f, 0x22, 0xcd, 0x30, 0x79,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x7, 0xfc, 0x10, 0xf6, 0xe6, 0x1f, 0x2c, 0x81,
    0xf2, 0xc8, 0x3, 0xd, 0x70, 0x2, 0xf4, 0x4,
    0xeb, 0x0, 0x7b, 0x0, 0x5, 0x70, 0x0, 0x0,
    0x0, 0x7, 0xa0, 0x0, 0x7a, 0x0,

    /* U+0040 "@" */
    0x0, 0x4c, 0xfe, 0x80, 0x0, 0x4e, 0x40, 0x1a,
    0x70, 0xc, 0x40, 0x0, 0xd, 0x1, 0xe0, 0xae,
    0x96, 0xb2, 0x4b, 0x3c, 0x1a, 0x69, 0x46, 0xa5,
    0x80, 0x86, 0x85, 0x69, 0x58, 0x8, 0x69, 0x45,
    0xa3, 0xc0, 0xb7, 0xb2, 0x3c, 0x9, 0xe3, 0xc9,
    0x0, 0xe1, 0x0, 0x0, 0x0, 0x6, 0xc1, 0x0,
    0x0, 0x0, 0x7, 0xee, 0xa0, 0x0,

    /* U+0041 "A" */
    0x0, 0xfc, 0x0, 0x1, 0xfe, 0x0, 0x3, 0xef,
    0x10, 0x6, 0xce, 0x30, 0x8, 0xac, 0x60, 0xa,
    0x8a, 0x80, 0xc, 0x68, 0xb0, 0xf, 0x46, 0xd0,
    0x1f, 0x23, 0xf0, 0x3f, 0xff, 0xf2, 0x5e, 0x22,
    0xf5, 0x8c, 0x0, 0xc7,

    /* U+0042 "B" */
    0xff, 0xe8, 0xf, 0x66, 0xf1, 0xf5, 0x1f, 0x3f,
    0x51, 0xf3, 0xf5, 0x3f, 0x1f, 0xff, 0xa0, 0xf6,
    0x4f, 0x1f, 0x50, 0xf5, 0xf5, 0xe, 0x6f, 0x50,
    0xf5, 0xf6, 0x4f, 0x2f, 0xff, 0x80,

    /* U+0043 "C" */
    0x5, 0xee, 0x50, 0xf, 0x88, 0xe0, 0x2f, 0x23,
    0xf1, 0x3f, 0x12, 0xf2, 0x3f, 0x11, 0xa1, 0x3f,
    0x10, 0x0, 0x3f, 0x10, 0x0, 0x3f, 0x12, 0xd1,
    0x3f, 0x12, 0xf2, 0x2f, 0x23, 0xf0, 0xe, 0x88,
    0xd0, 0x5, 0xee, 0x40,

    /* U+0044 "D" */
    0x1f, 0xfe, 0x60, 0x1f, 0x47, 0xf0, 0x1f, 0x31,
    0xf2, 0x1f, 0x30, 0xf3, 0x1f, 0x30, 0xf4, 0x1f,
    0x30, 0xf4, 0x1f, 0x30, 0xf4, 0x1f, 0x30, 0xf4,
    0x1f, 0x30, 0xf4, 0x1f, 0x31, 0xf3, 0x1f, 0x46,
    0xf0, 0x1f, 0xfe, 0x60,

    /* U+0045 "E" */
    0x1f, 0xff, 0x41, 0xf5, 0x20, 0x1f, 0x30, 0x1,
    0xf3, 0x0, 0x1f, 0x30, 0x1, 0xff, 0xf2, 0x1f,
    0x52, 0x1, 0xf3, 0x0, 0x1f, 0x30, 0x1, 0xf3,
    0x0, 0x1f, 0x52, 0x1, 0xff, 0xf4,

    /* U+0046 "F" */
    0x1f, 0xff, 0x41, 0xf5, 0x20, 0x1f, 0x30, 0x1,
    0xf3, 0x0, 0x1f, 0x30, 0x1, 0xff, 0xf2, 0x1f,
    0x52, 0x1, 0xf3, 0x0, 0x1f, 0x30, 0x1, 0xf3,
    0x0, 0x1f, 0x30, 0x1, 0xf3, 0x0,

    /* U+0047 "G" */
    0x5, 0xee, 0x50, 0xe, 0x87, 0xe0, 0x2f, 0x22,
    0xf1, 0x3f, 0x11, 0xf2, 0x3f, 0x11, 0xa1, 0x3f,
    0x10, 0x0, 0x3f, 0x1e, 0xe2, 0x3f, 0x12, 0xf2,
    0x3f, 0x10, 0xf2, 0x2f, 0x21, 0xf2, 0xe, 0x65,
    0xf2, 0x5, 0xec, 0xb2,

    /* U+0048 "H" */
    0x1f, 0x30, 0xf5, 0x1f, 0x30, 0xf5, 0x1f, 0x30,
    0xf5, 0x1f, 0x30, 0xf5, 0x1f, 0x30, 0xf5, 0x1f,
    0xff, 0xf5, 0x1f, 0x52, 0xf5, 0x1f, 0x30, 0xf5,
    0x1f, 0x30, 0xf5, 0x1f, 0x30, 0xf5, 0x1f, 0x30,
    0xf5, 0x1f, 0x30, 0xf5,

    /* U+0049 "I" */
    0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5, 0xf5,
    0xf5, 0xf5, 0xf5, 0xf5,

    /* U+004A "J" */
    0x0, 0xc, 0x90, 0x0, 0xc9, 0x0, 0xc, 0x90,
    0x0, 0xc9, 0x0, 0xc, 0x90, 0x0, 0xc9, 0x0,
    0xc, 0x92, 0x30, 0xc9, 0x7c, 0xc, 0x87, 0xc0,
    0xc8, 0x4f, 0x4e, 0x50, 0xaf, 0xb0,

    /* U+004B "K" */
    0x1f, 0x30, 0xe5, 0x1f, 0x34, 0xf0, 0x1f, 0x3a,
    0xa0, 0x1f, 0x4f, 0x40, 0x1f, 0x9e, 0x0, 0x1f,
    0xe9, 0x0, 0x1f, 0xea, 0x0, 0x1f, 0x8f, 0x10,
    0x1f, 0x4e, 0x60, 0x1f, 0x39, 0xc0, 0x1f, 0x33,
    0xf2, 0x1f, 0x30, 0xd8,

    /* U+004C "L" */
    0x1f, 0x30, 0x1, 0xf3, 0x0, 0x1f, 0x30, 0x1,
    0xf3, 0x0, 0x1f, 0x30, 0x1, 0xf3, 0x0, 0x1f,
    0x30, 0x1, 0xf3, 0x0, 0x1f, 0x30, 0x1, 0xf3,
    0x0, 0x1f, 0x52, 0x1, 0xff, 0xf4,

    /* U+004D "M" */
    0x1f, 0xa0, 0x6, 0xf5, 0x1f, 0xc0, 0x8, 0xf5,
    0x1f, 0xf0, 0xb, 0xf5, 0x1f, 0xe1, 0xd, 0xe5,
    0x1f, 0xb4, 0xf, 0xc5, 0x1f, 0x97, 0x2c, 0xd5,
    0x1f, 0x79, 0x5a, 0xd5, 0x1f, 0x4c, 0x77, 0xd5,
    0x1f, 0x2e, 0xa4, 0xd5, 0x1f, 0x1e, 0xe2, 0xd5,
    0x1f, 0x1b, 0xf0, 0xd5, 0x1f, 0x18, 0xd0, 0xd5,

    /* U+004E "N" */
    0x1f, 0x10, 0xa8, 0x1f, 0x60, 0xa8, 0x1f, 0xb0,
    0xa8, 0x1f, 0xf0, 0xa8, 0x1f, 0xc5, 0xa8, 0x1f,
    0x8a, 0x98, 0x1f, 0x3f, 0x98, 0x1f, 0x2d, 0xd8,
    0x1f, 0x28, 0xf8, 0x1f, 0x23, 0xf8, 0x1f, 0x20,
    0xe8, 0x1f, 0x20, 0x98,

    /* U+004F "O" */
    0x5, 0xee, 0x60, 0xf, 0x87, 0xf0, 0x2f, 0x22,
    0xf2, 0x3f, 0x11, 0xf3, 0x3f, 0x11, 0xf3, 0x3f,
    0x11, 0xf3, 0x3f, 0x11, 0xf3, 0x3f, 0x11, 0xf3,
    0x3f, 0x11, 0xf3, 0x2f, 0x22, 0xf2, 0xe, 0x78,
    0xf0, 0x5, 0xee, 0x50,

    /* U+0050 "P" */
    0x1f, 0xff, 0x70, 0x1f, 0x46, 0xf1, 0x1f, 0x31,
    0xf3, 0x1f, 0x30, 0xf4, 0x1f, 0x31, 0xf3, 0x1f,
    0x34, 0xf1, 0x1f, 0xff, 0x90, 0x1f, 0x51, 0x0,
    0x1f, 0x30, 0x0, 0x1f, 0x30, 0x0, 0x1f, 0x30,
    0x0, 0x1f, 0x30, 0x0,

    /* U+0051 "Q" */
    0x5, 0xee, 0x60, 0xf, 0x87, 0xf0, 0x2f, 0x22,
    0xf2, 0x3f, 0x11, 0xf3, 0x3f, 0x11, 0xf3, 0x3f,
    0x11, 0xf3, 0x3f, 0x11, 0xf3, 0x3f, 0x11, 0xf3,
    0x3f, 0x11, 0xf3, 0x2f, 0x22, 0xf2, 0xf, 0x77,
    0xe0, 0x6, 0xef, 0x60, 0x0, 0xc, 0x60, 0x0,
    0x1, 0x50,

    /* U+0052 "R" */
    0x1f, 0xff, 0x90, 0x1f, 0x45, 0xf2, 0x1f, 0x30,
    0xf3, 0x1f, 0x31, 0xf3, 0x1f, 0x33, 0xf1, 0x1f,
    0xff, 0xa0, 0x1f, 0x55, 0xf0, 0x1f, 0x30, 0xf2,
    0x1f, 0x30, 0xf3, 0x1f, 0x30, 0xf4, 0x1f, 0x30,
    0xf4, 0x1f, 0x30, 0xf4,

    /* U+0053 "S" */
    0x8, 0xed, 0x30, 0x2f, 0x5a, 0xb0, 0x4f, 0x6,
    0xd0, 0x3f, 0x16, 0xd0, 0xd, 0xb1, 0x10, 0x3,
    0xfa, 0x0, 0x0, 0x4f, 0x50, 0x2a, 0x9, 0xd0,
    0x4f, 0x4, 0xf0, 0x3f, 0x4, 0xf0, 0xf, 0x69,
    0xd0, 0x7, 0xfe, 0x40,

    /* U+0054 "T" */
    0xdf, 0xfd, 0x1a, 0xc1, 0x9, 0xb0, 0x9, 0xb0,
    0x9, 0xb0, 0x9, 0xb0, 0x9, 0xb0, 0x9, 0xb0,
    0x9, 0xb0, 0x9, 0xb0, 0x9, 0xb0, 0x9, 0xb0,

    /* U+0055 "U" */
    0x2f, 0x20, 0xf4, 0x2f, 0x20, 0xf4, 0x2f, 0x20,
    0xf4, 0x2f, 0x20, 0xf4, 0x2f, 0x20, 0xf4, 0x2f,
    0x20, 0xf4, 0x2f, 0x20, 0xf4, 0x2f, 0x20, 0xf4,
    0x2f, 0x20, 0xf4, 0x1f, 0x31, 0xf3, 0xe, 0x76,
    0xf1, 0x5, 0xee, 0x70,

    /* U+0056 "V" */
    0x8b, 0x0, 0xd6, 0x6d, 0x0, 0xf4, 0x3f, 0x1,
    0xf1, 0x1f, 0x23, 0xf0, 0xe, 0x45, 0xd0, 0xc,
    0x67, 0xa0, 0x9, 0x99, 0x80, 0x7, 0xbb, 0x60,
    0x4, 0xdd, 0x40, 0x2, 0xff, 0x10, 0x0, 0xff,
    0x0, 0x0, 0xdd, 0x0,

    /* U+0057 "W" */
    0x8a, 0x3, 0xf0, 0xf, 0x26, 0xc0, 0x5f, 0x11,
    0xf1, 0x4e, 0x8, 0xf4, 0x3f, 0x2, 0xf0, 0xae,
    0x64, 0xd0, 0xf, 0x1c, 0x98, 0x6b, 0x0, 0xe3,
    0xe5, 0xb7, 0x90, 0xc, 0x5e, 0x2d, 0x88, 0x0,
    0xa9, 0xc0, 0xfa, 0x60, 0x8, 0xda, 0xd, 0xd4,
    0x0, 0x6f, 0x80, 0xbf, 0x20, 0x4, 0xf5, 0x9,
    0xf1, 0x0, 0x3f, 0x30, 0x7f, 0x0,

    /* U+0058 "X" */
    0x7a, 0x8, 0xa2, 0xf0, 0xc6, 0xe, 0x4f, 0x20,
    0xab, 0xe0, 0x5, 0xfa, 0x0, 0x1f, 0x60, 0x4,
    0xf5, 0x0, 0x8f, 0x90, 0xc, 0xad, 0x0, 0xf2,
    0xf2, 0x4e, 0xb, 0x68, 0xa0, 0x7b,

    /* U+0059 "Y" */
    0xb8, 0x3, 0xf1, 0x7c, 0x7, 0xc0, 0x2f, 0x1b,
    0x80, 0xe, 0x5e, 0x40, 0xa, 0xcf, 0x0, 0x5,
    0xfc, 0x0, 0x1, 0xf8, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0xf6, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0xf6, 0x0,

    /* U+005A "Z" */
    0x1f, 0xff, 0x60, 0x23, 0xf4, 0x0, 0x4f, 0x0,
    0x8, 0xd0, 0x0, 0xc9, 0x0, 0xf, 0x50, 0x3,
    0xf1, 0x0, 0x7e, 0x0, 0xb, 0xa0, 0x0, 0xf6,
    0x0, 0x2f, 0x42, 0x4, 0xff, 0xf5,

    /* U+005B "[" */
    0xaf, 0xf0, 0xab, 0x0, 0xab, 0x0, 0xab, 0x0,
    0xab, 0x0, 0xab, 0x0, 0xab, 0x0, 0xab, 0x0,
    0xab, 0x0, 0xab, 0x0, 0xab, 0x0, 0xaf, 0xf0,

    /* U+005C "\\" */
    0x2f, 0x0, 0x0, 0xe3, 0x0, 0xb, 0x60, 0x0,
    0x8a, 0x0, 0x4, 0xd0, 0x0, 0x1f, 0x10, 0x0,
    0xd4, 0x0, 0xa, 0x80, 0x0, 0x6b, 0x0, 0x3,
    0xe0, 0x0, 0xf, 0x20, 0x0, 0xc6,

    /* U+005D "]" */
    0xff, 0xb0, 0xab, 0x9, 0xb0, 0x9b, 0x9, 0xb0,
    0x9b, 0x9, 0xb0, 0x9b, 0x9, 0xb0, 0x9b, 0xa,
    0xbf, 0xfb,

    /* U+005E "^" */
    0x0, 0xbf, 0x30, 0x0, 0xec, 0x70, 0x3, 0xf6,
    0xb0, 0x7, 0xb3, 0xf0, 0xb, 0x80, 0xf3, 0xf,
    0x40, 0xc7,

    /* U+005F "_" */
    0xbf, 0xff, 0x70,

    /* U+0060 "`" */
    0x23, 0x0, 0x2f, 0xa0, 0x0, 0x30,

    /* U+0061 "a" */
    0x5, 0xee, 0x50, 0xe, 0x78, 0xd0, 0xf, 0x34,
    0xf0, 0x4, 0x6, 0xf0, 0x3, 0xcd, 0xf0, 0xe,
    0x64, 0xf0, 0x3f, 0x14, 0xf0, 0x3f, 0x14, 0xf0,
    0x1f, 0x47, 0xf0, 0x9, 0xeb, 0xf0,

    /* U+0062 "b" */
    0x1f, 0x30, 0x0, 0x1f, 0x30, 0x0, 0x1f, 0xaf,
    0x80, 0x1f, 0x97, 0xf0, 0x1f, 0x42, 0xf2, 0x1f,
    0x32, 0xf2, 0x1f, 0x32, 0xf2, 0x1f, 0x32, 0xf2,
    0x1f, 0x32, 0xf2, 0x1f, 0x42, 0xf2, 0x1f, 0x86,
    0xf0, 0x1f, 0xae, 0x80,

    /* U+0063 "c" */
    0x6, 0xee, 0x40, 0xf7, 0xac, 0x2f, 0x15, 0xe3,
    0xf1, 0x4a, 0x3f, 0x10, 0x3, 0xf1, 0x0, 0x3f,
    0x15, 0xc2, 0xf1, 0x6e, 0xf, 0x6a, 0xc0, 0x6f,
    0xe4,

    /* U+0064 "d" */
    0x0, 0x4, 0xf0, 0x0, 0x4, 0xf0, 0x7, 0xeb,
    0xf0, 0xf, 0x79, 0xf0, 0x3f, 0x25, 0xf0, 0x3f,
    0x14, 0xf0, 0x3f, 0x14, 0xf0, 0x3f, 0x14, 0xf0,
    0x3f, 0x14, 0xf0, 0x2f, 0x14, 0xf0, 0x1f, 0x58,
    0xf0, 0x9, 0xfb, 0xf0,

    /* U+0065 "e" */
    0x6, 0xee, 0x40, 0xf6, 0x9c, 0x2f, 0x15, 0xe3,
    0xf1, 0x5e, 0x3f, 0xee, 0xe3, 0xf1, 0x0, 0x3f,
    0x14, 0xc2, 0xf1, 0x6d, 0xf, 0x69, 0xb0, 0x7e,
    0xe4,

    /* U+0066 "f" */
    0x5, 0xfa, 0xb, 0xa0, 0x8f, 0xfb, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,

    /* U+0067 "g" */
    0x7, 0xeb, 0xf0, 0xf6, 0x9f, 0x2f, 0x15, 0xf3,
    0xf1, 0x4f, 0x3f, 0x14, 0xf3, 0xf1, 0x4f, 0x3f,
    0x15, 0xf1, 0xf4, 0x7f, 0xa, 0xfd, 0xf0, 0x1,
    0x5f, 0x7, 0x19, 0xd1, 0xcf, 0xd4,

    /* U+0068 "h" */
    0x1f, 0x30, 0x0, 0x1f, 0x30, 0x0, 0x1f, 0x9e,
    0xb0, 0x1f, 0x95, 0xf3, 0x1f, 0x30, 0xf4, 0x1f,
    0x30, 0xf4, 0x1f, 0x30, 0xf4, 0x1f, 0x30, 0xf4,
    0x1f, 0x30, 0xf4, 0x1f, 0x30, 0xf4, 0x1f, 0x30,
    0xf4, 0x1f, 0x30, 0xf4,

    /* U+0069 "i" */
    0xf, 0x40, 0x30, 0xf, 0x40, 0xf4, 0xf, 0x40,
    0xf4, 0xf, 0x40, 0xf4, 0xf, 0x40, 0xf4, 0xf,
    0x40, 0xf4,

    /* U+006A "j" */
    0xd, 0x70, 0x31, 0xd, 0x70, 0xd7, 0xd, 0x70,
    0xd7, 0xd, 0x70, 0xd7, 0xd, 0x70, 0xd7, 0xd,
    0x70, 0xd7, 0xd, 0x68, 0xf2,

    /* U+006B "k" */
    0x1f, 0x30, 0x0, 0x1f, 0x30, 0x0, 0x1f, 0x31,
    0xf3, 0x1f, 0x37, 0xd0, 0x1f, 0x3d, 0x60, 0x1f,
    0x8f, 0x0, 0x1f, 0xe9, 0x0, 0x1f, 0xda, 0x0,
    0x1f, 0x7f, 0x10, 0x1f, 0x3d, 0x70, 0x1f, 0x36,
    0xe0, 0x1f, 0x30, 0xf5,

    /* U+006C "l" */
    0x1f, 0x31, 0xf3, 0x1f, 0x31, 0xf3, 0x1f, 0x31,
    0xf3, 0x1f, 0x31, 0xf3, 0x1f, 0x31, 0xf3, 0x1f,
    0x31, 0xf3,

    /* U+006D "m" */
    0x1f, 0x8f, 0xa7, 0xfa, 0x1, 0xf9, 0x6f, 0x95,
    0xf2, 0x1f, 0x31, 0xf3, 0x1f, 0x31, 0xf3, 0x1f,
    0x31, 0xf3, 0x1f, 0x31, 0xf3, 0x1f, 0x31, 0xf3,
    0x1f, 0x31, 0xf3, 0x1f, 0x31, 0xf3, 0x1f, 0x31,
    0xf3, 0x1f, 0x31, 0xf3, 0x1f, 0x31, 0xf3, 0x1f,
    0x31, 0xf3, 0x1f, 0x31, 0xf3,

    /* U+006E "n" */
    0x1f, 0x9e, 0xb0, 0x1f, 0x85, 0xf3, 0x1f, 0x30,
    0xf4, 0x1f, 0x30, 0xf4, 0x1f, 0x30, 0xf4, 0x1f,
    0x30, 0xf4, 0x1f, 0x30, 0xf4, 0x1f, 0x30, 0xf4,
    0x1f, 0x30, 0xf4, 0x1f, 0x30, 0xf4,

    /* U+006F "o" */
    0x6, 0xee, 0x40, 0xf, 0x69, 0xd0, 0x2f, 0x15,
    0xf0, 0x3f, 0x14, 0xf0, 0x3f, 0x14, 0xf0, 0x3f,
    0x14, 0xf0, 0x3f, 0x14, 0xf0, 0x2f, 0x15, 0xf0,
    0xf, 0x69, 0xd0, 0x6, 0xfe, 0x40,

    /* U+0070 "p" */
    0x1f, 0xaf, 0x80, 0x1f, 0x86, 0xf0, 0x1f, 0x42,
    0xf2, 0x1f, 0x32, 0xf2, 0x1f, 0x32, 0xf2, 0x1f,
    0x32, 0xf2, 0x1f, 0x32, 0xf2, 0x1f, 0x42, 0xf2,
    0x1f, 0x87, 0xf0, 0x1f, 0xbe, 0x70, 0x1f, 0x30,
    0x0, 0x1f, 0x30, 0x0,

    /* U+0071 "q" */
    0x9, 0xeb, 0xf0, 0x1f, 0x69, 0xf0, 0x3f, 0x15,
    0xf0, 0x3f, 0x14, 0xf0, 0x3f, 0x14, 0xf0, 0x3f,
    0x14, 0xf0, 0x3f, 0x14, 0xf0, 0x2f, 0x15, 0xf0,
    0xf, 0x69, 0xf0, 0x7, 0xeb, 0xf0, 0x0, 0x4,
    0xf0, 0x0, 0x4, 0xf0,

    /* U+0072 "r" */
    0x1f, 0x8d, 0x1f, 0x91, 0x1f, 0x30, 0x1f, 0x30,
    0x1f, 0x30, 0x1f, 0x30, 0x1f, 0x30, 0x1f, 0x30,
    0x1f, 0x30, 0x1f, 0x30,

    /* U+0073 "s" */
    0xa, 0xfc, 0x5, 0xf3, 0xe6, 0x6e, 0xb, 0x83,
    0xf4, 0x54, 0x9, 0xe3, 0x0, 0xa, 0xe1, 0x37,
    0xe, 0x85, 0xe0, 0xab, 0x2f, 0x4c, 0x90, 0x9f,
    0xd2,

    /* U+0074 "t" */
    0xd, 0x70, 0xd, 0x70, 0xbf, 0xf7, 0xd, 0x70,
    0xd, 0x70, 0xd, 0x70, 0xd, 0x70, 0xd, 0x70,
    0xd, 0x70, 0xd, 0x70, 0xd, 0x90, 0x8, 0xf8,

    /* U+0075 "u" */
    0x2f, 0x21, 0xf3, 0x2f, 0x21, 0xf3, 0x2f, 0x21,
    0xf3, 0x2f, 0x21, 0xf3, 0x2f, 0x21, 0xf3, 0x2f,
    0x21, 0xf3, 0x2f, 0x21, 0xf3, 0x1f, 0x21, 0xf3,
    0xf, 0x66, 0xf3, 0x8, 0xf9, 0xf3,

    /* U+0076 "v" */
    0xa8, 0xb, 0x78, 0xa0, 0xd4, 0x6c, 0xf, 0x24,
    0xe1, 0xf0, 0x2f, 0x3e, 0x0, 0xf7, 0xc0, 0xd,
    0xaa, 0x0, 0xbd, 0x70, 0x9, 0xf5, 0x0, 0x7f,
    0x30,

    /* U+0077 "w" */
    0x97, 0xf, 0x51, 0xf0, 0x88, 0x1f, 0x72, 0xd0,
    0x6a, 0x3e, 0x94, 0xb0, 0x4b, 0x5a, 0xb5, 0xa0,
    0x2d, 0x86, 0xd7, 0x80, 0x1e, 0xa3, 0xd9, 0x60,
    0xe, 0xc1, 0xbc, 0x40, 0xd, 0xe0, 0xaf, 0x20,
    0xb, 0xc0, 0x8f, 0x0, 0x9, 0xa0, 0x6f, 0x0,

    /* U+0078 "x" */
    0x99, 0xd, 0x54, 0xe1, 0xf0, 0xf, 0x9b, 0x0,
    0xaf, 0x60, 0x5, 0xf1, 0x0, 0x7f, 0x20, 0xb,
    0xf7, 0x1, 0xf7, 0xc0, 0x5c, 0x1f, 0x1a, 0x70,
    0xc6,

    /* U+0079 "y" */
    0x9a, 0x8, 0xb7, 0xc0, 0xa9, 0x4e, 0xb, 0x62,
    0xf0, 0xd4, 0xf, 0x2f, 0x10, 0xc6, 0xf0, 0xa,
    0x9d, 0x0, 0x7c, 0xa0, 0x5, 0xf8, 0x0, 0x2f,
    0x50, 0x2, 0xf3, 0x5, 0xfd, 0x0,

    /* U+007A "z" */
    0x5f, 0xfe, 0x2, 0x9c, 0x0, 0xc8, 0x0, 0xf3,
    0x5, 0xf0, 0x9, 0xb0, 0xd, 0x70, 0x1f, 0x20,
    0x6f, 0x22, 0x8f, 0xfd,

    /* U+007B "{" */
    0x8, 0xf1, 0xf, 0x60, 0xf, 0x30, 0x1f, 0x30,
    0x1f, 0x30, 0x5f, 0x20, 0xfa, 0x0, 0x7f, 0x10,
    0x1f, 0x30, 0x1f, 0x30, 0xf, 0x40, 0xb, 0xf1,
    0x0, 0x10,

    /* U+007C "|" */
    0xe5, 0xe5, 0xe5, 0xe5, 0xe5, 0xe5, 0xe5, 0xe5,
    0xe5, 0xe5, 0xe5, 0xe5, 0xe5,

    /* U+007D "}" */
    0xea, 0x0, 0x4f, 0x20, 0x1f, 0x30, 0x1f, 0x30,
    0xf, 0x30, 0xf, 0x70, 0x8, 0xf1, 0xf, 0x90,
    0xf, 0x30, 0x1f, 0x30, 0x2f, 0x20, 0xfd, 0x0,
    0x10, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x1, 0x0, 0xcf, 0x93, 0xe2, 0x2d,
    0x6c, 0xfb, 0x0, 0x10, 0x3, 0x10,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xaf, 0xf0, 0x0, 0x0,
    0x3, 0x7c, 0xff, 0xff, 0xf0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xb, 0xff, 0xff,
    0xfe, 0xac, 0xf0, 0x0, 0xb, 0xff, 0xd8, 0x30,
    0x9, 0xf0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x9,
    0xf0, 0x0, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf0, 0x0,
    0xb, 0xe0, 0x0, 0x3d, 0xff, 0xf0, 0x3, 0x6c,
    0xe0, 0x0, 0xcf, 0xff, 0xf0, 0xaf, 0xff, 0xe0,
    0x0, 0x5f, 0xff, 0x80, 0xdf, 0xff, 0xd0, 0x0,
    0x0, 0x21, 0x0, 0x2a, 0xda, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0x80, 0x9c, 0xcc, 0xcc, 0xcc, 0x40, 0x8f, 0xdf,
    0xb7, 0x77, 0x77, 0xfe, 0xdf, 0xd0, 0xc7, 0x0,
    0x0, 0xd, 0x50, 0xde, 0x8e, 0x70, 0x0, 0x0,
    0xdb, 0x8e, 0xe5, 0xdb, 0x77, 0x77, 0x7f, 0x95,
    0xed, 0x1c, 0xeb, 0xbb, 0xbb, 0xf6, 0x1d, 0xfc,
    0xf7, 0x0, 0x0, 0xd, 0xdc, 0xfd, 0xc, 0x70,
    0x0, 0x0, 0xd5, 0xd, 0xfa, 0xe9, 0x33, 0x33,
    0x3e, 0xca, 0xfc, 0x3d, 0xff, 0xff, 0xff, 0xf8,
    0x3c,

    /* U+F00B "" */
    0x35, 0x52, 0x5, 0x55, 0x55, 0x55, 0x3f, 0xff,
    0xc6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xfc, 0xee, 0x94, 0xee, 0xee,
    0xee, 0xec, 0x34, 0x42, 0x4, 0x44, 0x44, 0x44,
    0x3f, 0xff, 0xc6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x6f, 0xff, 0xff, 0xff, 0xfc, 0xee, 0x94,
    0xee, 0xee, 0xee, 0xec, 0x34, 0x42, 0x4, 0x44,
    0x44, 0x44, 0x3f, 0xff, 0xc6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x6f, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xa4, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x90, 0x50, 0x0, 0x0, 0x2e,
    0xff, 0x90, 0xaf, 0xc0, 0x0, 0x2e, 0xff, 0x90,
    0xc, 0xff, 0xc0, 0x2e, 0xff, 0x90, 0x0, 0x1c,
    0xff, 0xce, 0xff, 0x90, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x90, 0x0, 0x0,
    0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x6,
    0xf8, 0xdf, 0xf5, 0x6, 0xff, 0xc2, 0xef, 0xf9,
    0xff, 0xd1, 0x2, 0xef, 0xff, 0xd1, 0x0, 0x8,
    0xff, 0xf7, 0x0, 0x6, 0xff, 0xff, 0xf5, 0x6,
    0xff, 0xe4, 0xef, 0xf5, 0xef, 0xe1, 0x2, 0xef,
    0xd4, 0xa1, 0x0, 0x2, 0xb4,

    /* U+F011 "" */
    0x0, 0x0, 0x2, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xbf, 0x40, 0x30, 0x0, 0x1, 0xcf, 0x1b,
    0xf4, 0x7f, 0x80, 0x0, 0xbf, 0xd0, 0xbf, 0x45,
    0xff, 0x40, 0x4f, 0xe1, 0xb, 0xf4, 0x7, 0xfd,
    0x9, 0xf8, 0x0, 0xbf, 0x40, 0xe, 0xf2, 0xcf,
    0x40, 0xb, 0xf4, 0x0, 0xbf, 0x4b, 0xf4, 0x0,
    0x9e, 0x30, 0xb, 0xf4, 0x9f, 0x80, 0x0, 0x0,
    0x0, 0xff, 0x23, 0xff, 0x20, 0x0, 0x0, 0x8f,
    0xc0, 0xa, 0xfe, 0x40, 0x1, 0x8f, 0xf4, 0x0,
    0xc, 0xff, 0xed, 0xff, 0xf6, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x10, 0x3f,
    0xff, 0x70, 0x11, 0x0, 0x7f, 0xbf, 0xff, 0xff,
    0xce, 0xb0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x53, 0xff, 0xff, 0x82, 0x5f, 0xff, 0xf7, 0x3,
    0xff, 0xc0, 0x0, 0x7f, 0xf8, 0x0, 0x3f, 0xfa,
    0x0, 0x6, 0xff, 0x70, 0x2c, 0xff, 0xf4, 0x1,
    0xdf, 0xfe, 0x42, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xf6, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x5, 0x7, 0xff, 0xfa, 0x13, 0x20, 0x0, 0x0,
    0xf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x46,
    0x50, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x25, 0x0, 0x36, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xfc, 0x1a, 0xf6, 0x0, 0x0,
    0x0, 0x7f, 0xe9, 0xfe, 0xcf, 0x60, 0x0, 0x0,
    0xaf, 0xc3, 0x64, 0xff, 0xf6, 0x0, 0x1, 0xcf,
    0xa3, 0xef, 0xa3, 0xdf, 0x90, 0x3, 0xef, 0x75,
    0xff, 0xff, 0xc3, 0xcf, 0xb0, 0xdf, 0x48, 0xff,
    0xff, 0xff, 0xe3, 0x9f, 0x72, 0x27, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x50, 0x0, 0x9f, 0xff, 0xcb,
    0xef, 0xff, 0x30, 0x0, 0x9, 0xff, 0xf1, 0x7,
    0xff, 0xf3, 0x0, 0x0, 0x9f, 0xff, 0x10, 0x7f,
    0xff, 0x30, 0x0, 0x7, 0xff, 0xe0, 0x6, 0xff,
    0xf2, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x4, 0x54, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x47, 0x8f, 0xff, 0x87,
    0x40, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0, 0x0, 0x46,
    0x66, 0x3a, 0xfa, 0x36, 0x66, 0x50, 0xff, 0xff,
    0xf4, 0x54, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xb8, 0xf2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa0,

    /* U+F01C "" */
    0x0, 0x8, 0xaa, 0xaa, 0xaa, 0xa4, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x8f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x3f, 0xfe, 0xee,
    0x40, 0x0, 0x9e, 0xee, 0xf9, 0xff, 0xff, 0xfc,
    0x44, 0x5f, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x7, 0x90, 0x0,
    0x6d, 0xff, 0xfb, 0x40, 0xcf, 0x0, 0xbf, 0xfd,
    0xbd, 0xff, 0xac, 0xf0, 0xbf, 0xd3, 0x0, 0x3,
    0xcf, 0xff, 0x4f, 0xd1, 0x0, 0x7, 0xaa, 0xff,
    0xf9, 0xf5, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x1,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x4f, 0x9f, 0xff, 0xab, 0x70, 0x0, 0xd,
    0xf4, 0xff, 0xfb, 0x20, 0x0, 0x3c, 0xfb, 0xf,
    0xcb, 0xff, 0xca, 0xcf, 0xfc, 0x10, 0xfc, 0x5,
    0xcf, 0xff, 0xd7, 0x0, 0x9, 0x70, 0x0, 0x12,
    0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x5, 0x20, 0x0, 0x6, 0xf7, 0x1,
    0x16, 0xff, 0x8f, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0x8a, 0xcc, 0xef, 0xf8, 0x0, 0x2, 0xef, 0x80,
    0x0, 0x2, 0xe7, 0x0, 0x0, 0x1, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0, 0x6f,
    0x70, 0x0, 0x1, 0x16, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x82, 0xb1, 0xff, 0xff, 0xff, 0x80,
    0xc9, 0xff, 0xff, 0xff, 0x80, 0x9b, 0xff, 0xff,
    0xff, 0x82, 0xf3, 0xbd, 0xdf, 0xff, 0x80, 0x10,
    0x0, 0x3, 0xef, 0x80, 0x0, 0x0, 0x0, 0x3e,
    0x70, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xc2, 0x0, 0x0,
    0x0, 0x5, 0x20, 0x0, 0xb, 0xe1, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0xb7, 0xc, 0xb0, 0x1, 0x16,
    0xff, 0x80, 0x5, 0xf5, 0x2f, 0x3f, 0xff, 0xff,
    0xf8, 0x2c, 0x17, 0xd0, 0xb8, 0xff, 0xff, 0xff,
    0x80, 0xba, 0x2f, 0x18, 0xbf, 0xff, 0xff, 0xf8,
    0x9, 0xb1, 0xf2, 0x7b, 0xff, 0xff, 0xff, 0x82,
    0xf3, 0x5e, 0xa, 0x9a, 0xcc, 0xef, 0xf8, 0x0,
    0x2e, 0x80, 0xe4, 0x0, 0x2, 0xef, 0x80, 0xc,
    0xa0, 0x8d, 0x0, 0x0, 0x2, 0xe7, 0x0, 0x10,
    0x7f, 0x30, 0x0, 0x0, 0x1, 0x0, 0x0, 0x4f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0,

    /* U+F03E "" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xf9,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xd, 0xff, 0xf8,
    0xaf, 0xff, 0xff, 0x9b, 0xff, 0xf8, 0x0, 0xaf,
    0xff, 0xff, 0x6d, 0xf8, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x17, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0xaa, 0xaa, 0xaa,
    0xaa, 0xad, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F043 "" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x6f, 0x50,
    0x0, 0x0, 0xc, 0xfb, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0, 0x8f,
    0xff, 0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0x2a,
    0xff, 0xff, 0xff, 0xf9, 0xef, 0xef, 0xff, 0xff,
    0xdf, 0xb7, 0xff, 0xff, 0xfe, 0xce, 0x2d, 0xff,
    0xff, 0xb5, 0xfc, 0x36, 0xff, 0xf3, 0x7, 0xff,
    0xff, 0xf6, 0x0, 0x2, 0x89, 0x72, 0x0,

    /* U+F048 "" */
    0x6, 0x40, 0x0, 0x2, 0x42, 0xfa, 0x0, 0x3,
    0xef, 0x2f, 0xa0, 0x4, 0xff, 0xf2, 0xfa, 0x5,
    0xff, 0xff, 0x2f, 0xa6, 0xff, 0xff, 0xf2, 0xfe,
    0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xf2,
    0xfc, 0xdf, 0xff, 0xff, 0x2f, 0xa1, 0xdf, 0xff,
    0xf2, 0xfa, 0x1, 0xcf, 0xff, 0x2f, 0xa0, 0x0,
    0xbf, 0xf2, 0xf9, 0x0, 0x0, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x48, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x26, 0x66, 0x10, 0x16, 0x66, 0x30, 0xef, 0xff,
    0xc0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2,
    0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff,
    0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2,
    0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff,
    0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff, 0xd0, 0xbf,
    0xff, 0xf2, 0xaf, 0xff, 0x80, 0x6e, 0xff, 0xc0,

    /* U+F04D "" */
    0x26, 0x66, 0x66, 0x66, 0x66, 0x50, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xd1,

    /* U+F051 "" */
    0x5, 0x0, 0x0, 0x6, 0x45, 0xfb, 0x0, 0x1,
    0xfc, 0x6f, 0xfc, 0x10, 0x1f, 0xc6, 0xff, 0xfd,
    0x11, 0xfc, 0x6f, 0xff, 0xfd, 0x3f, 0xc6, 0xff,
    0xff, 0xff, 0xfc, 0x6f, 0xff, 0xff, 0xff, 0xc6,
    0xff, 0xff, 0xfa, 0xfc, 0x6f, 0xff, 0xf7, 0x1f,
    0xc6, 0xff, 0xf6, 0x1, 0xfc, 0x6f, 0xf5, 0x0,
    0x1f, 0xc3, 0xe4, 0x0, 0x0, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x8e, 0xee,
    0xee, 0xee, 0xee, 0xc1, 0x2, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xe3,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0xb, 0xff, 0x30,
    0x0, 0xbf, 0xf3, 0x0, 0xb, 0xff, 0x30, 0x0,
    0x2f, 0xf9, 0x0, 0x0, 0x6, 0xff, 0x80, 0x0,
    0x0, 0x6f, 0xf8, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0, 0x5, 0xb0,

    /* U+F054 "" */
    0x1, 0x0, 0x0, 0x0, 0x1e, 0xc0, 0x0, 0x0,
    0x1e, 0xfc, 0x0, 0x0, 0x2, 0xef, 0xc0, 0x0,
    0x0, 0x2e, 0xfc, 0x0, 0x0, 0x2, 0xef, 0xc0,
    0x0, 0x0, 0x7f, 0xf4, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6f, 0xf7, 0x0, 0x6, 0xff, 0x70, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0xa, 0x70, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x17, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x36, 0x66, 0xbf, 0xf6, 0x66, 0x50,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0x90, 0x0, 0x0,

    /* U+F068 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x35, 0x55, 0x55, 0x55, 0x55, 0x40,

    /* U+F06E "" */
    0x0, 0x0, 0x5a, 0xcd, 0xc8, 0x20, 0x0, 0x0,
    0x2, 0xdf, 0xd7, 0x69, 0xff, 0x90, 0x0, 0x4,
    0xff, 0xa0, 0x1, 0x2, 0xef, 0xc1, 0x2, 0xff,
    0xf1, 0x2, 0xfc, 0x16, 0xff, 0xb0, 0xcf, 0xfb,
    0x5, 0xaf, 0xf9, 0x1f, 0xff, 0x6d, 0xff, 0xb1,
    0xff, 0xff, 0xb1, 0xff, 0xf7, 0x4f, 0xfe, 0xb,
    0xff, 0xf5, 0x5f, 0xfc, 0x0, 0x6f, 0xf9, 0x8,
    0xa5, 0x1d, 0xfd, 0x20, 0x0, 0x5e, 0xfb, 0x53,
    0x6e, 0xfb, 0x10, 0x0, 0x0, 0x7, 0xce, 0xfe,
    0xa4, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xf9, 0x5, 0xac, 0xdb, 0x71, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xfd, 0x76, 0xaf, 0xf7,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x31, 0x30, 0x4f,
    0xfa, 0x0, 0x0, 0x32, 0x4, 0xef, 0xbf, 0xc0,
    0x9f, 0xf8, 0x0, 0xe, 0xf5, 0x2, 0xcf, 0xff,
    0x64, 0xff, 0xf3, 0x1, 0xff, 0xf6, 0x0, 0x9f,
    0xf8, 0x4f, 0xff, 0x40, 0x6, 0xff, 0xc0, 0x0,
    0x6f, 0xeb, 0xff, 0xa0, 0x0, 0x9, 0xff, 0x60,
    0x0, 0x3e, 0xff, 0xc0, 0x0, 0x0, 0x7, 0xff,
    0xa4, 0x30, 0x1b, 0xfb, 0x10, 0x0, 0x0, 0x2,
    0x8d, 0xff, 0x80, 0x8, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x70,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0x1a, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0xa, 0xff, 0x60, 0x0,
    0x0, 0x5, 0xff, 0xf5, 0xa, 0xff, 0xe1, 0x0,
    0x0, 0xe, 0xff, 0xf6, 0xb, 0xff, 0xf9, 0x0,
    0x0, 0x8f, 0xff, 0xfd, 0x8f, 0xff, 0xff, 0x20,
    0x2, 0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xb0,
    0xa, 0xff, 0xff, 0xf7, 0x1c, 0xff, 0xff, 0xf4,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x91,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xa0, 0xef, 0xfa, 0x0,
    0x5, 0xff, 0xff, 0xaf, 0xff, 0xf9, 0x4, 0xff,
    0xff, 0xfb, 0x11, 0x3f, 0xa4, 0xff, 0x84, 0xfc,
    0x0, 0x0, 0x33, 0xff, 0x90, 0x19, 0x0, 0x0,
    0x2, 0xef, 0xa2, 0x1, 0x70, 0x0, 0x2, 0xef,
    0xa4, 0xf6, 0x3f, 0xa0, 0xef, 0xff, 0xb0, 0x5f,
    0xff, 0xff, 0xaf, 0xff, 0xc0, 0x0, 0x6f, 0xff,
    0xfb, 0x11, 0x10, 0x0, 0x0, 0x14, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xa0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x5f, 0xfa, 0xff, 0xa0, 0x0,
    0x5, 0xff, 0x90, 0x3f, 0xfa, 0x0, 0x4f, 0xf9,
    0x0, 0x3, 0xff, 0xa0, 0xbf, 0x90, 0x0, 0x0,
    0x3f, 0xf1, 0x15, 0x0, 0x0, 0x0, 0x3, 0x30,

    /* U+F078 "" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x10, 0x9f, 0x50,
    0x0, 0x0, 0x1d, 0xe1, 0x8f, 0xf5, 0x0, 0x1,
    0xdf, 0xd0, 0x8, 0xff, 0x50, 0x1d, 0xfd, 0x10,
    0x0, 0x8f, 0xf6, 0xdf, 0xd1, 0x0, 0x0, 0x8,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x10, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x7d, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfd, 0x18, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x6f, 0xff, 0xfd, 0x4a, 0xaa, 0xaa, 0xfb,
    0x0, 0xc, 0xda, 0xf7, 0xf4, 0x0, 0x0, 0xe,
    0xb0, 0x0, 0x0, 0x9f, 0x11, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x9f, 0x10, 0x0, 0x2,
    0xe6, 0xeb, 0x9d, 0x0, 0x9, 0xf9, 0x99, 0x99,
    0x4d, 0xff, 0xff, 0xa0, 0x0, 0x7f, 0xff, 0xff,
    0xfb, 0x1d, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x6b, 0xcc, 0xc7, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf8, 0x22, 0x22, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x69, 0x9f, 0xff, 0x99,
    0x60, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x46,
    0x66, 0x2f, 0xff, 0x26, 0x66, 0x50, 0xff, 0xff,
    0x46, 0x76, 0x4f, 0xff, 0xf2, 0xff, 0xff, 0xfc,
    0xbc, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xb8, 0xf2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x1, 0x50, 0x0, 0x4f, 0xfd, 0x0, 0x3,
    0xaf, 0xf8, 0x7, 0xff, 0xf2, 0x0, 0xe, 0xff,
    0xff, 0xef, 0xfe, 0x30, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x8, 0xff, 0xff, 0xd5,
    0x0, 0x0, 0x0, 0x2, 0x98, 0x62, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0C4 "" */
    0x5, 0x84, 0x0, 0x0, 0x1, 0x0, 0x8f, 0xff,
    0x60, 0x0, 0xaf, 0xf3, 0xec, 0x2e, 0xc0, 0x1c,
    0xff, 0xa0, 0xde, 0x9f, 0xd1, 0xdf, 0xfa, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x2d,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xf7,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0x60, 0x0,
    0xcf, 0xaf, 0xd2, 0xef, 0xf6, 0x0, 0xfb, 0xd,
    0xd0, 0x2e, 0xff, 0x60, 0xbf, 0xef, 0x80, 0x2,
    0xef, 0xf3, 0x19, 0xc8, 0x0, 0x0, 0x16, 0x40,

    /* U+F0C5 "" */
    0x0, 0x2, 0x66, 0x66, 0x12, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x3f, 0x40, 0x0, 0xc, 0xff, 0xff,
    0x3f, 0xf2, 0xdf, 0x6c, 0xff, 0xff, 0x53, 0x31,
    0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0x7b, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xc2, 0x33, 0x33, 0x33, 0x20,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x8a, 0xaa,
    0xaa, 0xa9, 0x0, 0x0,

    /* U+F0C7 "" */
    0x49, 0x99, 0x99, 0x99, 0x60, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xfa, 0x0, 0x0, 0x1,
    0xff, 0x80, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xf5, 0xfe, 0xaa,
    0xaa, 0xaa, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0x80, 0x4f, 0xff, 0xf5,
    0xff, 0xff, 0x10, 0xc, 0xff, 0xf5, 0xff, 0xff,
    0x50, 0x1e, 0xff, 0xf5, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xf5, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xd1,

    /* U+F0C9 "" */
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0E0 "" */
    0x6b, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x95, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x65, 0xf9, 0x3d, 0xff, 0xff, 0xfd, 0x39,
    0xff, 0xfd, 0x39, 0xff, 0xf9, 0x3d, 0xff, 0xff,
    0xff, 0x65, 0xa4, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xb7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F0E7 "" */
    0x0, 0x55, 0x55, 0x20, 0x0, 0x3, 0xff, 0xff,
    0x90, 0x0, 0x5, 0xff, 0xff, 0x40, 0x0, 0x8,
    0xff, 0xff, 0x0, 0x0, 0xa, 0xff, 0xfc, 0x44,
    0x40, 0xc, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff,
    0xff, 0xff, 0x80, 0xd, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0xbf,
    0xc0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x3, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x0,
    0x0, 0x0, 0x3, 0x50, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x6e,
    0xf7, 0x54, 0x0, 0x0, 0xff, 0xf9, 0x8f, 0xff,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xff, 0xfa, 0x24, 0x44, 0x1, 0x0, 0xff, 0xf4,
    0xef, 0xff, 0x3e, 0x20, 0xff, 0xf4, 0xff, 0xff,
    0x3f, 0xe2, 0xff, 0xf4, 0xff, 0xff, 0x43, 0x31,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xf6, 0x23, 0x30, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x7a, 0xaa, 0xaa, 0xa2,

    /* U+F0F3 "" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0x0, 0x0, 0x7, 0xef, 0xfa,
    0x10, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xd0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0x30, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x13, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0xcf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x29, 0x50, 0x0, 0x0,

    /* U+F11C "" */
    0x5a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xfa,
    0xd, 0x1, 0xb0, 0xd0, 0xa2, 0xf, 0xaf, 0xeb,
    0xfb, 0xcf, 0xbf, 0xbe, 0xcb, 0xfa, 0xff, 0xe1,
    0x98, 0x1c, 0x1c, 0x15, 0xff, 0xaf, 0xfd, 0x8,
    0x70, 0xb0, 0xb0, 0x3f, 0xfa, 0xff, 0xdf, 0xdd,
    0xdd, 0xdd, 0xfd, 0xdf, 0xaf, 0xa0, 0xd0, 0x0,
    0x0, 0xa, 0x20, 0xfa, 0xfe, 0xaf, 0xaa, 0xaa,
    0xaa, 0xeb, 0xaf, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x1, 0x8f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xff, 0x50, 0x0, 0x3,
    0xbf, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x3c, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x9e, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x76, 0x0, 0x0,
    0x0,

    /* U+F15B "" */
    0x34, 0x44, 0x43, 0x10, 0x0, 0xff, 0xff, 0xfb,
    0x8a, 0x0, 0xff, 0xff, 0xfb, 0x8f, 0xa0, 0xff,
    0xff, 0xfb, 0x7f, 0xf8, 0xff, 0xff, 0xfc, 0x11,
    0x11, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbd, 0xdd, 0xdd, 0xdd, 0xd8,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x35, 0x54, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0xff, 0xd8, 0x10,
    0x0, 0x4, 0xef, 0xff, 0xdb, 0xbc, 0xff, 0xff,
    0x70, 0x8, 0xff, 0xd6, 0x10, 0x0, 0x0, 0x4b,
    0xff, 0xb0, 0xbf, 0x70, 0x0, 0x13, 0x32, 0x0,
    0x5, 0xed, 0x10, 0x30, 0x6, 0xdf, 0xff, 0xfe,
    0x80, 0x1, 0x10, 0x0, 0xb, 0xff, 0xfc, 0xce,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0xbe, 0x60, 0x0,
    0x4, 0xde, 0x10, 0x0, 0x0, 0x0, 0x10, 0x3,
    0x40, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xac, 0x30, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x88, 0x88, 0x65,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x99, 0x99,
    0x98, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x88, 0x30, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0x70,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x99, 0x94,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x90, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x85, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xfa, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x60, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x5c, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xef, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0x5c, 0x50, 0x0,
    0x0, 0x0, 0x64, 0x0, 0xa5, 0x0, 0x0, 0x0,
    0x10, 0x0, 0xbf, 0xf6, 0x4e, 0x21, 0x11, 0x11,
    0x17, 0xd3, 0xf, 0xff, 0xec, 0xce, 0xec, 0xcc,
    0xcc, 0xef, 0xe1, 0x7f, 0xe3, 0x0, 0x2d, 0x0,
    0x0, 0x6, 0x80, 0x0, 0x10, 0x0, 0x0, 0x97,
    0xa, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xe8, 0x0, 0x0, 0xdf, 0xf8, 0xcf, 0xf8,
    0x0, 0x7f, 0xff, 0x81, 0xdf, 0xf1, 0xc, 0xfa,
    0xf8, 0x52, 0xdf, 0x60, 0xff, 0x64, 0x78, 0x3a,
    0xf8, 0xf, 0xff, 0x50, 0x8, 0xff, 0xa1, 0xff,
    0xff, 0x12, 0xff, 0xfa, 0xf, 0xff, 0x41, 0x15,
    0xff, 0xa0, 0xef, 0x46, 0x88, 0x48, 0xf8, 0xb,
    0xfc, 0xf8, 0x42, 0xef, 0x50, 0x5f, 0xff, 0x82,
    0xef, 0xf1, 0x0, 0xaf, 0xfa, 0xef, 0xf6, 0x0,
    0x0, 0x5a, 0xdc, 0xa4, 0x0,

    /* U+F2ED "" */
    0x0, 0x1, 0x56, 0x63, 0x0, 0x0, 0x78, 0x8b,
    0xff, 0xfe, 0x88, 0x82, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x3f, 0xfc,
    0xfc, 0xfe, 0xdf, 0x90, 0x3f, 0xd5, 0xf4, 0xeb,
    0x7f, 0x90, 0x3f, 0xd5, 0xf4, 0xeb, 0x7f, 0x90,
    0x3f, 0xd5, 0xf4, 0xeb, 0x7f, 0x90, 0x3f, 0xd5,
    0xf4, 0xeb, 0x7f, 0x90, 0x3f, 0xd5, 0xf4, 0xeb,
    0x7f, 0x90, 0x3f, 0xe6, 0xf5, 0xec, 0x8f, 0x90,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x7, 0x9a,
    0xaa, 0xaa, 0xa9, 0x10,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x3, 0xc3, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x3e,
    0xfd, 0x3d, 0xf5, 0x0, 0x0, 0x3, 0xef, 0xff,
    0xd3, 0x50, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0x60, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x86, 0x30, 0x0, 0x0,
    0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x8b, 0xcc, 0xcc, 0xcc, 0xcc, 0xb6,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0xbf, 0xff, 0xf8, 0xbf, 0xf6, 0xef,
    0xff, 0x40, 0xbf, 0xff, 0xff, 0x10, 0xa4, 0x9,
    0xff, 0xf4, 0xaf, 0xff, 0xff, 0xfd, 0x10, 0x7,
    0xff, 0xff, 0x4d, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xbf, 0xff, 0xf4, 0x1d, 0xff, 0xff, 0xf4, 0x6,
    0x10, 0xbf, 0xff, 0x40, 0x1d, 0xff, 0xff, 0x47,
    0xfd, 0x2b, 0xff, 0xf4, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0,

    /* U+F7C2 "" */
    0x0, 0x8, 0x99, 0x99, 0x70, 0x0, 0xbf, 0xff,
    0xff, 0xf8, 0xa, 0xd0, 0xd0, 0xd0, 0xeb, 0x9f,
    0xd0, 0xd0, 0xd0, 0xeb, 0xff, 0xd1, 0xd1, 0xd1,
    0xeb, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xef, 0xff, 0xff, 0xff,
    0xfa, 0x5c, 0xdd, 0xdd, 0xdd, 0xb2,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0xf, 0xf0, 0xa, 0xff, 0x21,
    0x11, 0x11, 0x1f, 0xf0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x8, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 51, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 54, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18, .adv_w = 96, .box_w = 4, .box_h = 5, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 28, .adv_w = 131, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 70, .adv_w = 89, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 105, .adv_w = 221, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 183, .adv_w = 100, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 225, .adv_w = 42, .box_w = 2, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 230, .adv_w = 58, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 258, .adv_w = 58, .box_w = 3, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 279, .adv_w = 98, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 294, .adv_w = 82, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 307, .adv_w = 47, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 315, .adv_w = 71, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 317, .adv_w = 54, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 319, .adv_w = 80, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 349, .adv_w = 93, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 385, .adv_w = 66, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 403, .adv_w = 91, .box_w = 4, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 427, .adv_w = 92, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 463, .adv_w = 91, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 499, .adv_w = 91, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 529, .adv_w = 91, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 565, .adv_w = 91, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 601, .adv_w = 91, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 637, .adv_w = 91, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 667, .adv_w = 56, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 678, .adv_w = 57, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 692, .adv_w = 74, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 712, .adv_w = 73, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 722, .adv_w = 74, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 742, .adv_w = 81, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 772, .adv_w = 143, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 826, .adv_w = 95, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 862, .adv_w = 97, .box_w = 5, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 892, .adv_w = 93, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 928, .adv_w = 96, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 964, .adv_w = 78, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 994, .adv_w = 76, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1024, .adv_w = 96, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1060, .adv_w = 100, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1096, .adv_w = 54, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1108, .adv_w = 87, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1138, .adv_w = 91, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1174, .adv_w = 72, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1204, .adv_w = 132, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1252, .adv_w = 103, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1288, .adv_w = 96, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1324, .adv_w = 90, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1360, .adv_w = 96, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1402, .adv_w = 97, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1438, .adv_w = 89, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1474, .adv_w = 65, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1498, .adv_w = 98, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1534, .adv_w = 94, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1570, .adv_w = 138, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1624, .adv_w = 83, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1654, .adv_w = 85, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1690, .adv_w = 78, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1720, .adv_w = 78, .box_w = 4, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1744, .adv_w = 88, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1774, .adv_w = 78, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1792, .adv_w = 105, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1810, .adv_w = 76, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1813, .adv_w = 66, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1819, .adv_w = 94, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1849, .adv_w = 95, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1885, .adv_w = 89, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1910, .adv_w = 95, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1946, .adv_w = 91, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1971, .adv_w = 63, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1995, .adv_w = 94, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2025, .adv_w = 98, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2061, .adv_w = 51, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2079, .adv_w = 54, .box_w = 3, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2100, .adv_w = 88, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2136, .adv_w = 51, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2154, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2199, .adv_w = 99, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2229, .adv_w = 93, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2259, .adv_w = 95, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2295, .adv_w = 95, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2331, .adv_w = 66, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2351, .adv_w = 82, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2376, .adv_w = 62, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2400, .adv_w = 98, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2430, .adv_w = 76, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2455, .adv_w = 118, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2495, .adv_w = 75, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2520, .adv_w = 80, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2550, .adv_w = 69, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2570, .adv_w = 81, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2596, .adv_w = 55, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2609, .adv_w = 81, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2635, .adv_w = 113, .box_w = 7, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2649, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2747, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2812, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2890, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2955, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3000, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3091, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3182, .adv_w = 234, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3272, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3370, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3445, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3536, .adv_w = 104, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3575, .adv_w = 156, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3630, .adv_w = 234, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3735, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3800, .adv_w = 143, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3863, .adv_w = 182, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3922, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4006, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4078, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4150, .adv_w = 182, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4209, .adv_w = 182, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4287, .adv_w = 130, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4335, .adv_w = 130, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4383, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4455, .adv_w = 182, .box_w = 12, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4479, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4554, .adv_w = 260, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4673, .adv_w = 234, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4785, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4863, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4911, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4959, .adv_w = 260, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5053, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5118, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5216, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5314, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5386, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5470, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5542, .adv_w = 182, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5608, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5673, .adv_w = 130, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5743, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5827, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5911, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5986, .adv_w = 208, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6091, .adv_w = 156, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6161, .adv_w = 260, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6263, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6340, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6417, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6494, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6571, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6648, .adv_w = 260, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6742, .adv_w = 182, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6819, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6903, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7001, .adv_w = 260, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7086, .adv_w = 156, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7156, .adv_w = 209, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 2, 3, 4, 0, 5,
    6, 0, 0, 7, 8, 9, 0, 0,
    10, 11, 0, 12, 0, 0, 0, 13,
    14, 0, 15, 0, 0, 0, 0, 0,
    0, 0, 0, 16, 17, 0, 0, 0,
    0, 0, 0, 0, 18, 0, 0, 0,
    19, 0, 0, 20, 0, 0, 0, 21,
    22, 0, 23, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 1, 0, 2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 4, 0, 0, 0,
    5, 0, 0, 6, 0, 0, 0, 0,
    7, 0, 8, 0, 9, 10, 11, 12,
    13, 14, 15, 0, 0, 0, 0, 0,
    0, 0, 16, 0, 17, 0, 18, 0,
    19, 0, 0, 0, 0, 0, 0, 0,
    20, 0, 0, 0, 21, 0, 22, 23,
    24, 25, 26, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, -2, -2, 0, -2, 0,
    0, -3, -2, -4, -5, 0, -4, 0,
    0, 0, 0, 0, 0, -1, -1, -1,
    0, -2, -6, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -6, -7,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -10, -12, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -1, 0, -1, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -2, 0, -2, -2,
    -1, -1, 0, -3, -3, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -2, -3, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -24, -26, -3, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, -7, -4, 0, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, -1, -1, -1, 0, 0, 0, 0,
    0, 0, 0, -4, -4, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, -1, -1, -1, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -1, -4, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -3, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    -1, -1, 0, 0, 0, 0, 0, 0,
    -2, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 23,
    .right_class_cnt     = 26,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_Antonio_Regular_13 = {
#else
lv_font_t lv_font_Antonio_Regular_13 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 13,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = 0,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_ANTONIO_REGULAR_13*/

