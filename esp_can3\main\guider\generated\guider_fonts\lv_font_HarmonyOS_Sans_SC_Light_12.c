/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_LIGHT_12
#define LV_FONT_HARMONYOS_SANS_SC_LIGHT_12 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xb, 0xa, 0xa, 0x9, 0x9, 0x9, 0x8, 0x3,
    0x0, 0xb,

    /* U+0022 "\"" */
    0x0, 0x4, 0x49, 0x44, 0x94, 0x48,

    /* U+0023 "#" */
    0x0, 0x19, 0x3, 0x60, 0x0, 0x46, 0x6, 0x30,
    0x0, 0x73, 0x9, 0x0, 0xa, 0xeb, 0xbe, 0xb2,
    0x0, 0xa0, 0xa, 0x0, 0x0, 0x90, 0x27, 0x0,
    0x3, 0x60, 0x54, 0x0, 0x8d, 0xcb, 0xdb, 0x40,
    0x9, 0x0, 0xa0, 0x0, 0xa, 0x1, 0x80, 0x0,

    /* U+0024 "$" */
    0x0, 0x5, 0x0, 0x0, 0x0, 0xa0, 0x0, 0x3,
    0xae, 0x91, 0x1, 0xb1, 0xa3, 0xa0, 0x47, 0xa,
    0x4, 0x2, 0xa0, 0xa0, 0x0, 0x6, 0xbc, 0x0,
    0x0, 0x1, 0xdc, 0x30, 0x0, 0xa, 0x1c, 0x5,
    0x10, 0xa0, 0x92, 0x3a, 0xa, 0xc, 0x0, 0x5b,
    0xeb, 0x40, 0x0, 0xa, 0x0, 0x0, 0x0, 0xa0,
    0x0,

    /* U+0025 "%" */
    0x2a, 0xa5, 0x0, 0x74, 0x9, 0x10, 0xa0, 0x1a,
    0x0, 0x82, 0xa, 0x8, 0x30, 0x1, 0x99, 0x31,
    0xa0, 0x0, 0x0, 0x0, 0x92, 0x0, 0x0, 0x0,
    0x29, 0x0, 0x0, 0x0, 0x9, 0x23, 0xa9, 0x10,
    0x2, 0x90, 0xa0, 0x29, 0x0, 0xa1, 0xa, 0x0,
    0x90, 0x38, 0x0, 0x5a, 0xa2,

    /* U+0026 "&" */
    0x0, 0x5a, 0xa2, 0x0, 0x0, 0x2a, 0x1, 0xb0,
    0x0, 0x3, 0x80, 0xc, 0x0, 0x0, 0xb, 0x3a,
    0x30, 0x0, 0x0, 0x9e, 0x10, 0x24, 0x0, 0xa3,
    0x4a, 0x6, 0x40, 0x38, 0x0, 0x68, 0x90, 0x5,
    0x60, 0x0, 0x9a, 0x0, 0x2b, 0x0, 0x1b, 0xb2,
    0x0, 0x5c, 0xbb, 0x21, 0xc0,

    /* U+0027 "'" */
    0x0, 0x44, 0x44, 0x44,

    /* U+0028 "(" */
    0x0, 0x64, 0x1, 0x90, 0x8, 0x30, 0xb, 0x0,
    0xa, 0x0, 0x19, 0x0, 0x19, 0x0, 0xa, 0x0,
    0xb, 0x0, 0x8, 0x30, 0x1, 0x90, 0x0, 0x64,

    /* U+0029 ")" */
    0x64, 0x0, 0xa0, 0x5, 0x50, 0xa, 0x0, 0xb0,
    0xb, 0x0, 0xb0, 0xb, 0x1, 0xa0, 0x55, 0xa,
    0x6, 0x40,

    /* U+002A "*" */
    0x0, 0x80, 0x4, 0x88, 0x83, 0x7, 0xf6, 0x4,
    0x57, 0x53, 0x0, 0x60, 0x0,

    /* U+002B "+" */
    0x0, 0x8, 0x0, 0x0, 0x9, 0x0, 0x0, 0x9,
    0x0, 0x3a, 0xad, 0xaa, 0x0, 0x9, 0x0, 0x0,
    0x9, 0x0,

    /* U+002C "," */
    0x0, 0xd, 0x7, 0x23,

    /* U+002D "-" */
    0x1a, 0xaa, 0xa1,

    /* U+002E "." */
    0x0, 0x1a,

    /* U+002F "/" */
    0x0, 0xa, 0x0, 0x0, 0xa0, 0x0, 0x56, 0x0,
    0xa, 0x10, 0x0, 0xa0, 0x0, 0x46, 0x0, 0x9,
    0x10, 0x0, 0xa0, 0x0, 0x46, 0x0, 0x9, 0x10,
    0x0,

    /* U+0030 "0" */
    0x2, 0xbb, 0x90, 0x0, 0xb1, 0x6, 0x60, 0x1a,
    0x0, 0xb, 0x4, 0x70, 0x0, 0xc0, 0x56, 0x0,
    0xc, 0x5, 0x60, 0x0, 0xb0, 0x47, 0x0, 0xc,
    0x2, 0xa0, 0x0, 0xb0, 0xb, 0x10, 0x66, 0x0,
    0x2b, 0xb9, 0x0,

    /* U+0031 "1" */
    0x3, 0xd3, 0x7a, 0x93, 0x20, 0x73, 0x0, 0x73,
    0x0, 0x73, 0x0, 0x73, 0x0, 0x73, 0x0, 0x73,
    0x0, 0x73, 0x0, 0x73,

    /* U+0032 "2" */
    0x3, 0xbb, 0xa0, 0xb, 0x0, 0x58, 0x13, 0x0,
    0xb, 0x0, 0x0, 0x29, 0x0, 0x0, 0xa3, 0x0,
    0x5, 0x90, 0x0, 0x2b, 0x0, 0x0, 0xc1, 0x0,
    0xa, 0x40, 0x0, 0x4e, 0xbb, 0xbb,

    /* U+0033 "3" */
    0x4, 0xbb, 0xa0, 0x1b, 0x0, 0x66, 0x1, 0x0,
    0x29, 0x0, 0x0, 0x94, 0x0, 0x6e, 0xb0, 0x0,
    0x0, 0x78, 0x0, 0x0, 0xc, 0x12, 0x0, 0xc,
    0x2a, 0x0, 0x4a, 0x4, 0xbb, 0xa1,

    /* U+0034 "4" */
    0x0, 0x5, 0x70, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x48, 0x0, 0x0, 0xb, 0x10, 0x0, 0x3, 0x90,
    0x71, 0x0, 0xa1, 0x9, 0x20, 0x29, 0x0, 0x92,
    0x6, 0xbb, 0xbd, 0xb4, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x9, 0x20,

    /* U+0035 "5" */
    0x6, 0xcb, 0xb6, 0x8, 0x20, 0x0, 0x9, 0x10,
    0x0, 0xb, 0x0, 0x0, 0xc, 0x9b, 0x80, 0x9,
    0x10, 0x58, 0x0, 0x0, 0xc, 0x0, 0x0, 0xc,
    0xa, 0x0, 0x49, 0x5, 0xbb, 0xa1,

    /* U+0036 "6" */
    0x0, 0x6, 0x70, 0x0, 0x0, 0xc0, 0x0, 0x0,
    0x76, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x9, 0xdb,
    0xa1, 0x1, 0xe2, 0x3, 0xb0, 0x48, 0x0, 0xb,
    0x14, 0x70, 0x0, 0xa1, 0xc, 0x0, 0x2b, 0x0,
    0x3b, 0xbb, 0x20,

    /* U+0037 "7" */
    0x3b, 0xbb, 0xbe, 0x0, 0x0, 0x29, 0x0, 0x0,
    0x84, 0x0, 0x0, 0xc0, 0x0, 0x3, 0x80, 0x0,
    0x9, 0x30, 0x0, 0xc, 0x0, 0x0, 0x48, 0x0,
    0x0, 0xa2, 0x0, 0x0, 0xc0, 0x0,

    /* U+0038 "8" */
    0x3, 0xbb, 0xa0, 0x0, 0xc0, 0x4, 0x80, 0x1a,
    0x0, 0xb, 0x0, 0xb1, 0x6, 0x70, 0x3, 0xed,
    0xc0, 0x0, 0xb1, 0x4, 0x90, 0x56, 0x0, 0xb,
    0x6, 0x60, 0x0, 0xb0, 0x1b, 0x0, 0x3b, 0x0,
    0x4b, 0xba, 0x10,

    /* U+0039 "9" */
    0x5, 0xbb, 0xa0, 0x2b, 0x0, 0x49, 0x75, 0x0,
    0xb, 0x65, 0x0, 0xc, 0x2c, 0x10, 0x7a, 0x4,
    0xbb, 0xe3, 0x0, 0x3, 0xb0, 0x0, 0xb, 0x20,
    0x0, 0x49, 0x0, 0x0, 0xb1, 0x0,

    /* U+003A ":" */
    0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,

    /* U+003B ";" */
    0xc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x0, 0x70, 0x14, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x5, 0x0, 0x6, 0xb6, 0x7, 0xb5,
    0x0, 0x3d, 0x50, 0x0, 0x0, 0x6b, 0x60, 0x0,
    0x0, 0x5b, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x3a, 0xaa, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0xaa, 0xaa,

    /* U+003E ">" */
    0x23, 0x0, 0x0, 0x18, 0xa4, 0x0, 0x0, 0x18,
    0xb4, 0x0, 0x1, 0x8b, 0x1, 0x9a, 0x30, 0x3a,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x19, 0xb9, 0xa, 0x20, 0x59, 0x0, 0x1, 0xb0,
    0x0, 0x75, 0x0, 0x58, 0x0, 0xb, 0x0, 0x1,
    0x80, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x0,

    /* U+0040 "@" */
    0x0, 0x2, 0x8a, 0xa9, 0x40, 0x0, 0x0, 0x69,
    0x20, 0x0, 0x79, 0x0, 0x4, 0x80, 0x0, 0x0,
    0x5, 0x70, 0xa, 0x0, 0x7b, 0xb6, 0x50, 0xa0,
    0x18, 0x4, 0x80, 0xa, 0x60, 0x72, 0x35, 0x8,
    0x30, 0x5, 0x60, 0x54, 0x35, 0x7, 0x30, 0x5,
    0x60, 0x63, 0x18, 0x3, 0xa0, 0x9, 0x80, 0xa0,
    0xa, 0x0, 0x5a, 0xa2, 0x7a, 0x40, 0x4, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x79, 0x10, 0x1,
    0x70, 0x0, 0x0, 0x2, 0x9a, 0xa9, 0x20, 0x0,

    /* U+0041 "A" */
    0x0, 0xa, 0x50, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0x45, 0xa0, 0x0, 0x0, 0x90, 0x65, 0x0,
    0x0, 0xa0, 0x1a, 0x0, 0x5, 0x50, 0xa, 0x10,
    0xa, 0xbb, 0xbc, 0x60, 0xa, 0x0, 0x0, 0xb0,
    0x56, 0x0, 0x0, 0xa1, 0xa1, 0x0, 0x0, 0x57,

    /* U+0042 "B" */
    0xdb, 0xbc, 0x50, 0xb, 0x0, 0xb, 0x30, 0xb0,
    0x0, 0x47, 0xb, 0x0, 0x4, 0x70, 0xb0, 0x1,
    0xb2, 0xd, 0xaa, 0xda, 0x0, 0xb0, 0x0, 0x4b,
    0xb, 0x0, 0x0, 0xb0, 0xb0, 0x0, 0x2c, 0xd,
    0xbb, 0xbb, 0x20,

    /* U+0043 "C" */
    0x0, 0x4b, 0xbb, 0x30, 0x4, 0xa0, 0x0, 0xb1,
    0xb, 0x0, 0x0, 0x10, 0x29, 0x0, 0x0, 0x0,
    0x47, 0x0, 0x0, 0x0, 0x47, 0x0, 0x0, 0x0,
    0x29, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x10,
    0x4, 0xa0, 0x0, 0xb1, 0x0, 0x4b, 0xbb, 0x40,

    /* U+0044 "D" */
    0xdb, 0xbb, 0x70, 0xb, 0x0, 0x6, 0xa0, 0xb0,
    0x0, 0x8, 0x4b, 0x0, 0x0, 0x29, 0xb0, 0x0,
    0x0, 0xbb, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x2,
    0x9b, 0x0, 0x0, 0x84, 0xb0, 0x0, 0x6a, 0xd,
    0xbb, 0xb7, 0x0,

    /* U+0045 "E" */
    0xdb, 0xbb, 0xb2, 0xb0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0xdb, 0xbb, 0x80, 0xb0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0xdb, 0xbb, 0xb3,

    /* U+0046 "F" */
    0xdb, 0xbb, 0xb2, 0xb0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0xdb,
    0xbb, 0x80, 0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3b, 0xbc, 0x60, 0x3, 0xb1, 0x0, 0x85,
    0xb, 0x10, 0x0, 0x0, 0x29, 0x0, 0x0, 0x0,
    0x47, 0x0, 0x0, 0x0, 0x47, 0x0, 0x1b, 0xb8,
    0x29, 0x0, 0x0, 0x1a, 0xb, 0x10, 0x0, 0x1a,
    0x3, 0xb1, 0x0, 0x58, 0x0, 0x3b, 0xbc, 0x90,

    /* U+0048 "H" */
    0xb0, 0x0, 0x1, 0xab, 0x0, 0x0, 0x1a, 0xb0,
    0x0, 0x1, 0xab, 0x0, 0x0, 0x1a, 0xdb, 0xbb,
    0xbb, 0xab, 0x0, 0x0, 0x1a, 0xb0, 0x0, 0x1,
    0xab, 0x0, 0x0, 0x1a, 0xb0, 0x0, 0x1, 0xab,
    0x0, 0x0, 0x1a,

    /* U+0049 "I" */
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb,

    /* U+004A "J" */
    0x0, 0x8, 0x30, 0x0, 0x83, 0x0, 0x8, 0x30,
    0x0, 0x83, 0x0, 0x8, 0x30, 0x0, 0x83, 0x0,
    0x8, 0x30, 0x0, 0x82, 0x80, 0xb, 0x4, 0xcb,
    0x50,

    /* U+004B "K" */
    0xb0, 0x0, 0x1c, 0x1b, 0x0, 0xb, 0x30, 0xb0,
    0x9, 0x50, 0xb, 0x6, 0x70, 0x0, 0xb4, 0xe1,
    0x0, 0xc, 0xb3, 0xb0, 0x0, 0xd1, 0x8, 0x50,
    0xb, 0x0, 0xc, 0x10, 0xb0, 0x0, 0x3a, 0xb,
    0x0, 0x0, 0x95,

    /* U+004C "L" */
    0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0xdb, 0xbb, 0xb2,

    /* U+004D "M" */
    0xd1, 0x0, 0x0, 0xc, 0x1d, 0x90, 0x0, 0x5,
    0xf1, 0xba, 0x20, 0x0, 0xb9, 0x1b, 0x2a, 0x0,
    0x65, 0x91, 0xb0, 0x83, 0xb, 0x9, 0x1b, 0x1,
    0xb8, 0x30, 0x91, 0xb0, 0x7, 0xa0, 0x9, 0x1b,
    0x0, 0x0, 0x0, 0x91, 0xb0, 0x0, 0x0, 0x9,
    0x1b, 0x0, 0x0, 0x0, 0x91,

    /* U+004E "N" */
    0xd1, 0x0, 0x1, 0x9c, 0xa0, 0x0, 0x19, 0xa9,
    0x40, 0x1, 0x9a, 0x1b, 0x0, 0x19, 0xa0, 0x57,
    0x1, 0x9a, 0x0, 0xb1, 0x19, 0xa0, 0x2, 0xa1,
    0x9a, 0x0, 0x8, 0x59, 0xa0, 0x0, 0xc, 0x9a,
    0x0, 0x0, 0x59,

    /* U+004F "O" */
    0x0, 0x4b, 0xbb, 0x50, 0x0, 0x4a, 0x0, 0x9,
    0x50, 0xc, 0x0, 0x0, 0xc, 0x2, 0x90, 0x0,
    0x0, 0x92, 0x47, 0x0, 0x0, 0x6, 0x44, 0x70,
    0x0, 0x0, 0x65, 0x29, 0x0, 0x0, 0x9, 0x30,
    0xc0, 0x0, 0x0, 0xc0, 0x4, 0xa0, 0x0, 0x95,
    0x0, 0x4, 0xbb, 0xb5, 0x0,

    /* U+0050 "P" */
    0xdb, 0xbb, 0x40, 0xb0, 0x0, 0xb2, 0xb0, 0x0,
    0x47, 0xb0, 0x0, 0x57, 0xb0, 0x1, 0xc1, 0xdb,
    0xba, 0x30, 0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0xb0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4b, 0xbb, 0x50, 0x0, 0x4a, 0x0, 0x9,
    0x50, 0xc, 0x0, 0x0, 0xc, 0x2, 0x90, 0x0,
    0x0, 0x92, 0x47, 0x0, 0x0, 0x6, 0x44, 0x70,
    0x0, 0x0, 0x64, 0x29, 0x0, 0x0, 0x9, 0x20,
    0xc0, 0x0, 0x0, 0xc0, 0x4, 0xa0, 0x0, 0x95,
    0x0, 0x4, 0xbb, 0xd8, 0x0, 0x0, 0x0, 0x1,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0xb5,

    /* U+0052 "R" */
    0xdb, 0xbb, 0x40, 0xb, 0x0, 0xb, 0x20, 0xb0,
    0x0, 0x56, 0xb, 0x0, 0x6, 0x60, 0xb0, 0x2,
    0xc1, 0xd, 0xbb, 0xd2, 0x0, 0xb0, 0xb, 0x10,
    0xb, 0x0, 0x3a, 0x0, 0xb0, 0x0, 0x85, 0xb,
    0x0, 0x0, 0xc1,

    /* U+0053 "S" */
    0x5, 0xcb, 0xb2, 0x2, 0xb0, 0x1, 0xc0, 0x57,
    0x0, 0x1, 0x2, 0xc1, 0x0, 0x0, 0x4, 0xc9,
    0x30, 0x0, 0x0, 0x3b, 0x80, 0x0, 0x0, 0xc,
    0x15, 0x0, 0x0, 0x92, 0x59, 0x0, 0x1c, 0x0,
    0x6b, 0xbb, 0x30,

    /* U+0054 "T" */
    0x8b, 0xbe, 0xbb, 0x40, 0x1, 0xa0, 0x0, 0x0,
    0x1a, 0x0, 0x0, 0x1, 0xa0, 0x0, 0x0, 0x1a,
    0x0, 0x0, 0x1, 0xa0, 0x0, 0x0, 0x1a, 0x0,
    0x0, 0x1, 0xa0, 0x0, 0x0, 0x1a, 0x0, 0x0,
    0x1, 0xa0, 0x0,

    /* U+0055 "U" */
    0xb0, 0x0, 0x3, 0x8b, 0x0, 0x0, 0x38, 0xb0,
    0x0, 0x3, 0x8b, 0x0, 0x0, 0x38, 0xb0, 0x0,
    0x3, 0x8b, 0x0, 0x0, 0x38, 0xb0, 0x0, 0x4,
    0x7b, 0x0, 0x0, 0x74, 0x68, 0x0, 0x2c, 0x0,
    0x7c, 0xbb, 0x20,

    /* U+0056 "V" */
    0xa1, 0x0, 0x0, 0x55, 0x57, 0x0, 0x0, 0xa0,
    0xb, 0x0, 0x0, 0xa0, 0xa, 0x10, 0x5, 0x50,
    0x4, 0x70, 0xa, 0x0, 0x0, 0xb0, 0xa, 0x0,
    0x0, 0x92, 0x45, 0x0, 0x0, 0x47, 0x90, 0x0,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0x9, 0x50, 0x0,

    /* U+0057 "W" */
    0xa1, 0x0, 0xa, 0x30, 0x0, 0x82, 0x65, 0x0,
    0xd, 0x70, 0x0, 0xa0, 0x29, 0x0, 0x38, 0xb0,
    0x1, 0x90, 0xb, 0x0, 0x73, 0xb0, 0x4, 0x50,
    0x9, 0x20, 0xa0, 0x74, 0x8, 0x10, 0x5, 0x60,
    0x90, 0x28, 0xa, 0x0, 0x0, 0xa3, 0x60, 0xb,
    0x18, 0x0, 0x0, 0xb7, 0x20, 0xa, 0x64, 0x0,
    0x0, 0x7c, 0x0, 0x5, 0xd0, 0x0, 0x0, 0x39,
    0x0, 0x1, 0xc0, 0x0,

    /* U+0058 "X" */
    0x67, 0x0, 0x0, 0xb1, 0xb, 0x20, 0x6, 0x60,
    0x2, 0xb0, 0x1b, 0x0, 0x0, 0x85, 0x92, 0x0,
    0x0, 0xc, 0x80, 0x0, 0x0, 0xc, 0x90, 0x0,
    0x0, 0x93, 0x93, 0x0, 0x3, 0x90, 0x1c, 0x0,
    0xb, 0x0, 0x5, 0x80, 0x75, 0x0, 0x0, 0xb2,

    /* U+0059 "Y" */
    0x94, 0x0, 0x1, 0xb1, 0xc0, 0x0, 0x93, 0x7,
    0x70, 0x2a, 0x0, 0xc, 0x1a, 0x20, 0x0, 0x5b,
    0xa0, 0x0, 0x0, 0xd2, 0x0, 0x0, 0xb, 0x0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0x0, 0xb0, 0x0,

    /* U+005A "Z" */
    0x3b, 0xbb, 0xbe, 0x40, 0x0, 0x1, 0xb0, 0x0,
    0x0, 0xa3, 0x0, 0x0, 0x39, 0x0, 0x0, 0xb,
    0x10, 0x0, 0x5, 0x70, 0x0, 0x0, 0xc0, 0x0,
    0x0, 0x75, 0x0, 0x0, 0x1b, 0x0, 0x0, 0x8,
    0xdb, 0xbb, 0xb6,

    /* U+005B "[" */
    0xbb, 0x5a, 0x0, 0xa0, 0xa, 0x0, 0xa0, 0xa,
    0x0, 0xa0, 0xa, 0x0, 0xa0, 0xa, 0x0, 0xa0,
    0xa, 0x0, 0xbb, 0x50,

    /* U+005C "\\" */
    0x91, 0x0, 0x4, 0x60, 0x0, 0xa, 0x0, 0x0,
    0x91, 0x0, 0x4, 0x60, 0x0, 0xa, 0x0, 0x0,
    0xa1, 0x0, 0x5, 0x60, 0x0, 0xa, 0x0, 0x0,
    0xa0,

    /* U+005D "]" */
    0x8b, 0x90, 0xa, 0x0, 0xa0, 0xa, 0x0, 0xa0,
    0xa, 0x0, 0xa0, 0xa, 0x0, 0xa0, 0xa, 0x0,
    0xa0, 0xa, 0x8b, 0x90,

    /* U+005E "^" */
    0x0, 0xc2, 0x0, 0x39, 0x80, 0x9, 0x1a, 0x0,
    0xa0, 0x55, 0x54, 0x0, 0xa0,

    /* U+005F "_" */
    0xaa, 0xaa, 0x80,

    /* U+0060 "`" */
    0x1a, 0x0, 0x73,

    /* U+0061 "a" */
    0x3, 0xbb, 0x90, 0x9, 0x0, 0x75, 0x0, 0x0,
    0x28, 0x4, 0x99, 0xa9, 0x1a, 0x0, 0x19, 0x38,
    0x0, 0x69, 0x8, 0xaa, 0x69,

    /* U+0062 "b" */
    0xa0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0xa7, 0xbc, 0x40, 0xe4, 0x0, 0xb1, 0xc0,
    0x0, 0x46, 0xb0, 0x0, 0x28, 0xc0, 0x0, 0x46,
    0xd4, 0x0, 0xa1, 0x97, 0xbb, 0x50,

    /* U+0063 "c" */
    0x2, 0xbb, 0xb1, 0xb, 0x10, 0x25, 0x38, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x38, 0x0, 0x0, 0xb,
    0x10, 0x25, 0x2, 0xbb, 0xb1,

    /* U+0064 "d" */
    0x0, 0x0, 0x7, 0x30, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x7, 0x30, 0x2b, 0xb9, 0x83, 0xb, 0x10,
    0x2d, 0x33, 0x80, 0x0, 0x93, 0x56, 0x0, 0x8,
    0x33, 0x70, 0x0, 0x93, 0xc, 0x10, 0x2d, 0x30,
    0x3b, 0xb9, 0x73,

    /* U+0065 "e" */
    0x2, 0xbb, 0xa1, 0xc, 0x10, 0x39, 0x38, 0x0,
    0xb, 0x5c, 0xaa, 0xab, 0x37, 0x0, 0x0, 0xb,
    0x10, 0x4, 0x2, 0xbb, 0xb3,

    /* U+0066 "f" */
    0x1, 0xab, 0x10, 0x74, 0x0, 0x9, 0x10, 0x6,
    0xda, 0x70, 0x9, 0x10, 0x0, 0x91, 0x0, 0x9,
    0x10, 0x0, 0x91, 0x0, 0x9, 0x10, 0x0, 0x91,
    0x0,

    /* U+0067 "g" */
    0x2, 0xbb, 0x97, 0x30, 0xb1, 0x2, 0xd3, 0x38,
    0x0, 0x9, 0x35, 0x60, 0x0, 0x83, 0x37, 0x0,
    0x9, 0x30, 0xb1, 0x2, 0xd3, 0x2, 0xbb, 0x98,
    0x20, 0x0, 0x0, 0x91, 0x8, 0x0, 0x1b, 0x0,
    0x4b, 0xba, 0x20,

    /* U+0068 "h" */
    0xa0, 0x0, 0xa, 0x0, 0x0, 0xa0, 0x0, 0xa,
    0x8b, 0xb2, 0xe3, 0x2, 0xab, 0x0, 0xb, 0xa0,
    0x0, 0xba, 0x0, 0xb, 0xa0, 0x0, 0xba, 0x0,
    0xb,

    /* U+0069 "i" */
    0xb, 0x0, 0x0, 0xa, 0xa, 0xa, 0xa, 0xa,
    0xa, 0xa,

    /* U+006A "j" */
    0x0, 0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x0, 0xb, 0x0, 0xb, 0x0, 0xb, 0x0, 0xb,
    0x0, 0xb, 0x0, 0xb, 0x0, 0xa, 0x0, 0x29,
    0x1b, 0xb2,

    /* U+006B "k" */
    0xa0, 0x0, 0xa, 0x0, 0x0, 0xa0, 0x0, 0xa,
    0x0, 0x85, 0xa0, 0x57, 0xa, 0x3a, 0x0, 0xcb,
    0xa0, 0xe, 0x18, 0x50, 0xa0, 0xc, 0x1a, 0x0,
    0x3a,

    /* U+006C "l" */
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa,

    /* U+006D "m" */
    0x98, 0xba, 0x1a, 0xc6, 0xe, 0x20, 0x8a, 0x0,
    0xb0, 0xc0, 0x4, 0x80, 0x8, 0x2a, 0x0, 0x37,
    0x0, 0x73, 0xa0, 0x3, 0x70, 0x7, 0x3a, 0x0,
    0x37, 0x0, 0x73, 0xa0, 0x3, 0x70, 0x7, 0x30,

    /* U+006E "n" */
    0x98, 0xbc, 0x2e, 0x30, 0x2a, 0xb0, 0x0, 0xba,
    0x0, 0xb, 0xa0, 0x0, 0xba, 0x0, 0xb, 0xa0,
    0x0, 0xb0,

    /* U+006F "o" */
    0x1, 0xbb, 0xb2, 0x0, 0xb1, 0x1, 0xb0, 0x38,
    0x0, 0x7, 0x45, 0x60, 0x0, 0x55, 0x38, 0x0,
    0x7, 0x40, 0xb1, 0x1, 0xb0, 0x2, 0xbb, 0xb2,
    0x0,

    /* U+0070 "p" */
    0x97, 0xbc, 0x40, 0xd4, 0x0, 0xb1, 0xc0, 0x0,
    0x46, 0xb0, 0x0, 0x28, 0xc0, 0x0, 0x46, 0xe4,
    0x0, 0xa1, 0xa7, 0xbb, 0x50, 0xa0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0xa0, 0x0, 0x0,

    /* U+0071 "q" */
    0x2, 0xbb, 0x97, 0x30, 0xb1, 0x2, 0xd3, 0x37,
    0x0, 0x9, 0x35, 0x60, 0x0, 0x83, 0x37, 0x0,
    0x9, 0x30, 0xc1, 0x2, 0xd3, 0x3, 0xbb, 0x98,
    0x30, 0x0, 0x0, 0x73, 0x0, 0x0, 0x7, 0x30,
    0x0, 0x0, 0x73,

    /* U+0072 "r" */
    0x99, 0xb0, 0xe2, 0x0, 0xc0, 0x0, 0xa0, 0x0,
    0xa0, 0x0, 0xa0, 0x0, 0xa0, 0x0,

    /* U+0073 "s" */
    0x9, 0xab, 0x34, 0x70, 0x6, 0x2b, 0x10, 0x0,
    0x39, 0xa2, 0x0, 0x1, 0xc5, 0x30, 0xb, 0xa,
    0xab, 0x40,

    /* U+0074 "t" */
    0xb, 0x0, 0xb, 0x0, 0x8e, 0xa7, 0xb, 0x0,
    0xb, 0x0, 0xb, 0x0, 0xb, 0x0, 0xa, 0x0,
    0x4, 0xca,

    /* U+0075 "u" */
    0x19, 0x0, 0xa, 0x19, 0x0, 0xa, 0x19, 0x0,
    0xa, 0x19, 0x0, 0xa, 0xa, 0x0, 0xb, 0xb,
    0x0, 0x5c, 0x4, 0xcb, 0x69,

    /* U+0076 "v" */
    0xa1, 0x0, 0x19, 0x57, 0x0, 0x64, 0xb, 0x0,
    0xa0, 0x9, 0x21, 0x80, 0x3, 0x86, 0x30, 0x0,
    0xb9, 0x0, 0x0, 0x77, 0x0,

    /* U+0077 "w" */
    0xa0, 0x0, 0xc0, 0x0, 0xa6, 0x50, 0x1d, 0x50,
    0x18, 0x19, 0x6, 0x49, 0x6, 0x30, 0xa0, 0x90,
    0xa0, 0x90, 0x6, 0x48, 0x6, 0x49, 0x0, 0x1c,
    0x40, 0x1c, 0x50, 0x0, 0xb0, 0x0, 0xb0, 0x0,

    /* U+0078 "x" */
    0x75, 0x0, 0x93, 0xb, 0x13, 0x80, 0x3, 0xab,
    0x0, 0x0, 0xb6, 0x0, 0x3, 0x9b, 0x0, 0xb,
    0x4, 0x90, 0x74, 0x0, 0x93,

    /* U+0079 "y" */
    0xb1, 0x0, 0x19, 0x56, 0x0, 0x64, 0xb, 0x0,
    0xa0, 0x9, 0x22, 0x80, 0x3, 0x87, 0x20, 0x0,
    0xba, 0x0, 0x0, 0x77, 0x0, 0x0, 0x91, 0x0,
    0x1, 0xa0, 0x0, 0x9b, 0x20, 0x0,

    /* U+007A "z" */
    0x3a, 0xaa, 0xd0, 0x0, 0x7, 0x50, 0x0, 0x1a,
    0x0, 0x0, 0xa2, 0x0, 0x4, 0x80, 0x0, 0xb,
    0x0, 0x0, 0x7d, 0xaa, 0xa1,

    /* U+007B "{" */
    0x0, 0x88, 0x2, 0x90, 0x3, 0x70, 0x4, 0x70,
    0x4, 0x70, 0x7, 0x40, 0x8d, 0x0, 0x7, 0x40,
    0x4, 0x70, 0x4, 0x70, 0x3, 0x70, 0x2, 0x90,
    0x0, 0x88,

    /* U+007C "|" */
    0x64, 0x64, 0x64, 0x64, 0x64, 0x64, 0x64, 0x64,
    0x64, 0x64, 0x64, 0x64,

    /* U+007D "}" */
    0x88, 0x0, 0x8, 0x20, 0x6, 0x40, 0x6, 0x40,
    0x6, 0x40, 0x3, 0x80, 0x0, 0xc9, 0x3, 0x80,
    0x6, 0x40, 0x6, 0x40, 0x6, 0x40, 0x8, 0x20,
    0x88, 0x0,

    /* U+007E "~" */
    0x8, 0xb4, 0x8, 0x18, 0x8, 0xb3,

    /* U+626B "扫" */
    0x0, 0x55, 0x0, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x7a, 0xaa, 0xac, 0x1a, 0xbb, 0x90, 0x0, 0x0,
    0xa0, 0x5, 0x50, 0x0, 0x0, 0xa, 0x0, 0x55,
    0x20, 0x0, 0x0, 0xa0, 0x19, 0xc6, 0x5a, 0xaa,
    0xac, 0x38, 0x75, 0x0, 0x0, 0x0, 0xa0, 0x5,
    0x50, 0x0, 0x0, 0xa, 0x0, 0x55, 0x0, 0x0,
    0x0, 0xa0, 0x5, 0x42, 0xaa, 0xaa, 0xac, 0x9,
    0xc2, 0x0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x90,
    0x0, 0xb, 0x0, 0x0, 0x0, 0x90, 0x49, 0xaa,
    0x9b, 0x90, 0x29, 0xd9, 0x2, 0x70, 0x9, 0x0,
    0x0, 0x90, 0x0, 0x90, 0x36, 0x0, 0x0, 0x90,
    0x79, 0x99, 0x99, 0x93, 0x4, 0xc8, 0x0, 0x91,
    0x0, 0x0, 0x45, 0xa0, 0x9a, 0xc9, 0x9c, 0x93,
    0x0, 0x90, 0x9, 0x10, 0x18, 0x0, 0x0, 0x90,
    0x7, 0xa5, 0xa1, 0x0, 0x0, 0x90, 0x0, 0x3d,
    0xb4, 0x0, 0x19, 0x90, 0x8a, 0x60, 0x6, 0xa1,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+7259 "牙" */
    0x7, 0x99, 0x99, 0x9b, 0xb9, 0x90, 0x0, 0x62,
    0x0, 0x5, 0x40, 0x0, 0x0, 0xa0, 0x0, 0x5,
    0x40, 0x0, 0x0, 0xd9, 0x99, 0x9b, 0xb9, 0x91,
    0x0, 0x0, 0x3, 0x95, 0x40, 0x0, 0x0, 0x0,
    0x1b, 0x5, 0x40, 0x0, 0x0, 0x1, 0xb1, 0x5,
    0x40, 0x0, 0x0, 0x4b, 0x10, 0x5, 0x40, 0x0,
    0x1a, 0x90, 0x0, 0x5, 0x40, 0x0, 0x13, 0x0,
    0x6, 0x9b, 0x10, 0x0,

    /* U+7801 "码" */
    0x29, 0xda, 0x84, 0xa9, 0x9d, 0x0, 0x0, 0x90,
    0x0, 0x90, 0x9, 0x0, 0x1, 0x80, 0x0, 0x90,
    0x9, 0x0, 0x7, 0xb9, 0x42, 0x70, 0x9, 0x0,
    0xd, 0x51, 0x73, 0xb9, 0x9c, 0xa0, 0x55, 0x51,
    0x70, 0x0, 0x0, 0x90, 0x4, 0x51, 0x79, 0x99,
    0x95, 0x90, 0x4, 0x51, 0x70, 0x0, 0x0, 0x90,
    0x4, 0xba, 0x70, 0x0, 0x0, 0xa0, 0x0, 0x0,
    0x0, 0x8, 0x9a, 0x40,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x90, 0x6, 0x40, 0x0, 0x19, 0x99,
    0xd9, 0x9b, 0xa9, 0x93, 0x0, 0x0, 0x70, 0x6,
    0x20, 0x0, 0x1, 0x80, 0xa0, 0x3b, 0x88, 0x81,
    0x1, 0x80, 0xa0, 0x93, 0x41, 0x10, 0x1, 0x80,
    0xa4, 0x60, 0x87, 0x0, 0x0, 0x30, 0x51, 0x0,
    0x3, 0x10, 0x0, 0xa9, 0xa9, 0x9b, 0x9b, 0x10,
    0x0, 0xa0, 0x53, 0x9, 0x8, 0x10, 0x0, 0xa0,
    0x53, 0x9, 0x8, 0x10, 0x29, 0xd9, 0xba, 0x9d,
    0x9c, 0x96,

    /* U+8FDE "连" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x0,
    0x0, 0x73, 0x0, 0x0, 0x3, 0x90, 0x99, 0xd9,
    0x99, 0x92, 0x0, 0x54, 0x5, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xa, 0x8, 0x20, 0x0, 0x39, 0x90,
    0x4a, 0x4a, 0x54, 0x30, 0x0, 0x90, 0x25, 0x5a,
    0x65, 0x40, 0x0, 0x90, 0x0, 0x8, 0x20, 0x0,
    0x0, 0x91, 0x99, 0x9c, 0xa9, 0x93, 0x0, 0xc2,
    0x0, 0x8, 0x20, 0x0, 0x8, 0x4a, 0x20, 0x4,
    0x10, 0x0, 0x37, 0x0, 0x8a, 0x99, 0x88, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xd0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F043 "" */
    0x0, 0x2, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0x2f, 0xf7, 0x0, 0x0, 0xa, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xfc, 0xe,
    0xff, 0xff, 0xff, 0xf2, 0xf9, 0xcf, 0xff, 0xff,
    0x3d, 0xc5, 0xff, 0xff, 0xf1, 0x6f, 0xa3, 0xbf,
    0xfa, 0x0, 0x8f, 0xff, 0xfb, 0x0, 0x0, 0x26,
    0x74, 0x0, 0x0,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x26, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfd,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x99,
    0x99, 0x99, 0x99, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x12, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9a, 0xaa, 0xaa, 0xaa,
    0xaa, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0xc3, 0xbf, 0xff, 0xff, 0xfb, 0x3c,
    0xff, 0x57, 0xff, 0xff, 0x75, 0xff, 0xff, 0xf9,
    0x3d, 0xd3, 0x9f, 0xff, 0xff, 0xff, 0xd5, 0x5d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x1, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x1, 0xef, 0xfa, 0x5d,
    0x10, 0x0, 0x1, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x1, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x1, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 52, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 44, .box_w = 2, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10, .adv_w = 58, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 16, .adv_w = 119, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 56, .adv_w = 106, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 105, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 150, .adv_w = 132, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 195, .adv_w = 32, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 199, .adv_w = 62, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 223, .adv_w = 62, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 241, .adv_w = 79, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 254, .adv_w = 106, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 272, .adv_w = 44, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 276, .adv_w = 95, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 279, .adv_w = 42, .box_w = 2, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 281, .adv_w = 70, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 306, .adv_w = 106, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 341, .adv_w = 106, .box_w = 4, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 361, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 391, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 421, .adv_w = 106, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 456, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 486, .adv_w = 106, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 521, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 551, .adv_w = 106, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 586, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 616, .adv_w = 46, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 623, .adv_w = 48, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 637, .adv_w = 106, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 658, .adv_w = 106, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 670, .adv_w = 106, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 691, .adv_w = 80, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 716, .adv_w = 190, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 788, .adv_w = 125, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 828, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 863, .adv_w = 123, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 903, .adv_w = 136, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 938, .adv_w = 111, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 968, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 998, .adv_w = 132, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1038, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1073, .adv_w = 47, .box_w = 1, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1078, .adv_w = 83, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1103, .adv_w = 125, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1138, .adv_w = 106, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1168, .adv_w = 164, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1213, .adv_w = 139, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1248, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1293, .adv_w = 110, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1323, .adv_w = 145, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1377, .adv_w = 117, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1412, .adv_w = 106, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1447, .adv_w = 105, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1482, .adv_w = 136, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1517, .adv_w = 123, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1557, .adv_w = 183, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1617, .adv_w = 122, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1657, .adv_w = 114, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1692, .adv_w = 109, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1727, .adv_w = 60, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1747, .adv_w = 70, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1772, .adv_w = 60, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1792, .adv_w = 86, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1805, .adv_w = 77, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1808, .adv_w = 50, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 1811, .adv_w = 104, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1832, .adv_w = 115, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1862, .adv_w = 94, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1883, .adv_w = 115, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1918, .adv_w = 103, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1939, .adv_w = 63, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1964, .adv_w = 115, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1999, .adv_w = 109, .box_w = 5, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2024, .adv_w = 44, .box_w = 2, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2034, .adv_w = 44, .box_w = 4, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2060, .adv_w = 96, .box_w = 5, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2085, .adv_w = 44, .box_w = 1, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2090, .adv_w = 162, .box_w = 9, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2122, .adv_w = 109, .box_w = 5, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2140, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2165, .adv_w = 115, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2195, .adv_w = 115, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2230, .adv_w = 68, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2244, .adv_w = 86, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2262, .adv_w = 66, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2280, .adv_w = 109, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2301, .adv_w = 95, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2322, .adv_w = 146, .box_w = 9, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2354, .adv_w = 91, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2375, .adv_w = 95, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2405, .adv_w = 89, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2426, .adv_w = 65, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2452, .adv_w = 30, .box_w = 2, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2464, .adv_w = 65, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2490, .adv_w = 106, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 2496, .adv_w = 192, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2562, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2640, .adv_w = 192, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2700, .adv_w = 192, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2760, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2826, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2904, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2982, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3036, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3102, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3156, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3197, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3275, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3353, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3430, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3508, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3571, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3649, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3679, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3724, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3815, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3869, .adv_w = 132, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3928, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3976, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4048, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4109, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4170, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4218, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4284, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4323, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4362, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4423, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4440, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4503, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4607, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4705, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4771, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4810, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4849, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4929, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4983, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5061, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5146, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5207, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5279, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5340, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5401, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5455, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5514, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5586, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5658, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5721, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5812, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5871, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5961, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6029, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6097, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6165, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6233, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6301, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6389, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6454, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6526, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6611, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6679, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6738, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x13a, 0xfee, 0x1596, 0x2272, 0x2d73, 0x8d96, 0x8d9d,
    0x8da0, 0x8da1, 0x8da2, 0x8da6, 0x8da8, 0x8daa, 0x8dae, 0x8db1,
    0x8db6, 0x8dbb, 0x8dbc, 0x8dbd, 0x8dd3, 0x8dd8, 0x8ddd, 0x8de0,
    0x8de1, 0x8de2, 0x8de6, 0x8de7, 0x8de8, 0x8de9, 0x8dfc, 0x8dfd,
    0x8e03, 0x8e05, 0x8e06, 0x8e09, 0x8e0c, 0x8e0d, 0x8e0e, 0x8e10,
    0x8e28, 0x8e2a, 0x8e59, 0x8e5a, 0x8e5c, 0x8e5e, 0x8e75, 0x8e7c,
    0x8e7f, 0x8e88, 0x8eb1, 0x8eb9, 0x8ef0, 0x8f80, 0x8fd5, 0x8fd6,
    0x8fd7, 0x8fd8, 0x8fd9, 0x901c, 0x9028, 0x9082, 0x9099, 0x92ef,
    0x9557, 0x9637
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 25195, .range_length = 38456, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 66, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 28, 30, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 4, 4, 2,
    0, 2, 0, 0, 10, 0, 0, 3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 5, 4, -8, -25, -16, 6, -5,
    0, -25, -1, 4, 0, 0, 0, 0,
    0, 0, -14, 0, -12, -4, 0, -9,
    -10, -1, -8, -6, -8, -6, 0, 0,
    0, -5, -19, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    -8, -5, 0, 0, 0, -7, 0, -6,
    0, -5, -3, -5, -9, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -7, -21, 0, -11, 4, 0,
    -13, -5, 0, 0, 0, -16, -2, -16,
    -11, 0, -18, 4, 0, 0, -1, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, -2, 0, 0, 0, -2, 0, 0,
    0, 3, 0, -7, 0, -7, -2, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, -4, 4, 0, 6, -3, 0, 0,
    0, 1, 0, 1, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    2, 0, 1, -2, 0, 2, 0, 0,
    0, -2, 0, 0, -2, 0, -3, 0,
    -2, -3, 0, 0, -2, -2, -2, -3,
    -1, 0, -2, 5, 0, 1, -25, -12,
    6, -2, 0, -31, 0, 3, 0, 0,
    0, 0, 0, 0, -10, 0, -8, -4,
    0, -6, 0, -4, 0, -4, -8, -7,
    0, 0, 0, 0, 3, 0, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, -2, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -7, 0, 0, -20, 4, 0,
    2, -10, -1, 0, -2, 0, -3, 0,
    0, 0, 0, 0, -4, 0, -6, -7,
    0, -2, -2, -5, -6, -9, -5, 0,
    -9, -18, 0, -17, 5, 0, -14, -10,
    0, 3, -3, -25, -9, -29, -20, 0,
    -33, 0, -3, 0, -6, -5, 0, -1,
    -1, -6, -6, -17, 0, 0, -2, 4,
    0, 1, -26, -13, 3, 0, 0, -30,
    0, 0, 0, 1, 1, -2, 0, -6,
    -7, 0, -7, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -1, 0, -1,
    8, 0, -1, -2, 0, 0, 1, -2,
    -2, -4, -2, 0, -8, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 4, 0, 0, -2,
    0, 0, -2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 5, 0, -17, -19, -16, 6, -6,
    0, -26, 0, 3, 0, 4, 4, 0,
    0, 0, -22, 0, -19, -8, 0, -17,
    -21, -9, -16, -17, -19, -17, -2, 3,
    0, -4, -14, -11, 0, -3, 0, -13,
    0, 4, 0, 0, 0, 0, 0, 0,
    -14, 0, -11, -2, 0, -8, -8, 0,
    -4, -3, -4, -6, 0, 0, 4, -16,
    2, 0, 4, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -5, 0, 0, -2, -2, -4, -5, -8,
    0, 0, -6, 4, 4, -13, -22, -18,
    2, -9, 0, -26, -2, 0, 0, 0,
    0, 0, 0, 0, -21, 0, -18, -8,
    0, -14, -17, -5, -13, -12, -12, -13,
    0, 0, 2, -9, 3, 0, 2, -6,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -3, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, -7,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, -16, 0, -12, -11, -1, -17, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, -9, 0, -2,
    -6, 0, -7, 0, 0, 0, 0, -19,
    0, -12, -11, -5, -18, 0, -2, 0,
    0, -2, 0, 0, 0, -1, 0, -3,
    -4, -4, 0, 1, 0, 4, 5, 0,
    -2, 0, 0, 0, 0, -12, 0, -8,
    -4, 4, -12, 0, 0, 0, -1, 2,
    0, 0, 0, 3, 0, 0, 2, 2,
    0, 0, 2, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 3, 1, 0, -4, 0, 0, 0,
    0, -12, 0, -10, -7, -2, -14, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 1, 0, 0, 0, 9, 0, -1,
    -13, 0, 8, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -4, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, -17, 0, -9,
    -8, 0, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    0, -3, 5, 0, -7, 0, 0, 0,
    0, -15, 0, -9, -6, 0, -12, 0,
    -5, 0, -4, 0, 0, 0, -2, 0,
    -1, 0, 0, 0, 0, 5, 0, 2,
    -18, -6, -5, 0, 0, -19, 0, 0,
    0, -6, 0, -6, -11, 0, -8, 0,
    -6, 0, 0, 0, -1, 4, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, -17, 0, -12,
    -9, 0, -18, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, -1, 0, 2,
    0, -1, 1, -2, 5, 0, -5, 0,
    0, 0, 0, -15, 0, -7, 0, 0,
    -12, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -14, -6, -4, 0, 0, -13,
    0, -17, 0, -6, -3, -8, -12, 0,
    -2, 0, -3, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    2, 0, -7, 0, 0, 0, 0, -19,
    0, -8, -4, 0, -12, 0, -3, 0,
    -4, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -7, 0, 0, 0, 0, -19, 0, -6,
    -4, 0, -14, 0, -4, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 30,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Light_12 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Light_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 12,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_12*/

