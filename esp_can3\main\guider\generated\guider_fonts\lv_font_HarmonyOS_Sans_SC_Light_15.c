/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_LIGHT_15
#define LV_FONT_HARMONYOS_SANS_SC_LIGHT_15 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_15

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xb2, 0xb1, 0xb1, 0xb1, 0xa1, 0xa1, 0xa0, 0xa0,
    0x50, 0x20, 0xc3,

    /* U+0022 "\"" */
    0x4, 0x4, 0x1a, 0x1a, 0x19, 0x1a, 0x19, 0x1a,

    /* U+0023 "#" */
    0x0, 0x5, 0x70, 0xc, 0x0, 0x0, 0x9, 0x30,
    0xc, 0x0, 0x0, 0xc, 0x0, 0x38, 0x0, 0x9,
    0xce, 0xcc, 0xed, 0xc0, 0x0, 0x48, 0x0, 0xb1,
    0x0, 0x0, 0x84, 0x0, 0xc0, 0x0, 0x0, 0xc0,
    0x3, 0x90, 0x0, 0x9c, 0xfc, 0xce, 0xdc, 0x0,
    0x4, 0x90, 0xb, 0x10, 0x0, 0x7, 0x50, 0xc,
    0x0, 0x0, 0xb, 0x10, 0x2a, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x2, 0xb0, 0x0, 0x0, 0x2, 0xb0, 0x0,
    0x2, 0xbc, 0xed, 0x50, 0xc, 0x32, 0xb0, 0xb3,
    0xc, 0x2, 0xb0, 0x22, 0xe, 0x12, 0xb0, 0x0,
    0x5, 0xd7, 0xb0, 0x0, 0x0, 0x29, 0xf9, 0x10,
    0x0, 0x2, 0xb6, 0xd2, 0x0, 0x2, 0xb0, 0x59,
    0x57, 0x2, 0xb0, 0x3a, 0xc, 0x52, 0xb0, 0xb5,
    0x1, 0x9d, 0xfd, 0x70, 0x0, 0x2, 0xb0, 0x0,
    0x0, 0x2, 0xb0, 0x0,

    /* U+0025 "%" */
    0x1a, 0xbb, 0x30, 0x0, 0xc2, 0x8, 0x50, 0x1d,
    0x0, 0x68, 0x0, 0x87, 0x2, 0xd0, 0x1c, 0x0,
    0x0, 0x9b, 0xa2, 0x9, 0x50, 0x0, 0x0, 0x0,
    0x3, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xc2, 0x0,
    0x0, 0x0, 0x0, 0x68, 0x8, 0xba, 0x0, 0x0,
    0x1c, 0x6, 0x80, 0x4a, 0x0, 0x9, 0x50, 0xa2,
    0x0, 0xd0, 0x3, 0xb0, 0x7, 0x60, 0x3b, 0x0,
    0xc2, 0x0, 0xa, 0xcc, 0x10,

    /* U+0026 "&" */
    0x0, 0x8, 0xdc, 0x40, 0x0, 0x0, 0x9, 0x80,
    0x1d, 0x20, 0x0, 0x0, 0xd2, 0x0, 0x95, 0x0,
    0x0, 0xb, 0x40, 0xc, 0x30, 0x0, 0x0, 0x4c,
    0x2b, 0x60, 0x0, 0x0, 0x0, 0xde, 0x20, 0x4,
    0x20, 0x2, 0xc4, 0xa8, 0x0, 0xa2, 0x0, 0xd2,
    0x0, 0xb6, 0xd, 0x0, 0x2c, 0x0, 0x0, 0xc9,
    0x90, 0x2, 0xd0, 0x0, 0x1, 0xf5, 0x0, 0xc,
    0x60, 0x1, 0xb7, 0xd2, 0x0, 0x19, 0xdc, 0xc4,
    0x3, 0xd1,

    /* U+0027 "'" */
    0x4, 0x1a, 0x19, 0x19,

    /* U+0028 "(" */
    0x0, 0xa2, 0x6, 0x70, 0xd, 0x0, 0x58, 0x0,
    0xa4, 0x0, 0xc1, 0x0, 0xe0, 0x0, 0xe0, 0x0,
    0xc1, 0x0, 0xa3, 0x0, 0x58, 0x0, 0xd, 0x0,
    0x7, 0x70, 0x0, 0xa2,

    /* U+0029 ")" */
    0x58, 0x0, 0xa, 0x40, 0x2, 0xc0, 0x0, 0xb2,
    0x0, 0x77, 0x0, 0x49, 0x0, 0x3b, 0x0, 0x3b,
    0x0, 0x49, 0x0, 0x77, 0x0, 0xb2, 0x2, 0xc0,
    0xa, 0x40, 0x58, 0x0,

    /* U+002A "*" */
    0x0, 0x46, 0x0, 0x39, 0x46, 0x85, 0x3, 0xde,
    0x40, 0x2a, 0x9a, 0xb3, 0x11, 0x36, 0x1, 0x0,
    0x12, 0x0,

    /* U+002B "+" */
    0x0, 0x2, 0x50, 0x0, 0x0, 0x3, 0x80, 0x0,
    0x0, 0x3, 0x80, 0x0, 0x1c, 0xcc, 0xec, 0xc5,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x3, 0x80, 0x0,
    0x0, 0x3, 0x80, 0x0,

    /* U+002C "," */
    0x2, 0x0, 0xb6, 0x3, 0x50, 0x80,

    /* U+002D "-" */
    0xbb, 0xbb, 0xb4,

    /* U+002E "." */
    0x20, 0xd2,

    /* U+002F "/" */
    0x0, 0x0, 0xc1, 0x0, 0x2, 0xb0, 0x0, 0x8,
    0x50, 0x0, 0xd, 0x0, 0x0, 0x49, 0x0, 0x0,
    0xa3, 0x0, 0x1, 0xc0, 0x0, 0x6, 0x70, 0x0,
    0xc, 0x10, 0x0, 0x2b, 0x0, 0x0, 0x85, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x7d, 0xda, 0x0, 0x6, 0xa0, 0x6, 0xa0,
    0xd, 0x10, 0x0, 0xd2, 0x1d, 0x0, 0x0, 0x96,
    0x3b, 0x0, 0x0, 0x77, 0x3b, 0x0, 0x0, 0x68,
    0x3b, 0x0, 0x0, 0x77, 0x1d, 0x0, 0x0, 0x96,
    0xd, 0x10, 0x0, 0xd2, 0x6, 0xa0, 0x6, 0xa0,
    0x0, 0x7d, 0xda, 0x10,

    /* U+0031 "1" */
    0x0, 0x5e, 0x42, 0xc9, 0xa4, 0x32, 0x9, 0x40,
    0x0, 0x94, 0x0, 0x9, 0x40, 0x0, 0x94, 0x0,
    0x9, 0x40, 0x0, 0x94, 0x0, 0x9, 0x40, 0x0,
    0x94, 0x0, 0x9, 0x40,

    /* U+0032 "2" */
    0x0, 0x8d, 0xdb, 0x20, 0x9, 0x70, 0x5, 0xc0,
    0x9, 0x0, 0x0, 0xd1, 0x0, 0x0, 0x0, 0xd1,
    0x0, 0x0, 0x4, 0xc0, 0x0, 0x0, 0x1d, 0x20,
    0x0, 0x0, 0xc5, 0x0, 0x0, 0xa, 0x70, 0x0,
    0x0, 0x99, 0x0, 0x0, 0x7, 0xa0, 0x0, 0x0,
    0x3f, 0xdc, 0xcc, 0xc6,

    /* U+0033 "3" */
    0x0, 0x9d, 0xda, 0x10, 0xa, 0x70, 0x7, 0xa0,
    0x8, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0xa, 0x80, 0x0, 0xc, 0xfc, 0x10,
    0x0, 0x0, 0x5, 0xd1, 0x0, 0x0, 0x0, 0x95,
    0x14, 0x0, 0x0, 0x96, 0xd, 0x40, 0x3, 0xe1,
    0x1, 0xad, 0xdb, 0x30,

    /* U+0034 "4" */
    0x0, 0x0, 0x69, 0x0, 0x0, 0x0, 0x1d, 0x10,
    0x0, 0x0, 0x9, 0x60, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0x0, 0x0, 0xb3, 0x5, 0x40, 0x0, 0x59,
    0x0, 0x76, 0x0, 0xd, 0x10, 0x7, 0x60, 0x5,
    0xdc, 0xcc, 0xed, 0xc0, 0x0, 0x0, 0x7, 0x60,
    0x0, 0x0, 0x0, 0x76, 0x0, 0x0, 0x0, 0x7,
    0x60, 0x0,

    /* U+0035 "5" */
    0xf, 0xcc, 0xcc, 0x2, 0xb0, 0x0, 0x0, 0x49,
    0x0, 0x0, 0x6, 0x70, 0x0, 0x0, 0x8a, 0xcd,
    0x91, 0xa, 0x70, 0x6, 0xd0, 0x0, 0x0, 0xa,
    0x40, 0x0, 0x0, 0x77, 0x30, 0x0, 0xa, 0x5a,
    0x60, 0x4, 0xd0, 0x9, 0xdd, 0xa1, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x78, 0x0, 0x0, 0x2, 0xd0, 0x0,
    0x0, 0xb, 0x40, 0x0, 0x0, 0x59, 0x0, 0x0,
    0x1, 0xec, 0xdb, 0x20, 0x8, 0xa1, 0x4, 0xe1,
    0xe, 0x0, 0x0, 0x78, 0x2d, 0x0, 0x0, 0x4a,
    0xe, 0x0, 0x0, 0x78, 0x9, 0x90, 0x2, 0xd2,
    0x0, 0x8d, 0xdc, 0x30,

    /* U+0037 "7" */
    0x1c, 0xcc, 0xcc, 0xe6, 0x0, 0x0, 0x0, 0xd1,
    0x0, 0x0, 0x4, 0xa0, 0x0, 0x0, 0xa, 0x40,
    0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x77, 0x0,
    0x0, 0x0, 0xd1, 0x0, 0x0, 0x4, 0xa0, 0x0,
    0x0, 0xa, 0x40, 0x0, 0x0, 0x1d, 0x0, 0x0,
    0x0, 0x77, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x9d, 0xcb, 0x20, 0xa, 0x70, 0x3, 0xd0,
    0xd, 0x10, 0x0, 0xc2, 0x8, 0x80, 0x4, 0xc0,
    0x0, 0xbf, 0xee, 0x10, 0x8, 0x90, 0x5, 0xc0,
    0x1d, 0x0, 0x0, 0x96, 0x3b, 0x0, 0x0, 0x68,
    0x2d, 0x0, 0x0, 0x86, 0xb, 0x70, 0x3, 0xd1,
    0x0, 0x9d, 0xdb, 0x20,

    /* U+0039 "9" */
    0x1, 0x9d, 0xda, 0x10, 0xc, 0x60, 0x5, 0xd0,
    0x3c, 0x0, 0x0, 0xa5, 0x59, 0x0, 0x0, 0x87,
    0x3c, 0x0, 0x0, 0xb4, 0xc, 0x70, 0x6, 0xd0,
    0x1, 0x9d, 0xce, 0x40, 0x0, 0x0, 0x4a, 0x0,
    0x0, 0x0, 0xc1, 0x0, 0x0, 0x9, 0x60, 0x0,
    0x0, 0x3b, 0x0, 0x0,

    /* U+003A ":" */
    0xa4, 0x20, 0x0, 0x0, 0x0, 0x0, 0x20, 0xa4,

    /* U+003B ";" */
    0x86, 0x11, 0x0, 0x0, 0x0, 0x0, 0x10, 0x98,
    0x17, 0x80,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x34, 0x0, 0x0, 0x6c, 0x92,
    0x1, 0x8c, 0x71, 0x0, 0x1f, 0x80, 0x0, 0x0,
    0x3, 0x9c, 0x50, 0x0, 0x0, 0x1, 0x7c, 0x81,
    0x0, 0x0, 0x0, 0x54,

    /* U+003D "=" */
    0x1c, 0xcc, 0xcc, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xcc, 0xcc, 0xc5,

    /* U+003E ">" */
    0x15, 0x0, 0x0, 0x0, 0x7, 0xc8, 0x10, 0x0,
    0x0, 0x5, 0xca, 0x30, 0x0, 0x0, 0x5, 0xf6,
    0x0, 0x3, 0xab, 0x50, 0x6, 0xc9, 0x20, 0x0,
    0x17, 0x10, 0x0, 0x0,

    /* U+003F "?" */
    0x7, 0xcd, 0x90, 0x88, 0x0, 0x8a, 0x50, 0x0,
    0xe, 0x0, 0x0, 0x2c, 0x0, 0x0, 0xc3, 0x0,
    0xb, 0x40, 0x0, 0x68, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x77, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x28, 0xbb, 0xba, 0x40, 0x0, 0x0,
    0x0, 0x9a, 0x30, 0x0, 0x28, 0xb2, 0x0, 0x0,
    0xb5, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x68,
    0x0, 0x5c, 0xca, 0x46, 0x6, 0x70, 0xc, 0x10,
    0x3c, 0x10, 0x2d, 0x80, 0xc, 0x0, 0xc0, 0x9,
    0x50, 0x0, 0x78, 0x0, 0xc0, 0x1a, 0x0, 0xa3,
    0x0, 0x5, 0x80, 0xb, 0x10, 0xc0, 0x8, 0x50,
    0x0, 0x88, 0x0, 0xc0, 0xc, 0x0, 0x2c, 0x10,
    0x3a, 0xb0, 0x3a, 0x0, 0x67, 0x0, 0x3b, 0xc9,
    0x6, 0xca, 0x10, 0x0, 0xb5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaa, 0x30, 0x0, 0x29,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xbc, 0xc9, 0x30,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xc5, 0x0, 0x0, 0x0, 0x2, 0xcc,
    0x0, 0x0, 0x0, 0x8, 0x5c, 0x20, 0x0, 0x0,
    0xd, 0x6, 0x90, 0x0, 0x0, 0x59, 0x1, 0xe0,
    0x0, 0x0, 0xb4, 0x0, 0xa5, 0x0, 0x1, 0xe0,
    0x0, 0x5c, 0x0, 0x7, 0xed, 0xdd, 0xdf, 0x20,
    0xd, 0x20, 0x0, 0x8, 0x80, 0x3b, 0x0, 0x0,
    0x2, 0xe0, 0x95, 0x0, 0x0, 0x0, 0xa5,

    /* U+0042 "B" */
    0x9d, 0xcc, 0xd9, 0x10, 0x94, 0x0, 0x7, 0xb0,
    0x94, 0x0, 0x0, 0xd1, 0x94, 0x0, 0x0, 0xd1,
    0x94, 0x0, 0x8, 0xa0, 0x9d, 0xcc, 0xfe, 0x30,
    0x94, 0x0, 0x3, 0xd4, 0x94, 0x0, 0x0, 0x4b,
    0x94, 0x0, 0x0, 0x3b, 0x94, 0x0, 0x1, 0xc5,
    0x9d, 0xcc, 0xcc, 0x50,

    /* U+0043 "C" */
    0x0, 0x6, 0xcd, 0xd8, 0x0, 0x0, 0xb9, 0x10,
    0x7, 0xb0, 0x6, 0x90, 0x0, 0x0, 0x50, 0xd,
    0x20, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x20, 0x0, 0x0, 0x0,
    0x6, 0xa0, 0x0, 0x0, 0x50, 0x0, 0xb9, 0x10,
    0x7, 0xb0, 0x0, 0x6, 0xcd, 0xd9, 0x0,

    /* U+0044 "D" */
    0x9d, 0xcc, 0xc8, 0x10, 0x9, 0x40, 0x0, 0x5d,
    0x30, 0x94, 0x0, 0x0, 0x2d, 0x9, 0x40, 0x0,
    0x0, 0x96, 0x94, 0x0, 0x0, 0x4, 0xa9, 0x40,
    0x0, 0x0, 0x3b, 0x94, 0x0, 0x0, 0x4, 0xa9,
    0x40, 0x0, 0x0, 0x96, 0x94, 0x0, 0x0, 0x2d,
    0x9, 0x40, 0x0, 0x5d, 0x30, 0x9d, 0xcc, 0xc9,
    0x10, 0x0,

    /* U+0045 "E" */
    0x9d, 0xcc, 0xcc, 0x99, 0x40, 0x0, 0x0, 0x94,
    0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x9, 0xdc, 0xcc, 0xc2, 0x94, 0x0, 0x0,
    0x9, 0x40, 0x0, 0x0, 0x94, 0x0, 0x0, 0x9,
    0x40, 0x0, 0x0, 0x9d, 0xcc, 0xcc, 0xb0,

    /* U+0046 "F" */
    0x9d, 0xcc, 0xcc, 0x99, 0x40, 0x0, 0x0, 0x94,
    0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x9, 0xdc, 0xcc, 0xc2, 0x94, 0x0, 0x0,
    0x9, 0x40, 0x0, 0x0, 0x94, 0x0, 0x0, 0x9,
    0x40, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x5, 0xcd, 0xdb, 0x30, 0x0, 0x9a, 0x10,
    0x3, 0xc3, 0x6, 0xa0, 0x0, 0x0, 0x10, 0xd,
    0x20, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0xbd, 0xd7, 0xe, 0x0,
    0x0, 0x0, 0x58, 0xd, 0x20, 0x0, 0x0, 0x58,
    0x6, 0xb0, 0x0, 0x0, 0x58, 0x0, 0x9a, 0x10,
    0x2, 0xc5, 0x0, 0x5, 0xcd, 0xdb, 0x40,

    /* U+0048 "H" */
    0x94, 0x0, 0x0, 0x5, 0x89, 0x40, 0x0, 0x0,
    0x58, 0x94, 0x0, 0x0, 0x5, 0x89, 0x40, 0x0,
    0x0, 0x58, 0x94, 0x0, 0x0, 0x5, 0x89, 0xdc,
    0xcc, 0xcc, 0xd8, 0x94, 0x0, 0x0, 0x5, 0x89,
    0x40, 0x0, 0x0, 0x58, 0x94, 0x0, 0x0, 0x5,
    0x89, 0x40, 0x0, 0x0, 0x58, 0x94, 0x0, 0x0,
    0x5, 0x80,

    /* U+0049 "I" */
    0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94,
    0x94, 0x94, 0x94,

    /* U+004A "J" */
    0x0, 0x0, 0xa3, 0x0, 0x0, 0xa3, 0x0, 0x0,
    0xa3, 0x0, 0x0, 0xa3, 0x0, 0x0, 0xa3, 0x0,
    0x0, 0xa3, 0x0, 0x0, 0xa3, 0x0, 0x0, 0xa3,
    0x0, 0x0, 0xb2, 0xa4, 0x2, 0xd0, 0x2c, 0xdc,
    0x30,

    /* U+004B "K" */
    0x94, 0x0, 0x0, 0x6b, 0x9, 0x40, 0x0, 0x5c,
    0x0, 0x94, 0x0, 0x4c, 0x0, 0x9, 0x40, 0x3c,
    0x10, 0x0, 0x94, 0x3e, 0x10, 0x0, 0x9, 0x7c,
    0xb7, 0x0, 0x0, 0x9e, 0x20, 0xd3, 0x0, 0x9,
    0x50, 0x3, 0xd1, 0x0, 0x94, 0x0, 0x6, 0xb0,
    0x9, 0x40, 0x0, 0xa, 0x70, 0x94, 0x0, 0x0,
    0xd, 0x30,

    /* U+004C "L" */
    0x94, 0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x94,
    0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x9, 0x40, 0x0, 0x0, 0x94, 0x0, 0x0,
    0x9, 0x40, 0x0, 0x0, 0x94, 0x0, 0x0, 0x9,
    0x40, 0x0, 0x0, 0x9d, 0xdd, 0xdd, 0x90,

    /* U+004D "M" */
    0x97, 0x0, 0x0, 0x0, 0xa, 0x69, 0xf2, 0x0,
    0x0, 0x4, 0xf6, 0x98, 0xb0, 0x0, 0x0, 0xd9,
    0x69, 0x4a, 0x60, 0x0, 0x86, 0x76, 0x94, 0x1d,
    0x10, 0x3b, 0x7, 0x69, 0x40, 0x5a, 0xc, 0x20,
    0x76, 0x94, 0x0, 0xba, 0x70, 0x7, 0x69, 0x40,
    0x1, 0xb0, 0x0, 0x76, 0x94, 0x0, 0x0, 0x0,
    0x7, 0x69, 0x40, 0x0, 0x0, 0x0, 0x76, 0x94,
    0x0, 0x0, 0x0, 0x7, 0x60,

    /* U+004E "N" */
    0x97, 0x0, 0x0, 0x6, 0x79, 0xe3, 0x0, 0x0,
    0x67, 0x96, 0xc0, 0x0, 0x6, 0x79, 0x37, 0x90,
    0x0, 0x67, 0x93, 0xb, 0x40, 0x6, 0x79, 0x30,
    0x2d, 0x0, 0x67, 0x93, 0x0, 0x6a, 0x6, 0x79,
    0x30, 0x0, 0xb5, 0x67, 0x93, 0x0, 0x1, 0xd7,
    0x79, 0x30, 0x0, 0x5, 0xf7, 0x93, 0x0, 0x0,
    0xa, 0x70,

    /* U+004F "O" */
    0x0, 0x7, 0xcd, 0xd9, 0x10, 0x0, 0xb, 0x80,
    0x0, 0x5d, 0x20, 0x7, 0x90, 0x0, 0x0, 0x4b,
    0x0, 0xd1, 0x0, 0x0, 0x0, 0xd2, 0xe, 0x0,
    0x0, 0x0, 0x9, 0x52, 0xc0, 0x0, 0x0, 0x0,
    0x76, 0xe, 0x0, 0x0, 0x0, 0x9, 0x50, 0xd2,
    0x0, 0x0, 0x0, 0xd2, 0x7, 0x90, 0x0, 0x0,
    0x4b, 0x0, 0xb, 0x80, 0x0, 0x5d, 0x20, 0x0,
    0x7, 0xcd, 0xd9, 0x10, 0x0,

    /* U+0050 "P" */
    0x9d, 0xcc, 0xc6, 0x0, 0x94, 0x0, 0x1a, 0x80,
    0x94, 0x0, 0x0, 0xe0, 0x94, 0x0, 0x0, 0xd1,
    0x94, 0x0, 0x1, 0xe0, 0x94, 0x0, 0x2b, 0x70,
    0x9d, 0xcc, 0xb5, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x94, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x94, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x7, 0xcd, 0xd9, 0x10, 0x0, 0x0, 0xb8,
    0x0, 0x5, 0xd2, 0x0, 0x7, 0x90, 0x0, 0x0,
    0x4b, 0x0, 0xd, 0x10, 0x0, 0x0, 0xd, 0x20,
    0xe, 0x0, 0x0, 0x0, 0x9, 0x50, 0x2c, 0x0,
    0x0, 0x0, 0x7, 0x60, 0xe, 0x0, 0x0, 0x0,
    0x9, 0x50, 0xd, 0x20, 0x0, 0x0, 0xc, 0x20,
    0x7, 0x90, 0x0, 0x0, 0x4b, 0x0, 0x0, 0xb8,
    0x0, 0x5, 0xd1, 0x0, 0x0, 0x7, 0xcd, 0xdf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0x70,

    /* U+0052 "R" */
    0x9d, 0xcc, 0xc7, 0x0, 0x9, 0x40, 0x0, 0x9a,
    0x0, 0x94, 0x0, 0x0, 0xe0, 0x9, 0x40, 0x0,
    0xe, 0x0, 0x94, 0x0, 0x1a, 0x80, 0x9, 0xdc,
    0xde, 0x50, 0x0, 0x94, 0x0, 0xd2, 0x0, 0x9,
    0x40, 0x4, 0xc0, 0x0, 0x94, 0x0, 0x9, 0x70,
    0x9, 0x40, 0x0, 0xd, 0x20, 0x94, 0x0, 0x0,
    0x4c, 0x0,

    /* U+0053 "S" */
    0x1, 0xad, 0xdc, 0x40, 0xd, 0x50, 0x1, 0xc4,
    0x2d, 0x0, 0x0, 0x22, 0x1e, 0x10, 0x0, 0x0,
    0x5, 0xd6, 0x10, 0x0, 0x0, 0x16, 0xcb, 0x40,
    0x0, 0x0, 0x2, 0xd4, 0x0, 0x0, 0x0, 0x4b,
    0x65, 0x0, 0x0, 0x3a, 0x2d, 0x50, 0x1, 0xb5,
    0x2, 0xad, 0xdc, 0x50,

    /* U+0054 "T" */
    0x9c, 0xcd, 0xec, 0xcc, 0x0, 0x5, 0x80, 0x0,
    0x0, 0x5, 0x80, 0x0, 0x0, 0x5, 0x80, 0x0,
    0x0, 0x5, 0x80, 0x0, 0x0, 0x5, 0x80, 0x0,
    0x0, 0x5, 0x80, 0x0, 0x0, 0x5, 0x80, 0x0,
    0x0, 0x5, 0x80, 0x0, 0x0, 0x5, 0x80, 0x0,
    0x0, 0x5, 0x80, 0x0,

    /* U+0055 "U" */
    0xc2, 0x0, 0x0, 0x8, 0x6c, 0x20, 0x0, 0x0,
    0x86, 0xc2, 0x0, 0x0, 0x8, 0x6c, 0x20, 0x0,
    0x0, 0x86, 0xc2, 0x0, 0x0, 0x8, 0x6c, 0x20,
    0x0, 0x0, 0x86, 0xb2, 0x0, 0x0, 0x8, 0x5a,
    0x40, 0x0, 0x0, 0xa4, 0x69, 0x0, 0x0, 0x1e,
    0x0, 0xd6, 0x0, 0x1b, 0x70, 0x1, 0x9d, 0xdd,
    0x60, 0x0,

    /* U+0056 "V" */
    0x96, 0x0, 0x0, 0x0, 0xb3, 0x3c, 0x0, 0x0,
    0x1, 0xd0, 0xd, 0x20, 0x0, 0x6, 0x70, 0x7,
    0x80, 0x0, 0xc, 0x10, 0x1, 0xd0, 0x0, 0x2b,
    0x0, 0x0, 0xa4, 0x0, 0x85, 0x0, 0x0, 0x4a,
    0x0, 0xc0, 0x0, 0x0, 0xd, 0x14, 0x90, 0x0,
    0x0, 0x7, 0x6a, 0x30, 0x0, 0x0, 0x1, 0xcc,
    0x0, 0x0, 0x0, 0x0, 0xb7, 0x0, 0x0,

    /* U+0057 "W" */
    0xa5, 0x0, 0x0, 0x5b, 0x0, 0x0, 0xd, 0x5,
    0x90, 0x0, 0xa, 0xf0, 0x0, 0x3, 0xa0, 0xe,
    0x0, 0x0, 0xd9, 0x50, 0x0, 0x75, 0x0, 0xb3,
    0x0, 0x39, 0x4a, 0x0, 0xc, 0x0, 0x6, 0x80,
    0x7, 0x50, 0xd0, 0x1, 0xb0, 0x0, 0x1d, 0x0,
    0xc0, 0xa, 0x40, 0x57, 0x0, 0x0, 0xc2, 0x1b,
    0x0, 0x59, 0xa, 0x20, 0x0, 0x7, 0x65, 0x60,
    0x0, 0xd0, 0xc0, 0x0, 0x0, 0x2b, 0xa1, 0x0,
    0xb, 0x68, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x6e, 0x30, 0x0, 0x0, 0x8, 0x80, 0x0, 0x1,
    0xe0, 0x0, 0x0,

    /* U+0058 "X" */
    0x4c, 0x0, 0x0, 0x3, 0xc0, 0x9, 0x80, 0x0,
    0xc, 0x20, 0x0, 0xc3, 0x0, 0x96, 0x0, 0x0,
    0x2d, 0x14, 0xb0, 0x0, 0x0, 0x6, 0xbc, 0x10,
    0x0, 0x0, 0x0, 0xe7, 0x0, 0x0, 0x0, 0x8,
    0x8d, 0x20, 0x0, 0x0, 0x3b, 0x4, 0xc0, 0x0,
    0x1, 0xd1, 0x0, 0x88, 0x0, 0xa, 0x50, 0x0,
    0xc, 0x40, 0x69, 0x0, 0x0, 0x2, 0xd1,

    /* U+0059 "Y" */
    0x89, 0x0, 0x0, 0x8, 0x70, 0xd3, 0x0, 0x2,
    0xc0, 0x3, 0xc0, 0x0, 0xb4, 0x0, 0x9, 0x70,
    0x4a, 0x0, 0x0, 0x1d, 0x2c, 0x10, 0x0, 0x0,
    0x5e, 0x70, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0xe0, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0x0,

    /* U+005A "Z" */
    0x1c, 0xcc, 0xcc, 0xde, 0x0, 0x0, 0x0, 0xb,
    0x40, 0x0, 0x0, 0x6, 0xa0, 0x0, 0x0, 0x1,
    0xd1, 0x0, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0,
    0x4b, 0x0, 0x0, 0x0, 0xd, 0x10, 0x0, 0x0,
    0x9, 0x60, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0xd2, 0x0, 0x0, 0x0, 0x7f, 0xdd, 0xdd,
    0xdd, 0x20,

    /* U+005B "[" */
    0x9c, 0xc5, 0xa3, 0x0, 0xa3, 0x0, 0xa3, 0x0,
    0xa3, 0x0, 0xa3, 0x0, 0xa3, 0x0, 0xa3, 0x0,
    0xa3, 0x0, 0xa3, 0x0, 0xa3, 0x0, 0xa3, 0x0,
    0xa3, 0x0, 0x9c, 0xc5,

    /* U+005C "\\" */
    0x85, 0x0, 0x0, 0x2b, 0x0, 0x0, 0xc, 0x10,
    0x0, 0x6, 0x70, 0x0, 0x1, 0xc0, 0x0, 0x0,
    0xa3, 0x0, 0x0, 0x49, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x8, 0x50, 0x0, 0x3, 0xb0, 0x0, 0x0,
    0xc1,

    /* U+005D "]" */
    0x8c, 0xd4, 0x0, 0x85, 0x0, 0x85, 0x0, 0x85,
    0x0, 0x85, 0x0, 0x85, 0x0, 0x85, 0x0, 0x85,
    0x0, 0x85, 0x0, 0x85, 0x0, 0x85, 0x0, 0x85,
    0x0, 0x85, 0x8c, 0xd4,

    /* U+005E "^" */
    0x0, 0x16, 0x0, 0x0, 0x7, 0xe2, 0x0, 0x0,
    0xc4, 0x90, 0x0, 0x58, 0xc, 0x10, 0xc, 0x10,
    0x58, 0x3, 0x90, 0x0, 0xd0,

    /* U+005F "_" */
    0xbb, 0xbb, 0xbb,

    /* U+0060 "`" */
    0xb, 0x20, 0x1, 0xa0,

    /* U+0061 "a" */
    0x0, 0x9c, 0xca, 0x0, 0x76, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xd0, 0x8, 0xbb, 0xbe, 0xb, 0x50,
    0x0, 0xd0, 0xd0, 0x0, 0xe, 0xd, 0x20, 0x8,
    0xe0, 0x2b, 0xcc, 0x5c,

    /* U+0062 "b" */
    0xb2, 0x0, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0,
    0xb2, 0x0, 0x0, 0x0, 0xb3, 0xac, 0xd7, 0x0,
    0xbb, 0x20, 0x9, 0x70, 0xb6, 0x0, 0x0, 0xd0,
    0xb2, 0x0, 0x0, 0xb2, 0xb2, 0x0, 0x0, 0xb2,
    0xb6, 0x0, 0x0, 0xd0, 0xba, 0x20, 0x9, 0x70,
    0xb1, 0xac, 0xd7, 0x0,

    /* U+0063 "c" */
    0x0, 0x6c, 0xdc, 0x30, 0x6, 0xa0, 0x2, 0xa0,
    0xd, 0x0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x0,
    0x1c, 0x0, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0,
    0x6, 0xa0, 0x2, 0xa0, 0x0, 0x6c, 0xdc, 0x30,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x1b, 0x0, 0x0, 0x0, 0x1b,
    0x0, 0x0, 0x0, 0x1b, 0x0, 0x7d, 0xca, 0x2b,
    0x7, 0x90, 0x2, 0xbb, 0xd, 0x0, 0x0, 0x6b,
    0x1b, 0x0, 0x0, 0x2b, 0x1c, 0x0, 0x0, 0x2b,
    0xd, 0x0, 0x0, 0x6b, 0x7, 0xa0, 0x2, 0xab,
    0x0, 0x7d, 0xca, 0x1b,

    /* U+0065 "e" */
    0x0, 0x6c, 0xcb, 0x30, 0x7, 0x90, 0x2, 0xd1,
    0xd, 0x0, 0x0, 0x86, 0x2e, 0xbb, 0xbb, 0xc6,
    0x1c, 0x0, 0x0, 0x0, 0xd, 0x10, 0x0, 0x0,
    0x6, 0xb0, 0x1, 0xb0, 0x0, 0x6d, 0xcc, 0x40,

    /* U+0066 "f" */
    0x0, 0x4c, 0xc1, 0x0, 0xd1, 0x0, 0x2, 0xa0,
    0x0, 0x3, 0x90, 0x0, 0x6c, 0xeb, 0x80, 0x4,
    0x90, 0x0, 0x4, 0x90, 0x0, 0x4, 0x90, 0x0,
    0x4, 0x90, 0x0, 0x4, 0x90, 0x0, 0x4, 0x90,
    0x0, 0x4, 0x90, 0x0,

    /* U+0067 "g" */
    0x0, 0x7d, 0xca, 0x1b, 0x7, 0xa0, 0x2, 0xab,
    0xd, 0x0, 0x0, 0x6b, 0x1b, 0x0, 0x0, 0x2b,
    0x1c, 0x0, 0x0, 0x2b, 0xd, 0x0, 0x0, 0x5b,
    0x7, 0xa0, 0x2, 0xbb, 0x0, 0x7d, 0xca, 0x3b,
    0x0, 0x0, 0x0, 0x49, 0x8, 0x40, 0x1, 0xc3,
    0x1, 0xac, 0xcb, 0x30,

    /* U+0068 "h" */
    0xb2, 0x0, 0x0, 0xb, 0x20, 0x0, 0x0, 0xb2,
    0x0, 0x0, 0xb, 0x3b, 0xcd, 0x40, 0xbb, 0x10,
    0x2e, 0x1b, 0x40, 0x0, 0x94, 0xb2, 0x0, 0x7,
    0x6b, 0x20, 0x0, 0x76, 0xb2, 0x0, 0x7, 0x6b,
    0x20, 0x0, 0x76, 0xb2, 0x0, 0x7, 0x60,

    /* U+0069 "i" */
    0xb3, 0x20, 0x0, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2,
    0xb2, 0xb2, 0xb2,

    /* U+006A "j" */
    0x0, 0xb, 0x30, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xb2, 0x0, 0xb, 0x20, 0x0, 0xb2, 0x0,
    0xb, 0x20, 0x0, 0xb2, 0x0, 0xb, 0x20, 0x0,
    0xb2, 0x0, 0xb, 0x20, 0x0, 0xb1, 0x0, 0x1d,
    0x4, 0xdc, 0x40,

    /* U+006B "k" */
    0xb2, 0x0, 0x0, 0xb, 0x20, 0x0, 0x0, 0xb2,
    0x0, 0x0, 0xb, 0x20, 0x5, 0xa0, 0xb2, 0x4,
    0xb0, 0xb, 0x22, 0xc1, 0x0, 0xb3, 0xe5, 0x0,
    0xb, 0xd5, 0xd0, 0x0, 0xb4, 0x6, 0xa0, 0xb,
    0x20, 0xb, 0x60, 0xb2, 0x0, 0x1d, 0x20,

    /* U+006C "l" */
    0xb2, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2, 0xb2,
    0xb2, 0xb2, 0xb2,

    /* U+006D "m" */
    0xb3, 0xbd, 0xa0, 0x5c, 0xd6, 0xb, 0xa0, 0x8,
    0xa7, 0x0, 0xd2, 0xb4, 0x0, 0x2e, 0x0, 0x7,
    0x6b, 0x20, 0x0, 0xd0, 0x0, 0x57, 0xb2, 0x0,
    0xd, 0x0, 0x5, 0x7b, 0x20, 0x0, 0xd0, 0x0,
    0x57, 0xb2, 0x0, 0xd, 0x0, 0x5, 0x7b, 0x20,
    0x0, 0xd0, 0x0, 0x57,

    /* U+006E "n" */
    0xb2, 0xbc, 0xd4, 0xb, 0xa1, 0x2, 0xe1, 0xb4,
    0x0, 0x9, 0x4b, 0x20, 0x0, 0x76, 0xb2, 0x0,
    0x7, 0x6b, 0x20, 0x0, 0x76, 0xb2, 0x0, 0x7,
    0x6b, 0x20, 0x0, 0x76,

    /* U+006F "o" */
    0x0, 0x5c, 0xcc, 0x30, 0x5, 0xb0, 0x1, 0xc3,
    0xd, 0x0, 0x0, 0x3b, 0x1c, 0x0, 0x0, 0xe,
    0x1c, 0x0, 0x0, 0xe, 0xd, 0x0, 0x0, 0x3b,
    0x6, 0xb0, 0x1, 0xc3, 0x0, 0x5c, 0xcc, 0x30,

    /* U+0070 "p" */
    0xb1, 0xac, 0xd7, 0x0, 0xba, 0x20, 0x9, 0x70,
    0xb6, 0x0, 0x0, 0xd0, 0xb2, 0x0, 0x0, 0xb2,
    0xb2, 0x0, 0x0, 0xb2, 0xb6, 0x0, 0x0, 0xd0,
    0xbb, 0x20, 0x9, 0x70, 0xb3, 0xac, 0xd7, 0x0,
    0xb2, 0x0, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0,
    0xb2, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x7d, 0xca, 0x1b, 0x7, 0x90, 0x2, 0xab,
    0xd, 0x0, 0x0, 0x6b, 0x1b, 0x0, 0x0, 0x2b,
    0x2b, 0x0, 0x0, 0x2b, 0xd, 0x0, 0x0, 0x6b,
    0x7, 0xa0, 0x2, 0xbb, 0x0, 0x7d, 0xca, 0x2b,
    0x0, 0x0, 0x0, 0x1b, 0x0, 0x0, 0x0, 0x1b,
    0x0, 0x0, 0x0, 0x1b,

    /* U+0072 "r" */
    0xb3, 0xcd, 0x1b, 0xa1, 0x0, 0xb4, 0x0, 0xb,
    0x20, 0x0, 0xb2, 0x0, 0xb, 0x20, 0x0, 0xb2,
    0x0, 0xb, 0x20, 0x0,

    /* U+0073 "s" */
    0x4, 0xcc, 0xc3, 0x0, 0xd1, 0x1, 0xa0, 0x1e,
    0x0, 0x0, 0x0, 0x6d, 0x83, 0x0, 0x0, 0x4,
    0xb8, 0x0, 0x0, 0x0, 0xd1, 0x3b, 0x0, 0x1d,
    0x0, 0x6c, 0xcc, 0x40,

    /* U+0074 "t" */
    0x7, 0x60, 0x0, 0x76, 0x0, 0x8d, 0xdb, 0x70,
    0x76, 0x0, 0x7, 0x60, 0x0, 0x76, 0x0, 0x7,
    0x60, 0x0, 0x66, 0x0, 0x5, 0xa0, 0x0, 0xb,
    0xda,

    /* U+0075 "u" */
    0xd0, 0x0, 0x9, 0x4d, 0x0, 0x0, 0x94, 0xd0,
    0x0, 0x9, 0x4d, 0x0, 0x0, 0x94, 0xd0, 0x0,
    0x9, 0x4c, 0x10, 0x0, 0xb4, 0x79, 0x0, 0x5d,
    0x40, 0x9d, 0xc7, 0x74,

    /* U+0076 "v" */
    0xa5, 0x0, 0x0, 0xc1, 0x3b, 0x0, 0x3, 0xa0,
    0xc, 0x20, 0x9, 0x40, 0x6, 0x80, 0xc, 0x0,
    0x0, 0xd0, 0x47, 0x0, 0x0, 0x95, 0xa1, 0x0,
    0x0, 0x2c, 0xa0, 0x0, 0x0, 0xc, 0x50, 0x0,

    /* U+0077 "w" */
    0xa3, 0x0, 0xc, 0x30, 0x0, 0xb1, 0x49, 0x0,
    0x1e, 0x90, 0x1, 0xb0, 0xd, 0x0, 0x66, 0xd0,
    0x5, 0x60, 0x9, 0x30, 0xa1, 0x94, 0xa, 0x10,
    0x4, 0x90, 0xa0, 0x39, 0xb, 0x0, 0x0, 0xc5,
    0x60, 0xc, 0x56, 0x0, 0x0, 0x8d, 0x10, 0x8,
    0xd1, 0x0, 0x0, 0x3b, 0x0, 0x2, 0xb0, 0x0,

    /* U+0078 "x" */
    0x6a, 0x0, 0x7, 0x80, 0xb6, 0x2, 0xc0, 0x1,
    0xd2, 0xc2, 0x0, 0x4, 0xf6, 0x0, 0x0, 0x4e,
    0x70, 0x0, 0x1c, 0x1d, 0x20, 0xb, 0x40, 0x3c,
    0x6, 0x90, 0x0, 0x79,

    /* U+0079 "y" */
    0xa4, 0x0, 0x0, 0xc1, 0x3b, 0x0, 0x3, 0xa0,
    0xd, 0x20, 0xa, 0x30, 0x6, 0x80, 0x1c, 0x0,
    0x0, 0xd0, 0x66, 0x0, 0x0, 0x85, 0xc0, 0x0,
    0x0, 0x2e, 0x90, 0x0, 0x0, 0xc, 0x20, 0x0,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0,
    0xad, 0x80, 0x0, 0x0,

    /* U+007A "z" */
    0x1b, 0xbb, 0xbf, 0x20, 0x0, 0x7, 0x80, 0x0,
    0x2, 0xd0, 0x0, 0x0, 0xc3, 0x0, 0x0, 0x78,
    0x0, 0x0, 0x2d, 0x0, 0x0, 0xc, 0x30, 0x0,
    0x5, 0xfb, 0xbb, 0xb4,

    /* U+007B "{" */
    0x0, 0x2b, 0x90, 0xa, 0x40, 0x0, 0xc1, 0x0,
    0xd, 0x10, 0x0, 0xd0, 0x0, 0x2d, 0x0, 0x6f,
    0x40, 0x0, 0x3d, 0x0, 0x0, 0xd0, 0x0, 0xd,
    0x10, 0x0, 0xd1, 0x0, 0xc, 0x10, 0x0, 0xa5,
    0x0, 0x2, 0xb9,

    /* U+007C "|" */
    0x39, 0x39, 0x39, 0x39, 0x39, 0x39, 0x39, 0x39,
    0x39, 0x39, 0x39, 0x39, 0x39, 0x39,

    /* U+007D "}" */
    0x8b, 0x30, 0x0, 0x3b, 0x0, 0x0, 0xd0, 0x0,
    0xd, 0x0, 0x0, 0xd0, 0x0, 0xc, 0x30, 0x0,
    0x3f, 0x80, 0xb, 0x30, 0x0, 0xd0, 0x0, 0xd,
    0x0, 0x0, 0xd0, 0x0, 0xd, 0x0, 0x4, 0xb0,
    0x8, 0xb2, 0x0,

    /* U+007E "~" */
    0x2c, 0xa0, 0x8, 0x2a, 0x24, 0x80, 0xc0, 0xa0,
    0x6, 0xc5, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xc7, 0x2c, 0xf1,
    0x0, 0x1, 0xff, 0xea, 0x51, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x4a, 0xbe, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x5, 0xff, 0xff, 0xf1,
    0x8, 0xdd, 0xfc, 0x0, 0x6, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xfc, 0x0, 0x0, 0x8e, 0xfc, 0x30,
    0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0xdd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x20, 0x4, 0x44, 0x44, 0x44, 0x44, 0x0, 0x2e,
    0x57, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5e, 0xfa,
    0xbf, 0x52, 0x22, 0x22, 0x5f, 0xba, 0xff, 0x3,
    0xf3, 0x0, 0x0, 0x3, 0xf3, 0xf, 0xf8, 0x9f,
    0x30, 0x0, 0x0, 0x3f, 0xa8, 0xff, 0x79, 0xf8,
    0x55, 0x55, 0x58, 0xf9, 0x7f, 0xf0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xff, 0xbc, 0xf4, 0x11,
    0x11, 0x14, 0xfc, 0xbf, 0xf4, 0x6f, 0x30, 0x0,
    0x0, 0x3f, 0x64, 0xff, 0x3, 0xf3, 0x0, 0x0,
    0x3, 0xf3, 0xf, 0xfe, 0xef, 0x96, 0x66, 0x66,
    0x9f, 0xee, 0xfd, 0x14, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x1d,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xba,
    0x25, 0xbb, 0xbb, 0xbb, 0xbb, 0x84, 0x66, 0x50,
    0x26, 0x66, 0x66, 0x66, 0x64, 0xff, 0xff, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x44, 0x40, 0x14, 0x44,
    0x44, 0x44, 0x42, 0xac, 0xcc, 0x26, 0xcc, 0xcc,
    0xcc, 0xcc, 0xaf, 0xff, 0xf5, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xe3, 0x8f, 0xff, 0xff, 0xff,
    0xfc,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x4, 0x20,
    0x0, 0x0, 0xc, 0xff, 0xf6, 0x7, 0xfe, 0x20,
    0x0, 0xc, 0xff, 0xf6, 0x0, 0xef, 0xfe, 0x20,
    0xc, 0xff, 0xf6, 0x0, 0x3, 0xff, 0xfe, 0x3c,
    0xff, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd6, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x3c, 0x50, 0x0, 0x2, 0xc7, 0xe, 0xff, 0x50,
    0x2, 0xef, 0xf3, 0x9f, 0xff, 0x52, 0xef, 0xfd,
    0x0, 0x9f, 0xff, 0xef, 0xfd, 0x10, 0x0, 0x9f,
    0xff, 0xfd, 0x10, 0x0, 0x2, 0xff, 0xff, 0x60,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x50, 0x2, 0xef,
    0xfd, 0xaf, 0xff, 0x50, 0xdf, 0xfd, 0x10, 0x9f,
    0xff, 0x2b, 0xfd, 0x10, 0x0, 0x9f, 0xe1, 0x6,
    0x10, 0x0, 0x0, 0x52, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0x30, 0xef, 0x70, 0x97, 0x0, 0x0, 0x1e,
    0xfc, 0xe, 0xf7, 0x3f, 0xf8, 0x0, 0xb, 0xff,
    0x50, 0xef, 0x70, 0xbf, 0xf4, 0x3, 0xff, 0x60,
    0xe, 0xf7, 0x0, 0xdf, 0xb0, 0x8f, 0xe0, 0x0,
    0xef, 0x70, 0x5, 0xff, 0x1b, 0xfa, 0x0, 0xe,
    0xf7, 0x0, 0x1f, 0xf3, 0xbf, 0x90, 0x0, 0xdf,
    0x60, 0x0, 0xff, 0x49, 0xfc, 0x0, 0x2, 0x40,
    0x0, 0x3f, 0xf3, 0x5f, 0xf2, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x5, 0xff, 0xc3, 0x0, 0x7, 0xff,
    0xe1, 0x0, 0x8, 0xff, 0xff, 0xef, 0xff, 0xe2,
    0x0, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x75, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xdc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x8,
    0x25, 0xdf, 0xff, 0xd5, 0x28, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x4f, 0xff, 0xfe,
    0x41, 0x4e, 0xff, 0xff, 0x40, 0x2f, 0xff, 0x60,
    0x0, 0x6f, 0xff, 0x20, 0x1, 0xff, 0xf3, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x8f, 0xff, 0x50, 0x5f, 0xff, 0xff, 0x85, 0x8f,
    0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x6, 0xfd, 0xff, 0xff, 0xff, 0xfd,
    0xf6, 0x0, 0x4, 0x2, 0xbf, 0xff, 0xb2, 0x4,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x98, 0x20, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xd2, 0xff,
    0x40, 0x0, 0x0, 0x5, 0xff, 0x91, 0xbf, 0xef,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0x63, 0xb2, 0x8f,
    0xff, 0x40, 0x0, 0xb, 0xfe, 0x45, 0xff, 0xf4,
    0x5f, 0xf9, 0x0, 0x2d, 0xfd, 0x28, 0xff, 0xff,
    0xf6, 0x3e, 0xfc, 0x1d, 0xfb, 0x1b, 0xff, 0xff,
    0xff, 0xf9, 0x2c, 0xfb, 0x48, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x9, 0x30, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff,
    0xf4, 0x36, 0xff, 0xff, 0x10, 0x0, 0x3, 0xff,
    0xff, 0x0, 0x2f, 0xff, 0xf1, 0x0, 0x0, 0x3f,
    0xff, 0xf0, 0x2, 0xff, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xfe, 0x0, 0x1e, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x26, 0x6a,
    0xff, 0xfa, 0x66, 0x20, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xbb, 0x46, 0xf6, 0x4b,
    0xbb, 0xb9, 0xff, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xfd, 0xdd, 0xdd, 0xdd, 0xff,
    0x30, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf3, 0xdf, 0xca, 0xa9, 0x0, 0x0,
    0x19, 0xaa, 0xdf, 0xbf, 0xff, 0xff, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xec,
    0xcc, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x0, 0x4, 0x9c, 0xdc, 0x82, 0x0, 0xff, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xf0, 0x1d,
    0xff, 0xa5, 0x35, 0xaf, 0xfd, 0xff, 0xb, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xf3, 0xff, 0x30,
    0x0, 0x7, 0xdc, 0xdf, 0xff, 0x8f, 0xa0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf1, 0x31, 0x0, 0x0,
    0x1, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xee, 0xee, 0xe7, 0x0,
    0x0, 0x7, 0xe8, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xdf, 0x5f, 0xff, 0x91, 0x21, 0x0, 0x0,
    0x7f, 0xf0, 0xff, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xf7, 0xf, 0xfa, 0xff, 0xd7, 0x46, 0xcf, 0xfa,
    0x0, 0xff, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xa0, 0x1, 0x7b, 0xdc, 0x82, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x6f, 0xf8, 0x79, 0x9a, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x1, 0xcf, 0xf8,
    0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x1, 0xb5,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xd, 0x40, 0xff, 0xff,
    0xff, 0xf8, 0xa, 0xf1, 0xff, 0xff, 0xff, 0xf8,
    0x3, 0xf3, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xe0,
    0xef, 0xff, 0xff, 0xf8, 0xa, 0x20, 0x0, 0x1,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb4, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x2, 0xa,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0x80, 0x6, 0xf6,
    0xc, 0xd0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x9,
    0xf3, 0x3f, 0x4f, 0xff, 0xff, 0xff, 0x80, 0xd5,
    0xd, 0xb0, 0xda, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0xf1, 0x7f, 0xa, 0xcf, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0x36, 0xf0, 0x8d, 0xff, 0xff, 0xff, 0xf8,
    0xc, 0xe0, 0x8e, 0xa, 0xbe, 0xff, 0xff, 0xff,
    0x80, 0x92, 0x1e, 0x90, 0xe8, 0x0, 0x1, 0xcf,
    0xf8, 0x0, 0x1c, 0xe1, 0x5f, 0x30, 0x0, 0x1,
    0xcf, 0x80, 0x6, 0xe3, 0x1e, 0xb0, 0x0, 0x0,
    0x1, 0xb5, 0x0, 0x0, 0x1c, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xda, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x2f, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xf9, 0x6d,
    0xff, 0xfc, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0xbf,
    0xfc, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0x20, 0xac,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xf5, 0x0, 0x0, 0x9, 0xff, 0xff, 0xd0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xff, 0xff,
    0xfc, 0xd, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0x4e, 0xf1, 0xff, 0xff,
    0xff, 0xf3, 0xaf, 0x75, 0xef, 0xff, 0xff, 0x2,
    0xff, 0x82, 0x8f, 0xff, 0x70, 0x5, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x2, 0x9d, 0xda, 0x40, 0x0,

    /* U+F048 "" */
    0x2, 0x20, 0x0, 0x0, 0x1, 0x1, 0xff, 0x0,
    0x0, 0xb, 0xf2, 0x2f, 0xf0, 0x0, 0x1c, 0xff,
    0x42, 0xff, 0x0, 0x1d, 0xff, 0xf4, 0x2f, 0xf0,
    0x2e, 0xff, 0xff, 0x42, 0xff, 0x3e, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0xff, 0xff,
    0xff, 0x42, 0xff, 0x1c, 0xff, 0xff, 0xf4, 0x2f,
    0xf0, 0xb, 0xff, 0xff, 0x42, 0xff, 0x0, 0xa,
    0xff, 0xf4, 0x2f, 0xf0, 0x0, 0x9, 0xff, 0x41,
    0xfe, 0x0, 0x0, 0x8, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x2, 0x22, 0x10, 0x0, 0x12, 0x22, 0x0, 0xbf,
    0xff, 0xf5, 0x4, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xf9, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xf9, 0x8,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xe3, 0x2, 0xdf,
    0xff, 0x90,

    /* U+F04D "" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0,

    /* U+F051 "" */
    0x1, 0x0, 0x0, 0x0, 0x22, 0x1, 0xfc, 0x10,
    0x0, 0xe, 0xf2, 0x3f, 0xfd, 0x10, 0x0, 0xef,
    0x33, 0xff, 0xfe, 0x20, 0xe, 0xf3, 0x3f, 0xff,
    0xfe, 0x30, 0xef, 0x33, 0xff, 0xff, 0xff, 0x4e,
    0xf3, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x33, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x3f, 0xff, 0xff, 0xfd,
    0xff, 0x33, 0xff, 0xff, 0xfd, 0x1e, 0xf3, 0x3f,
    0xff, 0xfc, 0x10, 0xef, 0x33, 0xff, 0xfb, 0x0,
    0xe, 0xf3, 0x3f, 0xfa, 0x0, 0x0, 0xef, 0x31,
    0xd9, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0x9, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xca, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xd1, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x9,
    0xff, 0xa0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x5, 0xff,
    0xd1, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0, 0x0,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x5, 0x90,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd,
    0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x9c, 0xcc, 0xcf, 0xff, 0xcc, 0xcc, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xeb, 0x0,
    0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9c, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xa0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x2, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xa4, 0x24, 0xbf, 0xfb,
    0x10, 0x0, 0x2d, 0xff, 0x60, 0x16, 0x30, 0x8f,
    0xfd, 0x10, 0xd, 0xff, 0xa0, 0x1, 0xff, 0x70,
    0xcf, 0xfc, 0x9, 0xff, 0xf5, 0x12, 0x8f, 0xff,
    0x17, 0xff, 0xf8, 0xef, 0xff, 0x35, 0xff, 0xff,
    0xf2, 0x5f, 0xff, 0xc7, 0xff, 0xf6, 0x1f, 0xff,
    0xfe, 0x8, 0xff, 0xf5, 0xb, 0xff, 0xc0, 0x5e,
    0xfd, 0x30, 0xef, 0xfa, 0x0, 0xc, 0xff, 0x90,
    0x1, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x8, 0xff,
    0xd8, 0x68, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0x7c, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x23,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x5b,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xfa, 0x42, 0x5b, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x41, 0x62, 0x9, 0xff, 0xd1,
    0x0, 0x1, 0x50, 0x9, 0xff, 0x9f, 0xf6, 0xd,
    0xff, 0xc0, 0x0, 0xbf, 0x90, 0x5, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x70, 0xf, 0xff, 0xc1, 0x2,
    0xdf, 0xff, 0x26, 0xff, 0xfb, 0x0, 0x7f, 0xff,
    0x50, 0x0, 0xaf, 0xf6, 0x9f, 0xff, 0x40, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x80,
    0x0, 0x1, 0xbf, 0xf9, 0x0, 0x0, 0x3e, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x87, 0x30,
    0x1b, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x28, 0xcf,
    0xfe, 0x30, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb4,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf5, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xb8, 0xcf, 0xff, 0xfc, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf8, 0x1a, 0xff, 0xff,
    0xf6, 0x0, 0x1, 0xff, 0xff, 0xff, 0x20, 0x4f,
    0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff, 0xff, 0xf8,
    0x1a, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x7d,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x50,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x10, 0x45,
    0x54, 0x0, 0x0, 0x3, 0x5d, 0xfd, 0x1f, 0xff,
    0xf8, 0x0, 0x6, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf7, 0x5, 0xff, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0x74, 0xff, 0xc1, 0xcf, 0xa0, 0x0, 0x0, 0x63,
    0xff, 0xd1, 0x8, 0x90, 0x0, 0x0, 0x2, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xe2,
    0x93, 0xb, 0xd1, 0x4, 0x44, 0xef, 0xf3, 0x8f,
    0xe5, 0xdf, 0xd1, 0xff, 0xff, 0xf3, 0x2, 0xef,
    0xff, 0xff, 0xcf, 0xff, 0xf4, 0x0, 0x3, 0xff,
    0xff, 0xfa, 0x1, 0x10, 0x0, 0x0, 0x0, 0x1c,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0xff,
    0xc0, 0x0, 0x0, 0xbf, 0xf9, 0x7, 0xff, 0xc0,
    0x0, 0xbf, 0xf9, 0x0, 0x7, 0xff, 0xc1, 0x9f,
    0xf9, 0x0, 0x0, 0x7, 0xff, 0xb7, 0xf9, 0x0,
    0x0, 0x0, 0x7, 0xf9, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x6, 0x0, 0x0, 0x0, 0x0, 0x6, 0x1a, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x7f, 0xfc, 0x0,
    0x0, 0xa, 0xff, 0x90, 0x7f, 0xfc, 0x0, 0xb,
    0xff, 0x90, 0x0, 0x7f, 0xfc, 0x1b, 0xff, 0x90,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x10, 0x1, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x4, 0xff, 0xfd, 0x16, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x2a, 0xcc, 0xcc, 0xcf, 0xf0, 0x0, 0xbf, 0x9f,
    0xdb, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x1,
    0x51, 0xfc, 0x5, 0x0, 0x0, 0x0, 0xd, 0xf0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x6, 0xd, 0xf0, 0x62, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x7, 0xfc, 0xdf, 0x9f, 0xa0, 0x1, 0xff,
    0xdd, 0xdd, 0xdb, 0x2d, 0xff, 0xff, 0xe3, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf5, 0x1d, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0,

    /* U+F07B "" */
    0x3, 0x44, 0x44, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfc, 0x66, 0x66, 0x65, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x39, 0x9b,
    0xff, 0xfb, 0x99, 0x30, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xb8, 0x4f, 0xff, 0x48,
    0xbb, 0xb9, 0xff, 0xff, 0xf3, 0x22, 0x23, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x0,
    0x0, 0x6, 0xda, 0x0, 0xa, 0xff, 0xf3, 0x0,
    0x7, 0xef, 0xff, 0x84, 0xdf, 0xff, 0x50, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xdc, 0xa8, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x80, 0x0, 0x0, 0x79, 0x40, 0xbf, 0xdf,
    0xf6, 0x0, 0x1d, 0xff, 0xe0, 0xff, 0x5, 0xf9,
    0x2, 0xef, 0xfe, 0x20, 0xcf, 0xce, 0xf9, 0x3e,
    0xff, 0xe2, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x28, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5, 0xae, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xfd, 0xaf, 0xff, 0x40, 0x0, 0xef, 0x48,
    0xf8, 0xa, 0xff, 0xf4, 0x0, 0xef, 0x16, 0xf8,
    0x0, 0xaf, 0xff, 0x50, 0x9f, 0xff, 0xf3, 0x0,
    0xa, 0xff, 0xe0, 0x8, 0xdc, 0x50, 0x0, 0x0,
    0x46, 0x20,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf5, 0xb4, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf5, 0xbf, 0x40, 0x35, 0x44, 0xff,
    0xff, 0xf5, 0x8c, 0xb0, 0xff, 0xd4, 0xff, 0xff,
    0xfb, 0x66, 0x60, 0xff, 0xd4, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xd2, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xf6, 0x1, 0x11, 0x11,
    0x11, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbe, 0xee, 0xee, 0xee, 0xd2, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xff, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0xfe, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xf7, 0x27, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xf1, 0xff, 0xff,
    0xc0, 0x0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xf5,
    0x4, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x53, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x5f, 0xa1, 0xaf,
    0xff, 0xff, 0xff, 0xa1, 0x9f, 0xff, 0xd3, 0x6f,
    0xff, 0xff, 0x62, 0xdf, 0xff, 0xff, 0xf7, 0x2d,
    0xfd, 0x26, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x12,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x70, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xfa, 0x77,
    0x61, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0xdf,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x35, 0x55, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x20, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x3a, 0xfb, 0x33, 0x20, 0x0, 0x0, 0xff, 0xff,
    0x3e, 0xff, 0xf1, 0x0, 0x0, 0xff, 0xff, 0xcf,
    0xff, 0xf2, 0x0, 0x0, 0xff, 0xff, 0xc7, 0x77,
    0x71, 0x0, 0x0, 0xff, 0xfe, 0x2a, 0xaa, 0xa1,
    0x61, 0x0, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbc,
    0x10, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbf, 0xc0,
    0xff, 0xfc, 0x6f, 0xff, 0xf2, 0x12, 0x20, 0xff,
    0xfc, 0x6f, 0xff, 0xfc, 0x99, 0x90, 0xff, 0xfc,
    0x6f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xfc, 0x6f, 0xff,
    0xff, 0xff, 0xf1, 0x12, 0x21, 0x6f, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xd0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2a, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xd9, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xdd, 0xfd, 0xef, 0xdf, 0xdd, 0xfd,
    0xdf, 0xdf, 0xe0, 0x1d, 0x3, 0xb0, 0xd1, 0xd,
    0x10, 0xfe, 0xff, 0xbc, 0xfb, 0xcf, 0xbf, 0xcb,
    0xfc, 0xbf, 0xef, 0xff, 0x73, 0xc7, 0x3d, 0x38,
    0xb3, 0x9f, 0xfe, 0xff, 0xf4, 0xa, 0x40, 0xb0,
    0x6a, 0x6, 0xff, 0xef, 0xff, 0xfe, 0xff, 0xef,
    0xef, 0xfe, 0xff, 0xfe, 0xfe, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0xe2, 0x1f, 0xef, 0xe0, 0x1d, 0x0,
    0x0, 0x0, 0xd, 0x10, 0xfe, 0xff, 0xee, 0xfe,
    0xee, 0xee, 0xee, 0xfe, 0xef, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x14, 0x55, 0x55,
    0x9f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xb1, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0x97, 0x80, 0x0, 0xff, 0xff, 0xff, 0x98,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0x98, 0xff, 0x80,
    0xff, 0xff, 0xff, 0x95, 0xbb, 0xb1, 0xff, 0xff,
    0xff, 0xd5, 0x55, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe2,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xfc,
    0x82, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0xaf, 0xff, 0xd9,
    0x54, 0x34, 0x6a, 0xef, 0xff, 0x70, 0xcf, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x87,
    0xf8, 0x0, 0x0, 0x46, 0x76, 0x30, 0x0, 0x1b,
    0xf4, 0x1, 0x0, 0x19, 0xff, 0xff, 0xff, 0xe7,
    0x0, 0x1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xef,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x7, 0xff, 0x82,
    0x0, 0x3, 0xaf, 0xf3, 0x0, 0x0, 0x0, 0x6,
    0x20, 0x0, 0x0, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe8, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F241 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x30, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F242 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x10, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x10,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F243 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x32, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F244 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xee,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xc0, 0x1b, 0xb1, 0x0, 0x0, 0x0, 0x18, 0x92,
    0x0, 0xd4, 0x0, 0x0, 0x0, 0x1, 0x20, 0xb,
    0xff, 0xe1, 0x7d, 0x11, 0x11, 0x11, 0x11, 0x6f,
    0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0x5e, 0x50, 0x5, 0x60, 0x0, 0x0,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xe1, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xfd, 0x70, 0x0, 0x0, 0x7f, 0xff,
    0x8f, 0xff, 0x90, 0x0, 0x2f, 0xff, 0xf3, 0x6f,
    0xff, 0x30, 0x8, 0xff, 0xff, 0x31, 0x7f, 0xf9,
    0x0, 0xcf, 0x96, 0xf3, 0xb2, 0x9f, 0xd0, 0xf,
    0xfe, 0x26, 0x39, 0x1c, 0xff, 0x0, 0xff, 0xfe,
    0x20, 0xa, 0xff, 0xf0, 0xf, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0x10, 0xff, 0xfd, 0x10, 0x9, 0xff,
    0xf0, 0xf, 0xfd, 0x17, 0x39, 0x1a, 0xff, 0x0,
    0xcf, 0x97, 0xf3, 0xa2, 0x8f, 0xd0, 0x8, 0xff,
    0xff, 0x30, 0x8f, 0xf9, 0x0, 0x1f, 0xff, 0xf4,
    0x7f, 0xff, 0x20, 0x0, 0x5f, 0xff, 0xaf, 0xff,
    0x70, 0x0, 0x0, 0x29, 0xcd, 0xda, 0x40, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xcd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc1, 0x2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x1f, 0xfd, 0xff, 0xdf, 0xfd,
    0xff, 0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff,
    0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30,
    0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f,
    0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4,
    0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4, 0xcf,
    0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf5, 0xdf, 0x3f,
    0xe3, 0xff, 0x30, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x8, 0xde, 0xee, 0xee, 0xee, 0xd9,
    0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x64, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x4f, 0xe3,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x64, 0x30,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xca, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x31, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x8, 0xff, 0xff,
    0xf8, 0x5f, 0xfc, 0x3e, 0xff, 0xfc, 0x8, 0xff,
    0xff, 0xff, 0x20, 0x5b, 0x0, 0xaf, 0xff, 0xc8,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x8f, 0xff,
    0xfc, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x3f,
    0xff, 0xff, 0xc4, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x10, 0x5f, 0xff, 0xfc, 0x4, 0xff, 0xff, 0xff,
    0x10, 0x8e, 0x20, 0x9f, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xfb, 0x9f, 0xfe, 0x7f, 0xff, 0xfc, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xbe, 0xdf, 0xdf,
    0xdf, 0xf0, 0xb, 0xf4, 0x1d, 0xf, 0xc, 0xf1,
    0xbf, 0xf4, 0x1d, 0xf, 0xc, 0xf1, 0xff, 0xf8,
    0x6e, 0x5f, 0x5d, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1,
    0x0, 0x4, 0x40, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1c, 0xff, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x40,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 65, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 54, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11, .adv_w = 73, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 19, .adv_w = 149, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 74, .adv_w = 133, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 134, .adv_w = 181, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 195, .adv_w = 165, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 261, .adv_w = 40, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 265, .adv_w = 77, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 293, .adv_w = 77, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 321, .adv_w = 99, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 339, .adv_w = 133, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 367, .adv_w = 54, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 373, .adv_w = 119, .box_w = 6, .box_h = 1, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 376, .adv_w = 53, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 378, .adv_w = 88, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 411, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 455, .adv_w = 133, .box_w = 5, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 483, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 527, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 571, .adv_w = 133, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 621, .adv_w = 133, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 660, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 704, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 748, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 792, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 836, .adv_w = 58, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 844, .adv_w = 60, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 854, .adv_w = 133, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 882, .adv_w = 133, .box_w = 8, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 898, .adv_w = 133, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 926, .adv_w = 100, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 962, .adv_w = 237, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1060, .adv_w = 156, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1115, .adv_w = 151, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1159, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1214, .adv_w = 170, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1264, .adv_w = 139, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1303, .adv_w = 133, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1342, .adv_w = 165, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1397, .adv_w = 175, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1447, .adv_w = 59, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1458, .adv_w = 104, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1491, .adv_w = 156, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1541, .adv_w = 132, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1580, .adv_w = 204, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1641, .adv_w = 174, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1691, .adv_w = 181, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1752, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1796, .adv_w = 181, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1874, .adv_w = 147, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1924, .adv_w = 132, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1968, .adv_w = 132, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2012, .adv_w = 170, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2062, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2117, .adv_w = 229, .box_w = 15, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2200, .adv_w = 153, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2255, .adv_w = 143, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2305, .adv_w = 137, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2355, .adv_w = 75, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2383, .adv_w = 88, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2416, .adv_w = 75, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2444, .adv_w = 107, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 2465, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2468, .adv_w = 63, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 2472, .adv_w = 130, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2500, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2544, .adv_w = 117, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2576, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2620, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2652, .adv_w = 78, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2688, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2732, .adv_w = 137, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2771, .adv_w = 55, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2782, .adv_w = 55, .box_w = 5, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2817, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2856, .adv_w = 55, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2867, .adv_w = 202, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2911, .adv_w = 137, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2939, .adv_w = 141, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2971, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3015, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3059, .adv_w = 85, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3079, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3107, .adv_w = 83, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3132, .adv_w = 137, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3160, .adv_w = 119, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3192, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3240, .adv_w = 114, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3268, .adv_w = 119, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3312, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3340, .adv_w = 81, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3375, .adv_w = 37, .box_w = 2, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3389, .adv_w = 81, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3424, .adv_w = 133, .box_w = 7, .box_h = 3, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 3435, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3563, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3653, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3758, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3848, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3909, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4029, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4149, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4268, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4388, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4490, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4610, .adv_w = 120, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4658, .adv_w = 180, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4730, .adv_w = 270, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4858, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4948, .adv_w = 165, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5036, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5119, .adv_w = 210, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5238, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5336, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5434, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5517, .adv_w = 210, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 5622, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5685, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5748, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5846, .adv_w = 210, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 5874, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5976, .adv_w = 300, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6128, .adv_w = 270, .box_w = 19, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6280, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6393, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6452, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 6511, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6635, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6725, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6845, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6973, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7071, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7183, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7281, .adv_w = 210, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7372, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7462, .adv_w = 150, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7550, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7662, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7774, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7876, .adv_w = 240, .box_w = 17, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8012, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8108, .adv_w = 300, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8241, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8346, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8451, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8556, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8661, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8766, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8890, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8994, .adv_w = 210, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9099, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9227, .adv_w = 300, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9341, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9437, .adv_w = 241, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 28, 30, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 5, 0, 5, 5, 2,
    0, 3, 0, 0, 12, 0, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 6, 5, -10, -31, -20, 7, -7,
    0, -31, -1, 5, 0, 0, 0, 0,
    0, 0, -17, 0, -16, -5, 0, -11,
    -13, -1, -11, -8, -10, -7, 0, 0,
    0, -6, -24, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    -10, -6, 0, 0, 0, -9, 0, -7,
    0, -7, -4, -7, -11, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, -26, 0, -13, 5, 0,
    -16, -6, 0, 0, 0, -20, -2, -20,
    -14, 0, -23, 5, 0, 0, -1, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, -2, 0, 0, 0, -2, 0, 0,
    0, 3, 0, -8, 0, -8, -2, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 5,
    0, -5, 5, 0, 7, -4, 0, 0,
    0, 1, 0, 1, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    2, 0, 1, -3, 0, 2, 0, 0,
    0, -2, 0, 0, -3, 0, -4, 0,
    -3, -3, 0, 0, -2, -2, -2, -4,
    -2, 0, -2, 6, 0, 1, -31, -15,
    7, -2, 0, -39, 0, 4, 0, 0,
    0, 0, 0, 0, -13, 0, -10, -5,
    0, -8, 0, -5, 0, -6, -10, -9,
    0, 0, 0, 0, 4, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, -2, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, 0, -24, 5, 0,
    2, -12, -2, 0, -2, 0, -4, 0,
    0, 0, 0, 0, -5, 0, -7, -8,
    0, -2, -3, -6, -7, -11, -6, 0,
    -11, -23, 0, -22, 6, 0, -18, -12,
    0, 4, -3, -32, -12, -36, -25, 0,
    -42, 0, -4, 0, -7, -6, 0, -1,
    -1, -7, -7, -21, 0, 0, -2, 5,
    0, 2, -33, -16, 3, 0, 0, -37,
    0, 0, 0, 1, 1, -3, 0, -7,
    -9, 0, -8, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, -1, 0, -1,
    10, 0, -1, -2, 0, 0, 2, -2,
    -2, -5, -2, 0, -10, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 5, 0, 0, -3,
    0, 0, -2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 6, 0, -21, -24, -20, 8, -7,
    0, -33, 0, 4, 0, 5, 5, 0,
    0, 0, -28, 0, -23, -11, 0, -21,
    -26, -11, -20, -22, -23, -21, -2, 4,
    0, -5, -17, -14, 0, -4, 0, -16,
    0, 5, 0, 0, 0, 0, 0, 0,
    -17, 0, -14, -3, 0, -10, -10, 0,
    -5, -3, -5, -7, 0, 0, 5, -20,
    3, 0, 6, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -6, 0, 0, -3, -3, -5, -6, -10,
    0, 0, -7, 5, 5, -16, -28, -23,
    3, -11, 0, -32, -2, 0, 0, 0,
    0, 0, 0, 0, -26, 0, -22, -11,
    0, -18, -21, -6, -16, -14, -15, -17,
    0, 0, 2, -11, 4, 0, 2, -8,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -4, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, -9,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, -19, 0, -16, -14, -2, -21, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, -11, 0, -2,
    -8, 0, -8, 0, 0, 0, 0, -23,
    0, -16, -14, -6, -22, 0, -3, 0,
    0, -2, 0, 0, 0, -1, 0, -3,
    -5, -5, 0, 1, 0, 5, 6, 0,
    -2, 0, 0, 0, 0, -16, 0, -10,
    -5, 5, -15, 0, 0, 0, -1, 2,
    0, 0, 0, 4, 0, 0, 3, 3,
    0, 0, 3, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 4, 1, 0, -5, 0, 0, 0,
    0, -15, 0, -12, -9, -2, -17, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 2, 0, 0, 0, 11, 0, -1,
    -17, 0, 10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -5, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, -21, 0, -11,
    -10, 0, -18, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 5, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    0, -4, 6, 0, -9, 0, 0, 0,
    0, -19, 0, -11, -7, 0, -15, 0,
    -6, 0, -5, 0, 0, 0, -3, 0,
    -1, 0, 0, 0, 0, 6, 0, 2,
    -22, -8, -6, 0, 0, -24, 0, 0,
    0, -7, 0, -8, -13, 0, -10, 0,
    -7, 0, 0, 0, -2, 5, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, -22, 0, -15,
    -12, 0, -22, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, -1, 0, 3,
    0, -1, 1, -3, 6, 0, -6, 0,
    0, 0, 0, -19, 0, -9, 0, 0,
    -14, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -17, -7, -5, 0, 0, -17,
    0, -22, 0, -8, -3, -10, -14, 0,
    -3, 0, -4, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    2, 0, -8, 0, 0, 0, 0, -23,
    0, -10, -5, 0, -15, 0, -4, 0,
    -5, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -8, 0, 0, 0, 0, -24, 0, -7,
    -5, 0, -18, 0, -5, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 30,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Light_15 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Light_15 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_15*/

