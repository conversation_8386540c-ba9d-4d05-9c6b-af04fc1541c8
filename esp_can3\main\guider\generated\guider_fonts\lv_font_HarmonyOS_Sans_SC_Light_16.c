/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_LIGHT_16
#define LV_FONT_HARMONYOS_SANS_SC_LIGHT_16 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_16

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xa4, 0xa4, 0xa3, 0x93, 0x93, 0x93, 0x93, 0x82,
    0x82, 0x41, 0x0, 0x31, 0xa5,

    /* U+0022 "\"" */
    0xa, 0xb, 0xb, 0xc, 0xb, 0xc, 0xb, 0xb,

    /* U+0023 "#" */
    0x0, 0x1, 0xc0, 0x4, 0x80, 0x0, 0x5, 0x80,
    0x8, 0x40, 0x0, 0x9, 0x40, 0xc, 0x10, 0x9,
    0xef, 0xee, 0xef, 0xe9, 0x0, 0xc, 0x0, 0x49,
    0x0, 0x0, 0x49, 0x0, 0x76, 0x0, 0x0, 0x76,
    0x0, 0xa3, 0x0, 0x0, 0xa3, 0x0, 0xd0, 0x0,
    0xae, 0xfe, 0xee, 0xfe, 0x80, 0x1, 0xb0, 0x4,
    0x80, 0x0, 0x5, 0x70, 0x8, 0x40, 0x0, 0x9,
    0x30, 0xc, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0x4b, 0xfb, 0x40, 0x0, 0x6c, 0x4e,
    0x4d, 0x60, 0xe, 0x10, 0xe0, 0x1d, 0x0, 0xe0,
    0xe, 0x0, 0x10, 0xd, 0x30, 0xe0, 0x0, 0x0,
    0x3e, 0x7e, 0x0, 0x0, 0x0, 0x2a, 0xf9, 0x20,
    0x0, 0x0, 0xe, 0x7e, 0x50, 0x0, 0x0, 0xe0,
    0x3e, 0x2, 0x30, 0xe, 0x0, 0xc2, 0x3c, 0x0,
    0xe0, 0xd, 0x10, 0xaa, 0x1e, 0x8, 0xb0, 0x0,
    0x7d, 0xfe, 0x91, 0x0, 0x0, 0xe, 0x0, 0x0,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x7, 0x0,
    0x0,

    /* U+0025 "%" */
    0x8, 0xed, 0x40, 0x0, 0x3b, 0x0, 0x6a, 0x1,
    0xd1, 0x0, 0xc2, 0x0, 0x94, 0x0, 0x94, 0x6,
    0x90, 0x0, 0x5a, 0x1, 0xd1, 0x1d, 0x10, 0x0,
    0x7, 0xdc, 0x30, 0x96, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0x3, 0xcd, 0x70,
    0x0, 0x0, 0xd1, 0x1d, 0x20, 0xa6, 0x0, 0x7,
    0x70, 0x3a, 0x0, 0x49, 0x0, 0x1d, 0x0, 0x1d,
    0x10, 0x96, 0x0, 0xa5, 0x0, 0x4, 0xdd, 0x90,

    /* U+0026 "&" */
    0x0, 0x7, 0xde, 0x90, 0x0, 0x0, 0x0, 0x6c,
    0x10, 0x99, 0x0, 0x0, 0x0, 0xa5, 0x0, 0x2d,
    0x0, 0x0, 0x0, 0x96, 0x0, 0x5b, 0x0, 0x0,
    0x0, 0x3e, 0x16, 0xd2, 0x0, 0x0, 0x0, 0x9,
    0xf9, 0x10, 0x5, 0x0, 0x0, 0x9b, 0xa9, 0x0,
    0x1d, 0x0, 0x8, 0x90, 0xb, 0x70, 0x3a, 0x0,
    0xf, 0x0, 0x0, 0xd5, 0x85, 0x0, 0x2d, 0x0,
    0x0, 0x1e, 0xd0, 0x0, 0xf, 0x10, 0x0, 0xa,
    0xe2, 0x0, 0x9, 0xb1, 0x1, 0x9a, 0x5d, 0x0,
    0x0, 0x8d, 0xed, 0x70, 0x8, 0xa0,

    /* U+0027 "'" */
    0xa, 0xb, 0xb, 0xb,

    /* U+0028 "(" */
    0x0, 0x15, 0x0, 0xb2, 0x7, 0x70, 0xd, 0x10,
    0x4a, 0x0, 0x86, 0x0, 0xb3, 0x0, 0xc2, 0x0,
    0xd1, 0x0, 0xc2, 0x0, 0xa4, 0x0, 0x68, 0x0,
    0x2c, 0x0, 0xb, 0x30, 0x3, 0xb0, 0x0, 0x67,

    /* U+0029 ")" */
    0x43, 0x0, 0x1c, 0x10, 0x5, 0x90, 0x0, 0xd1,
    0x0, 0x86, 0x0, 0x4a, 0x0, 0x1d, 0x0, 0xe,
    0x0, 0xf, 0x0, 0xe, 0x0, 0x2c, 0x0, 0x68,
    0x0, 0xb4, 0x2, 0xc0, 0xa, 0x40, 0x59, 0x0,

    /* U+002A "*" */
    0x0, 0x1a, 0x0, 0x27, 0x19, 0x28, 0x6, 0xbd,
    0xb3, 0x4, 0xbe, 0x91, 0x39, 0x19, 0x3a, 0x0,
    0x1a, 0x0, 0x0, 0x1, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x0, 0x0, 0xd0, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x1e, 0xee, 0xfe, 0xed, 0x0, 0x0, 0xd0, 0x0,
    0x0, 0x0, 0xd0, 0x0, 0x0, 0x0, 0xd0, 0x0,

    /* U+002C "," */
    0x3, 0x20, 0x99, 0x1, 0x80, 0xa1, 0x0, 0x0,

    /* U+002D "-" */
    0xcd, 0xdd, 0xda,

    /* U+002E "." */
    0x41, 0xb4,

    /* U+002F "/" */
    0x0, 0x0, 0x86, 0x0, 0x0, 0xd1, 0x0, 0x3,
    0xa0, 0x0, 0x9, 0x50, 0x0, 0xd, 0x0, 0x0,
    0x59, 0x0, 0x0, 0xb3, 0x0, 0x1, 0xd0, 0x0,
    0x7, 0x70, 0x0, 0xc, 0x20, 0x0, 0x2c, 0x0,
    0x0, 0x86, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x4d, 0xec, 0x30, 0x0, 0x3d, 0x20, 0x4e,
    0x10, 0xa, 0x50, 0x0, 0x88, 0x0, 0xf0, 0x0,
    0x3, 0xc0, 0x1e, 0x0, 0x0, 0xf, 0x2, 0xd0,
    0x0, 0x0, 0xf0, 0x2d, 0x0, 0x0, 0xf, 0x1,
    0xe0, 0x0, 0x0, 0xf0, 0xf, 0x0, 0x0, 0x3c,
    0x0, 0xa5, 0x0, 0x8, 0x80, 0x3, 0xe2, 0x4,
    0xe1, 0x0, 0x5, 0xdf, 0xc3, 0x0,

    /* U+0031 "1" */
    0x0, 0x2c, 0xa0, 0x8d, 0x9a, 0x47, 0x5, 0xa0,
    0x0, 0x5a, 0x0, 0x5, 0xa0, 0x0, 0x5a, 0x0,
    0x5, 0xa0, 0x0, 0x5a, 0x0, 0x5, 0xa0, 0x0,
    0x5a, 0x0, 0x5, 0xa0, 0x0, 0x5a,

    /* U+0032 "2" */
    0x0, 0x5d, 0xed, 0x50, 0x6, 0xc2, 0x3, 0xe3,
    0xc, 0x10, 0x0, 0x88, 0x0, 0x0, 0x0, 0x69,
    0x0, 0x0, 0x0, 0xa6, 0x0, 0x0, 0x3, 0xd0,
    0x0, 0x0, 0x1d, 0x20, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0xb, 0x70, 0x0, 0x0, 0x99, 0x0, 0x0,
    0x7, 0xb0, 0x0, 0x0, 0x2f, 0xee, 0xee, 0xee,

    /* U+0033 "3" */
    0x0, 0x7d, 0xec, 0x40, 0x8, 0xa1, 0x4, 0xe2,
    0x9, 0x0, 0x0, 0x96, 0x0, 0x0, 0x0, 0xa6,
    0x0, 0x0, 0x5, 0xd1, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x0, 0x4, 0xd4, 0x0, 0x0, 0x0, 0x4c,
    0x0, 0x0, 0x0, 0x1e, 0x1b, 0x0, 0x0, 0x4c,
    0xa, 0x91, 0x3, 0xd5, 0x0, 0x7d, 0xed, 0x50,

    /* U+0034 "4" */
    0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x9, 0x60,
    0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x0, 0xb4,
    0x0, 0x0, 0x0, 0x4c, 0x0, 0x60, 0x0, 0xc,
    0x30, 0x1d, 0x0, 0x6, 0xa0, 0x1, 0xd0, 0x0,
    0xd1, 0x0, 0x1d, 0x0, 0x5e, 0xee, 0xee, 0xfe,
    0x80, 0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x1,
    0xd0, 0x0, 0x0, 0x0, 0x1d, 0x0,

    /* U+0035 "5" */
    0xd, 0xee, 0xee, 0x60, 0xe0, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x4, 0xa0, 0x0, 0x0, 0x6b, 0xce,
    0xc4, 0x8, 0xb1, 0x4, 0xe3, 0x20, 0x0, 0x5,
    0xb0, 0x0, 0x0, 0x1e, 0x0, 0x0, 0x1, 0xe8,
    0x10, 0x0, 0x5b, 0x7b, 0x10, 0x4e, 0x30, 0x7d,
    0xec, 0x40,

    /* U+0036 "6" */
    0x0, 0x0, 0x2d, 0x0, 0x0, 0x0, 0xb, 0x30,
    0x0, 0x0, 0x5, 0x80, 0x0, 0x0, 0x1, 0xb0,
    0x0, 0x0, 0x0, 0x9d, 0xed, 0x60, 0x0, 0x3e,
    0x30, 0x2d, 0x60, 0xb, 0x40, 0x0, 0x2e, 0x0,
    0xf0, 0x0, 0x0, 0xe2, 0x1f, 0x0, 0x0, 0xe,
    0x20, 0xe3, 0x0, 0x2, 0xe0, 0x6, 0xd3, 0x2,
    0xc6, 0x0, 0x6, 0xdf, 0xd6, 0x0,

    /* U+0037 "7" */
    0x1e, 0xee, 0xee, 0xee, 0x0, 0x0, 0x0, 0x69,
    0x0, 0x0, 0x0, 0xc3, 0x0, 0x0, 0x2, 0xd0,
    0x0, 0x0, 0x9, 0x60, 0x0, 0x0, 0xe, 0x10,
    0x0, 0x0, 0x5a, 0x0, 0x0, 0x0, 0xc4, 0x0,
    0x0, 0x2, 0xd0, 0x0, 0x0, 0x8, 0x70, 0x0,
    0x0, 0xe, 0x10, 0x0, 0x0, 0x5b, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x6d, 0xed, 0x40, 0x0, 0x6c, 0x10, 0x2d,
    0x30, 0xc, 0x40, 0x0, 0x69, 0x0, 0xb4, 0x0,
    0x6, 0x80, 0x4, 0xc2, 0x3, 0xd2, 0x0, 0x8,
    0xff, 0xf6, 0x0, 0x7, 0xb2, 0x3, 0xc5, 0x1,
    0xe0, 0x0, 0x2, 0xd0, 0x3d, 0x0, 0x0, 0xf,
    0x1, 0xe0, 0x0, 0x2, 0xe0, 0x9, 0xa1, 0x2,
    0xc7, 0x0, 0x7, 0xde, 0xd6, 0x0,

    /* U+0039 "9" */
    0x0, 0x8e, 0xec, 0x40, 0xa, 0xa1, 0x3, 0xd4,
    0x2e, 0x0, 0x0, 0x4c, 0x4b, 0x0, 0x0, 0x1e,
    0x2e, 0x0, 0x0, 0x4d, 0xb, 0xa1, 0x3, 0xe8,
    0x0, 0x9e, 0xed, 0xf1, 0x0, 0x0, 0xd, 0x70,
    0x0, 0x0, 0x6d, 0x0, 0x0, 0x0, 0xe3, 0x0,
    0x0, 0x8, 0x90, 0x0, 0x0, 0x2e, 0x0, 0x0,

    /* U+003A ":" */
    0x96, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x32,
    0x97,

    /* U+003B ";" */
    0x79, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x7b, 0x9, 0x93, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x17, 0x0, 0x0, 0x2a, 0xd6,
    0x0, 0x4b, 0xc5, 0x0, 0xd, 0xb3, 0x0, 0x0,
    0x8, 0xd7, 0x10, 0x0, 0x0, 0x7, 0xd9, 0x20,
    0x0, 0x0, 0x5, 0xca, 0x0, 0x0, 0x0, 0x3,

    /* U+003D "=" */
    0x1d, 0xdd, 0xdd, 0xdc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xdd, 0xdd, 0xdc,

    /* U+003E ">" */
    0x17, 0x0, 0x0, 0x0, 0x7, 0xd8, 0x10, 0x0,
    0x0, 0x6, 0xda, 0x30, 0x0, 0x0, 0x5, 0xcb,
    0x0, 0x0, 0x29, 0xd7, 0x0, 0x3a, 0xd6, 0x0,
    0xc, 0xb4, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x6, 0xde, 0xb2, 0x7, 0xb1, 0x5, 0xe0, 0x70,
    0x0, 0xb, 0x40, 0x0, 0x0, 0xc3, 0x0, 0x0,
    0x5c, 0x0, 0x0, 0x3c, 0x10, 0x0, 0xd, 0x20,
    0x0, 0x5, 0x80, 0x0, 0x0, 0x75, 0x0, 0x0,
    0x3, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x30, 0x0, 0x0, 0x5b, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x5, 0xbd, 0xdc, 0x82, 0x0, 0x0,
    0x0, 0x3, 0xd8, 0x20, 0x1, 0x6d, 0x60, 0x0,
    0x0, 0x3d, 0x20, 0x0, 0x0, 0x0, 0xb6, 0x0,
    0x0, 0xd2, 0x0, 0x0, 0x0, 0x0, 0xd, 0x20,
    0x6, 0x80, 0x1, 0xae, 0xd6, 0x72, 0x6, 0x80,
    0xb, 0x20, 0xb, 0x70, 0xa, 0xd3, 0x1, 0xd0,
    0xd, 0x0, 0x3c, 0x0, 0x0, 0xe3, 0x0, 0xd0,
    0xd, 0x0, 0x69, 0x0, 0x0, 0xc3, 0x0, 0xc0,
    0xc, 0x0, 0x59, 0x0, 0x0, 0xc3, 0x0, 0xd0,
    0xd, 0x0, 0x2c, 0x0, 0x0, 0xe3, 0x0, 0xc0,
    0xb, 0x20, 0xb, 0x70, 0x1a, 0x98, 0x7, 0x70,
    0x6, 0x80, 0x0, 0x9d, 0xc5, 0xa, 0xd9, 0x0,
    0x0, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd8, 0x20, 0x1, 0x7a, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xbd, 0xdc, 0x71, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x7b, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xd1, 0x0, 0x0, 0x0, 0x3, 0x96, 0x70, 0x0,
    0x0, 0x0, 0x93, 0x1d, 0x0, 0x0, 0x0, 0xc,
    0x0, 0xa4, 0x0, 0x0, 0x5, 0x70, 0x4, 0xa0,
    0x0, 0x0, 0xb1, 0x0, 0xd, 0x10, 0x0, 0x1f,
    0xee, 0xee, 0xf6, 0x0, 0x7, 0x50, 0x0, 0x2,
    0xc0, 0x0, 0xd0, 0x0, 0x0, 0xc, 0x30, 0x3b,
    0x0, 0x0, 0x0, 0x69, 0x9, 0x50, 0x0, 0x0,
    0x1, 0xe0,

    /* U+0042 "B" */
    0x8f, 0xee, 0xeb, 0x40, 0x8, 0x70, 0x0, 0x4e,
    0x40, 0x87, 0x0, 0x0, 0x6a, 0x8, 0x70, 0x0,
    0x6, 0xa0, 0x87, 0x0, 0x4, 0xd3, 0x8, 0xed,
    0xdf, 0xf8, 0x0, 0x87, 0x0, 0x2, 0xaa, 0x8,
    0x70, 0x0, 0x0, 0xd3, 0x87, 0x0, 0x0, 0xa,
    0x58, 0x70, 0x0, 0x0, 0xd4, 0x87, 0x0, 0x1,
    0x9c, 0x8, 0xee, 0xee, 0xe9, 0x10,

    /* U+0043 "C" */
    0x0, 0x4, 0xbe, 0xeb, 0x30, 0x0, 0x6d, 0x40,
    0x4, 0xd3, 0x3, 0xe1, 0x0, 0x0, 0x36, 0xa,
    0x60, 0x0, 0x0, 0x0, 0xf, 0x10, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x10, 0x0, 0x0, 0x0,
    0xa, 0x60, 0x0, 0x0, 0x0, 0x3, 0xe1, 0x0,
    0x0, 0x36, 0x0, 0x6d, 0x40, 0x4, 0xd3, 0x0,
    0x4, 0xbe, 0xeb, 0x30,

    /* U+0044 "D" */
    0x8f, 0xee, 0xea, 0x40, 0x0, 0x87, 0x0, 0x4,
    0xd8, 0x0, 0x87, 0x0, 0x0, 0xc, 0x60, 0x87,
    0x0, 0x0, 0x2, 0xe0, 0x87, 0x0, 0x0, 0x0,
    0xc3, 0x87, 0x0, 0x0, 0x0, 0xa5, 0x87, 0x0,
    0x0, 0x0, 0xa5, 0x87, 0x0, 0x0, 0x0, 0xc3,
    0x87, 0x0, 0x0, 0x2, 0xe0, 0x87, 0x0, 0x0,
    0xc, 0x60, 0x87, 0x0, 0x4, 0xd9, 0x0, 0x8f,
    0xee, 0xea, 0x40, 0x0,

    /* U+0045 "E" */
    0x8f, 0xee, 0xee, 0xe3, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x8e, 0xee, 0xee, 0x90,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x8f, 0xee, 0xee, 0xe6,

    /* U+0046 "F" */
    0x8f, 0xee, 0xee, 0xe3, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x8f, 0xee, 0xee, 0x90,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x2, 0xae, 0xed, 0x70, 0x0, 0x5, 0xd5,
    0x0, 0x18, 0xb0, 0x2, 0xe1, 0x0, 0x0, 0x3,
    0x0, 0xa6, 0x0, 0x0, 0x0, 0x0, 0xf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x6, 0xee,
    0xe2, 0xf, 0x0, 0x0, 0x0, 0xc, 0x20, 0xe1,
    0x0, 0x0, 0x0, 0xc2, 0xa, 0x70, 0x0, 0x0,
    0xc, 0x20, 0x2e, 0x20, 0x0, 0x0, 0xc2, 0x0,
    0x5e, 0x60, 0x1, 0x9b, 0x0, 0x0, 0x3a, 0xee,
    0xd7, 0x0,

    /* U+0048 "H" */
    0x87, 0x0, 0x0, 0x0, 0xc2, 0x87, 0x0, 0x0,
    0x0, 0xc2, 0x87, 0x0, 0x0, 0x0, 0xc2, 0x87,
    0x0, 0x0, 0x0, 0xc2, 0x87, 0x0, 0x0, 0x0,
    0xc2, 0x8e, 0xee, 0xee, 0xee, 0xf2, 0x87, 0x0,
    0x0, 0x0, 0xc2, 0x87, 0x0, 0x0, 0x0, 0xc2,
    0x87, 0x0, 0x0, 0x0, 0xc2, 0x87, 0x0, 0x0,
    0x0, 0xc2, 0x87, 0x0, 0x0, 0x0, 0xc2, 0x87,
    0x0, 0x0, 0x0, 0xc2,

    /* U+0049 "I" */
    0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87,
    0x87, 0x87, 0x87, 0x87,

    /* U+004A "J" */
    0x0, 0x0, 0x59, 0x0, 0x0, 0x59, 0x0, 0x0,
    0x59, 0x0, 0x0, 0x59, 0x0, 0x0, 0x59, 0x0,
    0x0, 0x59, 0x0, 0x0, 0x59, 0x0, 0x0, 0x59,
    0x0, 0x0, 0x69, 0x20, 0x0, 0x87, 0xa7, 0x2,
    0xd2, 0x1b, 0xed, 0x50,

    /* U+004B "K" */
    0x87, 0x0, 0x0, 0xc, 0x60, 0x87, 0x0, 0x0,
    0xb7, 0x0, 0x87, 0x0, 0xa, 0x80, 0x0, 0x87,
    0x0, 0x99, 0x0, 0x0, 0x87, 0x7, 0xa0, 0x0,
    0x0, 0x87, 0x6f, 0x70, 0x0, 0x0, 0x8c, 0xc2,
    0xe3, 0x0, 0x0, 0x8d, 0x10, 0x4d, 0x0, 0x0,
    0x87, 0x0, 0x8, 0xa0, 0x0, 0x87, 0x0, 0x0,
    0xc6, 0x0, 0x87, 0x0, 0x0, 0x2e, 0x20, 0x87,
    0x0, 0x0, 0x5, 0xd0,

    /* U+004C "L" */
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x8f, 0xee, 0xee, 0xe3,

    /* U+004D "M" */
    0x89, 0x0, 0x0, 0x0, 0x0, 0xe2, 0x8f, 0x30,
    0x0, 0x0, 0x9, 0xf2, 0x8a, 0xd0, 0x0, 0x0,
    0x3d, 0xc2, 0x86, 0x97, 0x0, 0x0, 0xc3, 0xc2,
    0x86, 0x1d, 0x20, 0x6, 0x90, 0xc2, 0x86, 0x5,
    0xb0, 0x1d, 0x10, 0xc2, 0x86, 0x0, 0xb5, 0xa5,
    0x0, 0xc2, 0x86, 0x0, 0x2e, 0xb0, 0x0, 0xc2,
    0x86, 0x0, 0x5, 0x20, 0x0, 0xc2, 0x86, 0x0,
    0x0, 0x0, 0x0, 0xc2, 0x86, 0x0, 0x0, 0x0,
    0x0, 0xc2, 0x86, 0x0, 0x0, 0x0, 0x0, 0xc2,

    /* U+004E "N" */
    0x8a, 0x0, 0x0, 0x0, 0xc1, 0x8f, 0x40, 0x0,
    0x0, 0xc1, 0x88, 0xd1, 0x0, 0x0, 0xc1, 0x86,
    0x6a, 0x0, 0x0, 0xc1, 0x86, 0xb, 0x50, 0x0,
    0xc1, 0x86, 0x2, 0xd1, 0x0, 0xc1, 0x86, 0x0,
    0x6a, 0x0, 0xc1, 0x86, 0x0, 0xb, 0x50, 0xc1,
    0x86, 0x0, 0x1, 0xd1, 0xc1, 0x86, 0x0, 0x0,
    0x6b, 0xc1, 0x86, 0x0, 0x0, 0xb, 0xf1, 0x86,
    0x0, 0x0, 0x1, 0xe1,

    /* U+004F "O" */
    0x0, 0x4, 0xbe, 0xeb, 0x40, 0x0, 0x0, 0x7d,
    0x40, 0x3, 0xc7, 0x0, 0x3, 0xd1, 0x0, 0x0,
    0xd, 0x40, 0xa, 0x60, 0x0, 0x0, 0x5, 0xb0,
    0xf, 0x10, 0x0, 0x0, 0x0, 0xf0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x1f, 0x0, 0x0, 0x0,
    0x0, 0xe1, 0xf, 0x10, 0x0, 0x0, 0x0, 0xf0,
    0xa, 0x60, 0x0, 0x0, 0x5, 0xb0, 0x3, 0xd1,
    0x0, 0x0, 0xd, 0x40, 0x0, 0x7d, 0x40, 0x4,
    0xd8, 0x0, 0x0, 0x4, 0xbe, 0xec, 0x40, 0x0,

    /* U+0050 "P" */
    0x8f, 0xee, 0xe9, 0x10, 0x87, 0x0, 0x6, 0xe1,
    0x87, 0x0, 0x0, 0x88, 0x87, 0x0, 0x0, 0x5a,
    0x87, 0x0, 0x0, 0x97, 0x87, 0x0, 0x17, 0xe1,
    0x8e, 0xee, 0xd9, 0x10, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x87, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4, 0xbe, 0xeb, 0x40, 0x0, 0x0, 0x7d,
    0x40, 0x3, 0xc8, 0x0, 0x3, 0xd1, 0x0, 0x0,
    0xd, 0x40, 0xa, 0x60, 0x0, 0x0, 0x5, 0xb0,
    0xf, 0x10, 0x0, 0x0, 0x0, 0xf0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x1f, 0x0, 0x0, 0x0,
    0x0, 0xe1, 0xf, 0x10, 0x0, 0x0, 0x0, 0xf0,
    0xa, 0x60, 0x0, 0x0, 0x5, 0xa0, 0x3, 0xd1,
    0x0, 0x0, 0xd, 0x30, 0x0, 0x7d, 0x40, 0x4,
    0xc7, 0x0, 0x0, 0x4, 0xbe, 0xee, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xc8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x64,

    /* U+0052 "R" */
    0x8f, 0xee, 0xe9, 0x10, 0x8, 0x70, 0x0, 0x7e,
    0x10, 0x87, 0x0, 0x0, 0x97, 0x8, 0x70, 0x0,
    0x6, 0x90, 0x87, 0x0, 0x0, 0xa7, 0x8, 0x70,
    0x1, 0x8d, 0x0, 0x8f, 0xee, 0xf9, 0x10, 0x8,
    0x70, 0x9, 0x80, 0x0, 0x87, 0x0, 0xd, 0x40,
    0x8, 0x70, 0x0, 0x3d, 0x10, 0x87, 0x0, 0x0,
    0x7a, 0x8, 0x70, 0x0, 0x0, 0xc6,

    /* U+0053 "S" */
    0x0, 0x8d, 0xed, 0x80, 0x0, 0xaa, 0x10, 0x1a,
    0xa0, 0x1f, 0x0, 0x0, 0x8, 0x1, 0xf0, 0x0,
    0x0, 0x0, 0xa, 0xb1, 0x0, 0x0, 0x0, 0x6,
    0xdb, 0x60, 0x0, 0x0, 0x0, 0x28, 0xe4, 0x0,
    0x0, 0x0, 0x3, 0xe0, 0x0, 0x0, 0x0, 0xc,
    0x36, 0x80, 0x0, 0x0, 0xd2, 0xd, 0x81, 0x1,
    0x9b, 0x0, 0x8, 0xde, 0xe9, 0x10,

    /* U+0054 "T" */
    0xbe, 0xee, 0xfe, 0xee, 0x70, 0x0, 0x1d, 0x0,
    0x0, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x0, 0x1d,
    0x0, 0x0, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x0,
    0x1d, 0x0, 0x0, 0x0, 0x1, 0xd0, 0x0, 0x0,
    0x0, 0x1d, 0x0, 0x0, 0x0, 0x1, 0xd0, 0x0,
    0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x1, 0xd0,
    0x0, 0x0, 0x0, 0x1d, 0x0, 0x0,

    /* U+0055 "U" */
    0xa4, 0x0, 0x0, 0x0, 0xfa, 0x40, 0x0, 0x0,
    0xf, 0xa4, 0x0, 0x0, 0x0, 0xfa, 0x40, 0x0,
    0x0, 0xf, 0xa4, 0x0, 0x0, 0x0, 0xfa, 0x40,
    0x0, 0x0, 0xf, 0xa4, 0x0, 0x0, 0x0, 0xfa,
    0x50, 0x0, 0x0, 0xf, 0x87, 0x0, 0x0, 0x2,
    0xd3, 0xd0, 0x0, 0x0, 0x98, 0xa, 0xa2, 0x0,
    0x7e, 0x10, 0x7, 0xde, 0xe9, 0x10,

    /* U+0056 "V" */
    0x97, 0x0, 0x0, 0x0, 0x1d, 0x3, 0xd0, 0x0,
    0x0, 0x7, 0x70, 0xd, 0x30, 0x0, 0x0, 0xd1,
    0x0, 0x79, 0x0, 0x0, 0x2b, 0x0, 0x1, 0xe0,
    0x0, 0x8, 0x50, 0x0, 0xb, 0x40, 0x0, 0xd0,
    0x0, 0x0, 0x5a, 0x0, 0x4a, 0x0, 0x0, 0x0,
    0xe1, 0xa, 0x40, 0x0, 0x0, 0x8, 0x60, 0xd0,
    0x0, 0x0, 0x0, 0x2c, 0x58, 0x0, 0x0, 0x0,
    0x0, 0xcd, 0x20, 0x0, 0x0, 0x0, 0x6, 0xc0,
    0x0, 0x0,

    /* U+0057 "W" */
    0xa6, 0x0, 0x0, 0xe, 0x30, 0x0, 0x0, 0xd0,
    0x5b, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x5, 0x90,
    0xe, 0x0, 0x0, 0x79, 0xd0, 0x0, 0x9, 0x40,
    0xb, 0x40, 0x0, 0xb2, 0xd2, 0x0, 0xd, 0x0,
    0x6, 0x90, 0x0, 0xd0, 0x86, 0x0, 0x2b, 0x0,
    0x1, 0xd0, 0x4, 0x80, 0x3b, 0x0, 0x76, 0x0,
    0x0, 0xc2, 0x9, 0x40, 0xe, 0x0, 0xb2, 0x0,
    0x0, 0x77, 0xd, 0x0, 0xa, 0x50, 0xc0, 0x0,
    0x0, 0x2c, 0x2a, 0x0, 0x5, 0x94, 0x80, 0x0,
    0x0, 0xd, 0x86, 0x0, 0x0, 0xe9, 0x30, 0x0,
    0x0, 0x9, 0xf1, 0x0, 0x0, 0xbe, 0x0, 0x0,
    0x0, 0x4, 0xc0, 0x0, 0x0, 0x6a, 0x0, 0x0,

    /* U+0058 "X" */
    0x4d, 0x0, 0x0, 0x0, 0x98, 0x9, 0x90, 0x0,
    0x4, 0xc0, 0x0, 0xd4, 0x0, 0x1d, 0x20, 0x0,
    0x2e, 0x10, 0xb5, 0x0, 0x0, 0x7, 0xb6, 0xa0,
    0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0, 0x0,
    0xce, 0x10, 0x0, 0x0, 0x9, 0x75, 0xc0, 0x0,
    0x0, 0x4c, 0x0, 0xa7, 0x0, 0x1, 0xd2, 0x0,
    0x1d, 0x30, 0xb, 0x60, 0x0, 0x4, 0xd0, 0x6b,
    0x0, 0x0, 0x0, 0x89,

    /* U+0059 "Y" */
    0x7a, 0x0, 0x0, 0x1, 0xd1, 0xd, 0x30, 0x0,
    0x8, 0x70, 0x4, 0xc0, 0x0, 0x2d, 0x0, 0x0,
    0xa6, 0x0, 0xa4, 0x0, 0x0, 0x1d, 0x13, 0xb0,
    0x0, 0x0, 0x6, 0x9c, 0x20, 0x0, 0x0, 0x0,
    0xc8, 0x0, 0x0, 0x0, 0x0, 0x95, 0x0, 0x0,
    0x0, 0x0, 0x95, 0x0, 0x0, 0x0, 0x0, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x95, 0x0, 0x0, 0x0,
    0x0, 0x95, 0x0, 0x0,

    /* U+005A "Z" */
    0xe, 0xee, 0xee, 0xef, 0x60, 0x0, 0x0, 0x3,
    0xd0, 0x0, 0x0, 0x0, 0xd3, 0x0, 0x0, 0x0,
    0x79, 0x0, 0x0, 0x0, 0x2d, 0x10, 0x0, 0x0,
    0xb, 0x50, 0x0, 0x0, 0x6, 0xb0, 0x0, 0x0,
    0x1, 0xe1, 0x0, 0x0, 0x0, 0xa6, 0x0, 0x0,
    0x0, 0x4c, 0x0, 0x0, 0x0, 0xd, 0x20, 0x0,
    0x0, 0x7, 0xfe, 0xee, 0xee, 0xea,

    /* U+005B "[" */
    0x8e, 0xe9, 0x85, 0x0, 0x85, 0x0, 0x85, 0x0,
    0x85, 0x0, 0x85, 0x0, 0x85, 0x0, 0x85, 0x0,
    0x85, 0x0, 0x85, 0x0, 0x85, 0x0, 0x85, 0x0,
    0x85, 0x0, 0x85, 0x0, 0x85, 0x0, 0x8e, 0xe9,

    /* U+005C "\\" */
    0x86, 0x0, 0x0, 0x2c, 0x0, 0x0, 0xc, 0x20,
    0x0, 0x7, 0x70, 0x0, 0x1, 0xd0, 0x0, 0x0,
    0xb3, 0x0, 0x0, 0x59, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x9, 0x50, 0x0, 0x3, 0xa0, 0x0, 0x0,
    0xd1, 0x0, 0x0, 0x86,

    /* U+005D "]" */
    0x9e, 0xe8, 0x0, 0x59, 0x0, 0x59, 0x0, 0x59,
    0x0, 0x59, 0x0, 0x59, 0x0, 0x59, 0x0, 0x59,
    0x0, 0x59, 0x0, 0x59, 0x0, 0x59, 0x0, 0x59,
    0x0, 0x59, 0x0, 0x59, 0x0, 0x59, 0x9e, 0xe8,

    /* U+005E "^" */
    0x0, 0x1f, 0x20, 0x0, 0x7, 0xb9, 0x0, 0x0,
    0xd1, 0xd1, 0x0, 0x59, 0x6, 0x70, 0xc, 0x20,
    0xd, 0x3, 0xb0, 0x0, 0x85,

    /* U+005F "_" */
    0xdd, 0xdd, 0xdd, 0x50,

    /* U+0060 "`" */
    0x7, 0x0, 0x7, 0x70, 0x0, 0xc0,

    /* U+0061 "a" */
    0x0, 0x7d, 0xec, 0x30, 0x5, 0x90, 0x2, 0xd0,
    0x0, 0x0, 0x0, 0x94, 0x0, 0x6b, 0xcc, 0xd6,
    0x8, 0xa1, 0x0, 0x96, 0xe, 0x0, 0x0, 0x86,
    0xf, 0x0, 0x0, 0xb6, 0xb, 0x70, 0x6, 0xd6,
    0x1, 0xad, 0xd8, 0x66,

    /* U+0062 "b" */
    0xa4, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0x0,
    0xa4, 0x7d, 0xea, 0x10, 0xab, 0x70, 0x6, 0xd0,
    0xaa, 0x0, 0x0, 0xa6, 0xa5, 0x0, 0x0, 0x4a,
    0xa4, 0x0, 0x0, 0x3b, 0xa5, 0x0, 0x0, 0x4a,
    0xaa, 0x0, 0x0, 0x96, 0xaa, 0x70, 0x6, 0xd0,
    0xa2, 0x7d, 0xea, 0x10,

    /* U+0063 "c" */
    0x0, 0x3c, 0xed, 0x60, 0x3, 0xd3, 0x1, 0xb4,
    0xb, 0x40, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x1d, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0xb, 0x40, 0x0, 0x0, 0x3, 0xd3, 0x1, 0xb4,
    0x0, 0x3c, 0xed, 0x60,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xa, 0x40, 0x0, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x0, 0xa, 0x40, 0x0, 0x0,
    0x0, 0xa4, 0x0, 0x4c, 0xec, 0x3a, 0x40, 0x4d,
    0x30, 0x2b, 0xc4, 0xc, 0x30, 0x0, 0x1f, 0x40,
    0xe0, 0x0, 0x0, 0xc4, 0x1d, 0x0, 0x0, 0xa,
    0x40, 0xe0, 0x0, 0x0, 0xc4, 0xc, 0x30, 0x0,
    0x1f, 0x40, 0x4d, 0x20, 0x1b, 0xb4, 0x0, 0x4d,
    0xec, 0x38, 0x40,

    /* U+0065 "e" */
    0x0, 0x4c, 0xed, 0x50, 0x3, 0xe3, 0x2, 0xd4,
    0xc, 0x50, 0x0, 0x4b, 0xf, 0x0, 0x0, 0xe,
    0x1f, 0xcc, 0xcc, 0xcd, 0xe, 0x0, 0x0, 0x0,
    0xb, 0x30, 0x0, 0x0, 0x3, 0xd3, 0x0, 0x86,
    0x0, 0x3c, 0xee, 0x80,

    /* U+0066 "f" */
    0x0, 0x3c, 0xe6, 0x0, 0xd4, 0x1, 0x0, 0xd0,
    0x0, 0x1, 0xc0, 0x0, 0x7d, 0xfd, 0xd0, 0x2,
    0xc0, 0x0, 0x2, 0xc0, 0x0, 0x2, 0xc0, 0x0,
    0x2, 0xc0, 0x0, 0x2, 0xc0, 0x0, 0x2, 0xc0,
    0x0, 0x2, 0xc0, 0x0, 0x2, 0xc0, 0x0,

    /* U+0067 "g" */
    0x0, 0x4c, 0xec, 0x38, 0x30, 0x3d, 0x30, 0x1b,
    0xa3, 0xc, 0x40, 0x0, 0x1f, 0x30, 0xe0, 0x0,
    0x0, 0xc3, 0x1d, 0x0, 0x0, 0xa, 0x30, 0xe0,
    0x0, 0x0, 0xc3, 0xc, 0x30, 0x0, 0x1f, 0x30,
    0x3d, 0x30, 0x1b, 0xc3, 0x0, 0x4c, 0xec, 0x3a,
    0x30, 0x0, 0x0, 0x0, 0xc2, 0x3, 0x0, 0x0,
    0x1e, 0x0, 0x7b, 0x10, 0x1b, 0x70, 0x0, 0x6d,
    0xed, 0x70, 0x0,

    /* U+0068 "h" */
    0xa4, 0x0, 0x0, 0xa, 0x40, 0x0, 0x0, 0xa4,
    0x0, 0x0, 0xa, 0x40, 0x0, 0x0, 0xa4, 0x8e,
    0xe8, 0xa, 0xc5, 0x1, 0xc6, 0xa8, 0x0, 0x3,
    0xca, 0x40, 0x0, 0x1d, 0xa4, 0x0, 0x0, 0xea,
    0x40, 0x0, 0xe, 0xa4, 0x0, 0x0, 0xea, 0x40,
    0x0, 0xe, 0xa4, 0x0, 0x0, 0xe0,

    /* U+0069 "i" */
    0xa5, 0x31, 0x0, 0xa4, 0xa4, 0xa4, 0xa4, 0xa4,
    0xa4, 0xa4, 0xa4, 0xa4,

    /* U+006A "j" */
    0x0, 0xa, 0x50, 0x0, 0x31, 0x0, 0x0, 0x0,
    0x0, 0xa4, 0x0, 0xa, 0x40, 0x0, 0xa4, 0x0,
    0xa, 0x40, 0x0, 0xa4, 0x0, 0xa, 0x40, 0x0,
    0xa4, 0x0, 0xa, 0x40, 0x0, 0xa4, 0x0, 0xa,
    0x40, 0x0, 0xb3, 0x10, 0x2d, 0x5, 0xed, 0x40,

    /* U+006B "k" */
    0xa4, 0x0, 0x0, 0xa, 0x40, 0x0, 0x0, 0xa4,
    0x0, 0x0, 0xa, 0x40, 0x0, 0x0, 0xa4, 0x0,
    0x1d, 0x3a, 0x40, 0xb, 0x50, 0xa4, 0x9, 0x80,
    0xa, 0x47, 0xa0, 0x0, 0xa9, 0xdd, 0x0, 0xa,
    0xd1, 0x99, 0x0, 0xa4, 0x0, 0xd4, 0xa, 0x40,
    0x3, 0xd0, 0xa4, 0x0, 0x8, 0xa0,

    /* U+006C "l" */
    0xa4, 0xa4, 0xa4, 0xa4, 0xa4, 0xa4, 0xa4, 0xa4,
    0xa4, 0xa4, 0xa4, 0xa4, 0xa4,

    /* U+006D "m" */
    0xa3, 0xae, 0xd3, 0xa, 0xec, 0x20, 0xab, 0x30,
    0x5d, 0x93, 0x5, 0xd0, 0xa7, 0x0, 0xc, 0x70,
    0x0, 0xd1, 0xa4, 0x0, 0xa, 0x40, 0x0, 0xa3,
    0xa4, 0x0, 0xa, 0x40, 0x0, 0xa3, 0xa4, 0x0,
    0xa, 0x40, 0x0, 0xa3, 0xa4, 0x0, 0xa, 0x40,
    0x0, 0xa3, 0xa4, 0x0, 0xa, 0x40, 0x0, 0xa3,
    0xa4, 0x0, 0xa, 0x40, 0x0, 0xa3,

    /* U+006E "n" */
    0xa3, 0x8e, 0xe8, 0xa, 0xb5, 0x1, 0xc6, 0xa8,
    0x0, 0x3, 0xca, 0x40, 0x0, 0x1d, 0xa4, 0x0,
    0x0, 0xea, 0x40, 0x0, 0xe, 0xa4, 0x0, 0x0,
    0xea, 0x40, 0x0, 0xe, 0xa4, 0x0, 0x0, 0xe0,

    /* U+006F "o" */
    0x0, 0x3b, 0xed, 0x60, 0x0, 0x2d, 0x40, 0x1a,
    0x80, 0xb, 0x40, 0x0, 0xe, 0x10, 0xe0, 0x0,
    0x0, 0x96, 0x1d, 0x0, 0x0, 0x7, 0x80, 0xe0,
    0x0, 0x0, 0x86, 0xb, 0x40, 0x0, 0xd, 0x10,
    0x2d, 0x40, 0x1a, 0x80, 0x0, 0x3b, 0xed, 0x60,
    0x0,

    /* U+0070 "p" */
    0xa2, 0x7d, 0xea, 0x10, 0xaa, 0x70, 0x6, 0xd0,
    0xaa, 0x0, 0x0, 0xa6, 0xa5, 0x0, 0x0, 0x4a,
    0xa4, 0x0, 0x0, 0x3b, 0xa5, 0x0, 0x0, 0x4a,
    0xaa, 0x0, 0x0, 0x96, 0xab, 0x70, 0x6, 0xd0,
    0xa4, 0x7d, 0xea, 0x10, 0xa4, 0x0, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x4c, 0xec, 0x39, 0x40, 0x4d, 0x30, 0x2b,
    0xb4, 0xc, 0x30, 0x0, 0x1f, 0x40, 0xe0, 0x0,
    0x0, 0xc4, 0x1d, 0x0, 0x0, 0xa, 0x40, 0xe0,
    0x0, 0x0, 0xc4, 0xc, 0x30, 0x0, 0x1f, 0x40,
    0x4d, 0x20, 0x1b, 0xc4, 0x0, 0x4d, 0xec, 0x3a,
    0x40, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0x0,
    0xa, 0x40, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x0,
    0x0, 0xa, 0x40,

    /* U+0072 "r" */
    0xa3, 0xbf, 0x6a, 0xc4, 0x0, 0xa8, 0x0, 0xa,
    0x50, 0x0, 0xa4, 0x0, 0xa, 0x40, 0x0, 0xa4,
    0x0, 0xa, 0x40, 0x0, 0xa4, 0x0, 0x0,

    /* U+0073 "s" */
    0x2, 0xce, 0xd6, 0x0, 0xd4, 0x1, 0xc3, 0x1e,
    0x0, 0x0, 0x0, 0xba, 0x20, 0x0, 0x0, 0x5b,
    0xd5, 0x0, 0x0, 0x2, 0xd4, 0x13, 0x0, 0x6,
    0x82, 0xd2, 0x1, 0xc4, 0x4, 0xce, 0xd6, 0x0,

    /* U+0074 "t" */
    0x2, 0x40, 0x0, 0x5, 0x90, 0x0, 0x5, 0x90,
    0x0, 0x9e, 0xed, 0xc0, 0x5, 0x90, 0x0, 0x5,
    0x90, 0x0, 0x5, 0x90, 0x0, 0x5, 0x90, 0x0,
    0x5, 0x90, 0x0, 0x5, 0x90, 0x0, 0x2, 0xd0,
    0x0, 0x0, 0x8e, 0xd2,

    /* U+0075 "u" */
    0xc2, 0x0, 0x2, 0xcc, 0x20, 0x0, 0x2c, 0xc2,
    0x0, 0x2, 0xcc, 0x20, 0x0, 0x2c, 0xc2, 0x0,
    0x2, 0xcb, 0x20, 0x0, 0x3c, 0xa5, 0x0, 0x6,
    0xc4, 0xd2, 0x4, 0xbc, 0x7, 0xee, 0xa1, 0xc0,

    /* U+0076 "v" */
    0xa6, 0x0, 0x0, 0x68, 0x3c, 0x0, 0x0, 0xb2,
    0xd, 0x20, 0x1, 0xc0, 0x7, 0x80, 0x7, 0x70,
    0x1, 0xe0, 0xc, 0x10, 0x0, 0xb4, 0x2b, 0x0,
    0x0, 0x5a, 0x75, 0x0, 0x0, 0xd, 0xc0, 0x0,
    0x0, 0x8, 0x90, 0x0,

    /* U+0077 "w" */
    0xa4, 0x0, 0x6, 0xa0, 0x0, 0xc, 0x59, 0x0,
    0xa, 0xe0, 0x0, 0x58, 0xe, 0x0, 0xc, 0x94,
    0x0, 0xa3, 0xa, 0x40, 0x48, 0x49, 0x0, 0xc0,
    0x5, 0x90, 0x93, 0xd, 0x3, 0x80, 0x0, 0xd0,
    0xc0, 0xa, 0x38, 0x30, 0x0, 0xa6, 0x90, 0x4,
    0x9c, 0x0, 0x0, 0x5e, 0x40, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x0, 0x0, 0xa4, 0x0,

    /* U+0078 "x" */
    0x6b, 0x0, 0x1, 0xd1, 0xb, 0x60, 0xb, 0x50,
    0x1, 0xe2, 0x6a, 0x0, 0x0, 0x6c, 0xd1, 0x0,
    0x0, 0xd, 0x80, 0x0, 0x0, 0x6b, 0xe1, 0x0,
    0x1, 0xd1, 0x6b, 0x0, 0xb, 0x50, 0xb, 0x60,
    0x6a, 0x0, 0x2, 0xe1,

    /* U+0079 "y" */
    0xb5, 0x0, 0x0, 0x69, 0x4b, 0x0, 0x0, 0xb3,
    0xe, 0x10, 0x1, 0xd0, 0x8, 0x70, 0x7, 0x70,
    0x2, 0xd0, 0xc, 0x10, 0x0, 0xc3, 0x3b, 0x0,
    0x0, 0x69, 0x85, 0x0, 0x0, 0xe, 0xd0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0xa, 0x30, 0x0,
    0x0, 0x1c, 0x0, 0x0, 0x0, 0x95, 0x0, 0x0,
    0xbe, 0x90, 0x0, 0x0,

    /* U+007A "z" */
    0x1d, 0xdd, 0xde, 0x90, 0x0, 0x1, 0xd1, 0x0,
    0x0, 0xa6, 0x0, 0x0, 0x4c, 0x0, 0x0, 0xd,
    0x20, 0x0, 0x8, 0x80, 0x0, 0x3, 0xd0, 0x0,
    0x0, 0xc4, 0x0, 0x0, 0x5f, 0xdd, 0xdd, 0xa0,

    /* U+007B "{" */
    0x0, 0xa, 0xd1, 0x0, 0x6a, 0x0, 0x0, 0x95,
    0x0, 0x0, 0xa4, 0x0, 0x0, 0xa4, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0xb3, 0x0, 0x1, 0xe0, 0x0,
    0x5f, 0x60, 0x0, 0x4, 0xe0, 0x0, 0x0, 0xb3,
    0x0, 0x0, 0xa4, 0x0, 0x0, 0xa4, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x79, 0x0, 0x0, 0x1a, 0xd1,

    /* U+007C "|" */
    0x15, 0x3b, 0x3b, 0x3b, 0x3b, 0x3b, 0x3b, 0x3b,
    0x3b, 0x3b, 0x3b, 0x3b, 0x3b, 0x3b, 0x3b, 0x3b,

    /* U+007D "}" */
    0x9c, 0x30, 0x0, 0x3d, 0x0, 0x0, 0xe0, 0x0,
    0xd, 0x10, 0x0, 0xd1, 0x0, 0xd, 0x10, 0x0,
    0xc2, 0x0, 0x9, 0x70, 0x0, 0x1d, 0xc0, 0x8,
    0x91, 0x0, 0xd1, 0x0, 0xd, 0x10, 0x0, 0xd1,
    0x0, 0xe, 0x0, 0x3, 0xe0, 0x9, 0xd4, 0x0,

    /* U+007E "~" */
    0x1c, 0xc2, 0x2, 0xa9, 0x52, 0xc1, 0x77, 0xb0,
    0x3, 0xdb, 0x0,

    /* U+53C2 "参" */
    0x0, 0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xb1, 0x3, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x99, 0x0, 0x0, 0x6b, 0x10, 0x0,
    0x0, 0x5e, 0xb7, 0x88, 0x9a, 0xad, 0xe4, 0x0,
    0x0, 0x34, 0x32, 0x5b, 0x0, 0x0, 0x1a, 0x0,
    0x0, 0x0, 0x0, 0xc3, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xcc, 0xcf, 0xdc, 0xcd, 0xec, 0xcc, 0xc0,
    0x0, 0x0, 0x6a, 0x0, 0x0, 0xc3, 0x0, 0x0,
    0x0, 0x9, 0xa0, 0x4, 0xa6, 0x1b, 0x70, 0x0,
    0x5, 0xc6, 0x49, 0xc8, 0x20, 0x33, 0x7c, 0x50,
    0x28, 0x14, 0x83, 0x0, 0x5b, 0x92, 0x1, 0x92,
    0x0, 0x0, 0x15, 0xab, 0x71, 0x0, 0x74, 0x0,
    0x0, 0x3b, 0xa6, 0x10, 0x2, 0x8c, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x48, 0xc9, 0x40, 0x0, 0x0,
    0x1, 0x79, 0xbb, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+603B "总" */
    0x0, 0x0, 0x22, 0x0, 0x0, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x79, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x70, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0x1d, 0xdd, 0xdd, 0xde, 0xed, 0xd7, 0x0,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x49, 0x0,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x49, 0x0,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x49, 0x0,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x49, 0x0,
    0x0, 0x1c, 0xcc, 0xcc, 0xcc, 0xcc, 0xc7, 0x0,
    0x0, 0x0, 0x0, 0x7, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x1a, 0x3, 0xc0, 0x0, 0x48, 0x0,
    0x0, 0xc1, 0x2b, 0x0, 0x69, 0x6, 0xc, 0x20,
    0x3, 0xa0, 0x2b, 0x0, 0x1, 0xc, 0x3, 0xb0,
    0xc, 0x30, 0x1d, 0x0, 0x0, 0x1c, 0x0, 0xa4,
    0x4, 0x0, 0xa, 0xdc, 0xcc, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x2, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0xc, 0x0, 0x0, 0x6, 0x80, 0x0, 0x0,
    0x0, 0xc, 0x0, 0x78, 0x88, 0xe8, 0x88, 0x50,
    0x0, 0xc, 0x0, 0x37, 0xa4, 0x44, 0xd4, 0x20,
    0xc, 0xcf, 0xc5, 0x1, 0xb0, 0x0, 0xb0, 0x0,
    0x0, 0xc, 0x0, 0x0, 0xc0, 0x4, 0x80, 0x0,
    0x0, 0xc, 0x4, 0xcc, 0xcc, 0xcc, 0xcc, 0xc2,
    0x0, 0xd, 0x73, 0x0, 0x35, 0x0, 0x0, 0x0,
    0x5, 0xbe, 0x60, 0x0, 0xa3, 0x0, 0x0, 0x0,
    0x37, 0x1c, 0x8, 0xcd, 0xec, 0xcc, 0xec, 0xc1,
    0x0, 0xc, 0x0, 0xa, 0x40, 0x0, 0xc0, 0x0,
    0x0, 0xc, 0x0, 0x1e, 0x71, 0x9, 0x50, 0x0,
    0x0, 0xc, 0x0, 0x0, 0x5b, 0xca, 0x0, 0x0,
    0x0, 0xc, 0x0, 0x2, 0x7c, 0x7a, 0xb3, 0x0,
    0xb, 0xc8, 0x8, 0xdb, 0x50, 0x0, 0x2a, 0xb0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x10,

    /* U+6570 "数" */
    0x0, 0x0, 0x21, 0x0, 0x0, 0x21, 0x0, 0x0,
    0x4, 0x30, 0x84, 0x6, 0x10, 0xb1, 0x0, 0x0,
    0x0, 0xb1, 0x84, 0x39, 0x1, 0xb0, 0x0, 0x0,
    0x0, 0x34, 0x84, 0x70, 0x6, 0xc8, 0x88, 0x80,
    0xa, 0xbb, 0xdc, 0xbb, 0x2d, 0x55, 0x5d, 0x50,
    0x0, 0x6, 0xe8, 0x0, 0x5f, 0x0, 0xb, 0x0,
    0x0, 0x59, 0x88, 0xb2, 0x69, 0x30, 0x39, 0x0,
    0x9, 0x80, 0x84, 0x19, 0x4, 0x60, 0x75, 0x0,
    0x4, 0x4, 0x50, 0x0, 0x1, 0xb0, 0xb1, 0x0,
    0xb, 0xbe, 0xcb, 0xb9, 0x0, 0xa4, 0xc0, 0x0,
    0x0, 0x68, 0x0, 0x58, 0x0, 0x2f, 0x40, 0x0,
    0x0, 0xb9, 0x21, 0xc1, 0x0, 0x5f, 0x60, 0x0,
    0x0, 0x3, 0xce, 0x60, 0x3, 0xc1, 0xc4, 0x0,
    0x0, 0x4a, 0xa4, 0xa7, 0x4d, 0x10, 0x1d, 0x50,
    0xc, 0x82, 0x0, 0x1, 0xb1, 0x0, 0x1, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+672A "未" */
    0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xad, 0xdd, 0xde, 0xed, 0xdd, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0,
    0xc, 0xdd, 0xdd, 0xef, 0xfd, 0xdd, 0xdd, 0x90,
    0x0, 0x0, 0x0, 0xc9, 0x8a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x58, 0x58, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x89, 0x8, 0x50, 0xb5, 0x0, 0x0,
    0x0, 0x9, 0xa0, 0x8, 0x50, 0xb, 0x70, 0x0,
    0x3, 0xc8, 0x0, 0x8, 0x50, 0x0, 0xaa, 0x10,
    0x3d, 0x30, 0x0, 0x8, 0x50, 0x0, 0x5, 0xd1,
    0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0,

    /* U+6A21 "模" */
    0x0, 0x7, 0x0, 0x0, 0x80, 0x0, 0x70, 0x0,
    0x0, 0xc, 0x0, 0x0, 0xc0, 0x0, 0xc0, 0x0,
    0x0, 0xc, 0x0, 0xbc, 0xfc, 0xcc, 0xfc, 0xc2,
    0x1, 0x1c, 0x11, 0x0, 0xc0, 0x0, 0xc0, 0x0,
    0x1d, 0xdf, 0xdb, 0x1, 0x61, 0x11, 0x61, 0x0,
    0x0, 0x1f, 0x0, 0x3c, 0x99, 0x99, 0x9b, 0x50,
    0x0, 0x7f, 0x70, 0x38, 0x0, 0x0, 0x5, 0x50,
    0x0, 0xbc, 0x94, 0x3c, 0x99, 0x99, 0x9b, 0x50,
    0x5, 0x7c, 0xb, 0x38, 0x0, 0x0, 0x5, 0x50,
    0xc, 0x1c, 0x0, 0x2a, 0xaa, 0xfa, 0xaa, 0x30,
    0x47, 0xc, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0,
    0x0, 0xc, 0x2, 0xbb, 0xbd, 0xfc, 0xbb, 0xb3,
    0x0, 0xc, 0x0, 0x0, 0xc, 0x8b, 0x0, 0x0,
    0x0, 0xc, 0x0, 0x6, 0xc4, 0x4, 0xc5, 0x0,
    0x0, 0xc, 0x5, 0xb6, 0x0, 0x0, 0x6, 0xb5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6D4B "测" */
    0x3, 0x80, 0x5, 0x55, 0x54, 0x0, 0x0, 0xa0,
    0x5, 0xb2, 0xc4, 0x44, 0xc0, 0x90, 0xb, 0x0,
    0x3, 0x1b, 0x7, 0xb, 0xb, 0x0, 0xb0, 0x60,
    0x0, 0xb0, 0xa0, 0xb0, 0xb0, 0xb, 0x8, 0xa0,
    0xb, 0xa, 0xb, 0xb, 0x0, 0xb0, 0x7, 0xa0,
    0xb0, 0xa0, 0xb0, 0xb0, 0xb, 0x0, 0x2, 0xb,
    0xa, 0xb, 0xb, 0x0, 0xb0, 0x0, 0x0, 0xb0,
    0xa0, 0xb0, 0xb0, 0xb, 0x0, 0x39, 0xb, 0xa,
    0xb, 0xb, 0x0, 0xb0, 0x7, 0x50, 0xb2, 0xa0,
    0xb0, 0xb0, 0xb, 0x0, 0xb1, 0x0, 0x78, 0x50,
    0x2, 0x0, 0xb0, 0xc, 0x0, 0x1a, 0x8, 0x40,
    0x0, 0xb, 0x4, 0x80, 0x2c, 0x20, 0xb, 0x20,
    0x1, 0xb0, 0x83, 0x1c, 0x20, 0x0, 0x11, 0x5c,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7259 "牙" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x2,
    0xaa, 0xaa, 0xaa, 0xaa, 0xdc, 0xaa, 0x90, 0x0,
    0x23, 0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x8,
    0x50, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0, 0xd1,
    0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x1d, 0x22,
    0x22, 0x22, 0xa6, 0x22, 0x20, 0x1, 0xaa, 0xaa,
    0xad, 0xcd, 0xca, 0xaa, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x94, 0x0, 0x0, 0x0, 0x0, 0x2, 0xd2,
    0x9, 0x40, 0x0, 0x0, 0x0, 0x3, 0xd2, 0x0,
    0x94, 0x0, 0x0, 0x0, 0x7, 0xc2, 0x0, 0x9,
    0x40, 0x0, 0x0, 0x3c, 0x90, 0x0, 0x0, 0x94,
    0x0, 0x0, 0x9c, 0x30, 0x0, 0x0, 0xb, 0x30,
    0x0, 0x2, 0x0, 0x0, 0x9, 0xcd, 0xc0, 0x0,
    0x0,

    /* U+7EBF "线" */
    0x0, 0x2, 0x20, 0x0, 0x40, 0x10, 0x0, 0x0,
    0x0, 0xb3, 0x0, 0xb, 0x16, 0x90, 0x0, 0x0,
    0x3b, 0x0, 0x0, 0xa2, 0x5, 0xa0, 0x0, 0xb,
    0x30, 0x81, 0x9, 0x30, 0x4, 0x41, 0x5, 0x90,
    0x2b, 0x48, 0xdd, 0xcb, 0x97, 0x22, 0xe8, 0x9d,
    0x32, 0x38, 0x50, 0x0, 0x0, 0x26, 0x47, 0x90,
    0x0, 0x66, 0x1, 0x36, 0x50, 0x0, 0xd1, 0x7,
    0x9c, 0xec, 0xb8, 0x63, 0x0, 0x95, 0x0, 0x53,
    0x2b, 0x0, 0x18, 0x0, 0x5d, 0x8b, 0xa0, 0x0,
    0xc0, 0x1c, 0x30, 0xa, 0x74, 0x0, 0x0, 0xa,
    0x4c, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0x20, 0x39, 0x0, 0x37, 0xbc, 0x12, 0xb9, 0xd0,
    0x6, 0x66, 0xc9, 0x51, 0x1b, 0xc3, 0x6, 0x90,
    0xb2, 0x0, 0x0, 0x0, 0x40, 0x0, 0x8, 0xe8,
    0x0,

    /* U+7EC4 "组" */
    0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x90, 0x4, 0xbb, 0xbb, 0xbb, 0x0,
    0x0, 0xd, 0x10, 0x6, 0x82, 0x22, 0x2c, 0x0,
    0x0, 0x78, 0x3, 0xa6, 0x70, 0x0, 0xc, 0x0,
    0x1, 0xd0, 0xc, 0x26, 0x70, 0x0, 0xc, 0x0,
    0xc, 0xcb, 0xd8, 0x6, 0xca, 0xaa, 0xae, 0x0,
    0x5, 0x32, 0xc0, 0x6, 0x82, 0x22, 0x2d, 0x0,
    0x0, 0xa, 0x40, 0x6, 0x70, 0x0, 0xc, 0x0,
    0x0, 0x59, 0x2, 0x16, 0x70, 0x0, 0xc, 0x0,
    0x4, 0xfb, 0xdb, 0x26, 0xda, 0xaa, 0xae, 0x0,
    0x4, 0x52, 0x0, 0x6, 0x82, 0x22, 0x2d, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x70, 0x0, 0xc, 0x0,
    0x0, 0x2, 0x6a, 0x66, 0x70, 0x0, 0xc, 0x0,
    0x8, 0xdd, 0x95, 0x16, 0x70, 0x0, 0xc, 0x0,
    0x6, 0x20, 0x0, 0xcd, 0xec, 0xcc, 0xcf, 0xc6,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x5, 0x0, 0x0, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xc, 0x10, 0x0, 0xd0, 0x0, 0x0,
    0xb, 0xcc, 0xcf, 0xcc, 0xcc, 0xfc, 0xcc, 0xc2,
    0x0, 0x0, 0xc, 0x10, 0x0, 0xd0, 0x0, 0x0,
    0x0, 0x32, 0x4, 0x40, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x85, 0x6, 0x70, 0x1e, 0xaa, 0xaa, 0x90,
    0x0, 0x85, 0x6, 0x70, 0x86, 0x42, 0x22, 0x20,
    0x0, 0x85, 0x6, 0x72, 0xc0, 0x7b, 0x10, 0x0,
    0x0, 0x85, 0x6, 0x79, 0x30, 0x3, 0xc5, 0x0,
    0x0, 0x11, 0x2, 0x20, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x2c, 0xcc, 0xcc, 0xcc, 0xcc, 0xc5, 0x0,
    0x0, 0x39, 0x0, 0xc0, 0xa, 0x10, 0x56, 0x0,
    0x0, 0x39, 0x0, 0xc0, 0xa, 0x10, 0x56, 0x0,
    0x0, 0x39, 0x0, 0xc0, 0xa, 0x10, 0x56, 0x0,
    0x1c, 0xde, 0xcc, 0xfc, 0xce, 0xcc, 0xdd, 0xc7,

    /* U+8BD5 "试" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x63, 0x0, 0x0, 0x0, 0xc, 0xa, 0x0, 0x2,
    0xd2, 0x0, 0x0, 0x0, 0xc0, 0x3c, 0x10, 0x3,
    0xd0, 0x0, 0x0, 0xb, 0x10, 0x20, 0x0, 0x3,
    0x1d, 0xdd, 0xdd, 0xfd, 0xdd, 0xa1, 0x22, 0x0,
    0x0, 0x0, 0xa, 0x20, 0x0, 0x6a, 0xe1, 0x0,
    0x0, 0x0, 0x93, 0x0, 0x0, 0xb, 0x10, 0xdd,
    0xdd, 0xb7, 0x50, 0x0, 0x0, 0xb1, 0x0, 0xb,
    0x0, 0x66, 0x0, 0x0, 0xb, 0x10, 0x0, 0xb0,
    0x4, 0x80, 0x0, 0x0, 0xb1, 0x0, 0xb, 0x0,
    0x2a, 0x0, 0x20, 0xb, 0x2c, 0x0, 0xb0, 0x0,
    0xd0, 0xb, 0x0, 0xbc, 0x31, 0x5e, 0xbd, 0x2b,
    0x31, 0xa0, 0xd, 0x53, 0xc8, 0x52, 0x0, 0x5a,
    0x56, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0xae,
    0x10,

    /* U+8FDE "连" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,
    0x3, 0xb0, 0x0, 0x0, 0x95, 0x0, 0x0, 0x0,
    0x0, 0x89, 0x9, 0xcc, 0xfc, 0xcc, 0xcc, 0xa0,
    0x0, 0xb, 0x50, 0x7, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x20, 0xd, 0x11, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x59, 0x1, 0xb0, 0x0, 0x0,
    0x1c, 0xcc, 0x0, 0xd6, 0x56, 0xc5, 0x55, 0x20,
    0x0, 0xd, 0x0, 0x77, 0x78, 0xd7, 0x77, 0x30,
    0x0, 0xd, 0x0, 0x0, 0x1, 0xb0, 0x0, 0x0,
    0x0, 0xd, 0x3, 0x44, 0x45, 0xc4, 0x44, 0x40,
    0x0, 0xd, 0x7, 0x88, 0x89, 0xd8, 0x88, 0x80,
    0x0, 0xe, 0x10, 0x0, 0x1, 0xb0, 0x0, 0x0,
    0x0, 0x99, 0xb0, 0x0, 0x1, 0xb0, 0x0, 0x0,
    0x7, 0xa0, 0x5c, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0x0, 0x3, 0xad, 0xdc, 0xcb, 0xbc, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0x3, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x51, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x84, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x2b, 0xee, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x2b, 0xee, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xfd,
    0xdf, 0xff, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xb2,
    0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,

    /* U+F00B "" */
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x1b, 0xa0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0,
    0xbf, 0xff, 0xb0, 0xb, 0xff, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0xfb, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x3, 0x0, 0x0, 0x0, 0x3, 0x8, 0xfc, 0x10,
    0x0, 0x1c, 0xf8, 0xff, 0xfc, 0x10, 0x1c, 0xff,
    0xf5, 0xff, 0xfc, 0x2c, 0xff, 0xf5, 0x5, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x1d, 0xff, 0xfd, 0x10, 0x0, 0x1c,
    0xff, 0xff, 0xfc, 0x10, 0x1c, 0xff, 0xf9, 0xff,
    0xfc, 0x1c, 0xff, 0xf5, 0x5, 0xff, 0xfc, 0xdf,
    0xf5, 0x0, 0x5, 0xff, 0xd1, 0xa4, 0x0, 0x0,
    0x4, 0xa1,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x10, 0x6f, 0xf1, 0x3, 0x10, 0x0,
    0x0, 0x5f, 0xd0, 0x6f, 0xf1, 0x3f, 0xd1, 0x0,
    0x3, 0xff, 0xf1, 0x6f, 0xf1, 0x5f, 0xfd, 0x0,
    0xd, 0xff, 0x40, 0x6f, 0xf1, 0x9, 0xff, 0x70,
    0x4f, 0xf7, 0x0, 0x6f, 0xf1, 0x0, 0xcf, 0xe0,
    0x9f, 0xf0, 0x0, 0x6f, 0xf1, 0x0, 0x5f, 0xf3,
    0xbf, 0xc0, 0x0, 0x6f, 0xf1, 0x0, 0x2f, 0xf5,
    0xbf, 0xc0, 0x0, 0x4f, 0xe0, 0x0, 0x1f, 0xf6,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0xf, 0xfe, 0x10, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x6, 0xff, 0xd3, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xda, 0xbe, 0xff, 0xf4, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x17, 0xbd, 0xca, 0x50, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x4, 0xfd, 0xdf, 0xff, 0xff, 0xfd, 0xef, 0x40,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xfe, 0xdf, 0xff, 0xff, 0xfd, 0xdf, 0x40,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0xdd, 0x30, 0x3f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x4f,
    0xf4, 0x0, 0x0, 0x0, 0x9, 0xff, 0x99, 0xff,
    0xbf, 0xf4, 0x0, 0x0, 0x1, 0xbf, 0xf6, 0x22,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x2d, 0xfe, 0x35,
    0xff, 0x53, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xc1,
    0x8f, 0xff, 0xf8, 0x1c, 0xfe, 0x40, 0x7f, 0xfa,
    0x1a, 0xff, 0xff, 0xff, 0xa1, 0xaf, 0xf7, 0xcf,
    0x82, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x28, 0xfc,
    0x14, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x41, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0xf9, 0x0, 0x8f,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xf0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x6f, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x1b, 0xb1, 0xcf, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xc2, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F01C "" */
    0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x1e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F021 "" */
    0x0, 0x0, 0x6, 0xbd, 0xda, 0x50, 0x2, 0xff,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xfe, 0x42, 0xff,
    0x0, 0x7f, 0xff, 0xa7, 0x7b, 0xff, 0xf9, 0xff,
    0x5, 0xff, 0xc1, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xe, 0xfc, 0x0, 0x0, 0x2, 0x22, 0xdf, 0xff,
    0x5f, 0xf2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x8f, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xf8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2f, 0xf4,
    0xff, 0xfd, 0x22, 0x20, 0x0, 0x0, 0xcf, 0xe0,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x2c, 0xff, 0x40,
    0xff, 0x9f, 0xff, 0xb7, 0x6a, 0xff, 0xf7, 0x0,
    0xff, 0x24, 0xdf, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0x20, 0x5, 0xac, 0xdb, 0x60, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x8f, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x9, 0xff,
    0x0, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x1, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x0, 0xae,
    0xff, 0xff, 0xff, 0xff, 0x5, 0xf8, 0xdf, 0xff,
    0xff, 0xff, 0x2, 0x60, 0x0, 0x0, 0x9f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x3, 0xee, 0x10, 0x0, 0x0, 0x8, 0xff, 0x0,
    0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x5, 0xfc, 0x7, 0xf4, 0xdf, 0xff, 0xff,
    0xff, 0x2, 0x50, 0x5f, 0x60, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xd, 0xc0, 0xbd, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xe0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x6, 0xf7, 0xd,
    0xc0, 0xad, 0xdf, 0xff, 0xff, 0xff, 0x2, 0x50,
    0x5f, 0x60, 0xe9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x5, 0xfc, 0x6, 0xf4, 0x0, 0x0, 0x9, 0xff,
    0x0, 0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x2, 0xee, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,

    /* U+F03E "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xc, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xfe, 0x12, 0xef, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xe1, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x4e, 0xfe, 0x10, 0x0, 0x2, 0xff,
    0xff, 0xe1, 0x2, 0xc1, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F043 "" */
    0x0, 0x0, 0x4e, 0x40, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x30, 0x0, 0xc, 0xff, 0xff, 0xfc,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0xf2, 0xbf, 0xff,
    0xff, 0xfe, 0x9f, 0xa1, 0xbf, 0xff, 0xff, 0x92,
    0xff, 0xa2, 0x2f, 0xff, 0xf2, 0x4, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x2, 0x9e, 0xfe, 0x92, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x1, 0xcc, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff,
    0x40, 0x3, 0xef, 0xff, 0xff, 0x40, 0x3f, 0xff,
    0xff, 0xff, 0x44, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x45, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4f, 0xff, 0xff, 0xff, 0x40, 0x3, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff, 0x30,
    0x0, 0x1, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x20,
    0x0, 0x0, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x8e, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xf7, 0x0, 0x7f, 0xff,
    0xf7,

    /* U+F04D "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x10, 0x0,
    0x3, 0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xff,
    0xfe, 0x30, 0x4, 0xff, 0xff, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0x44, 0xff, 0xff,
    0xff, 0xf3, 0x4, 0xff, 0xff, 0xfe, 0x30, 0x4,
    0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xcc, 0x10,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x1a, 0x40, 0x0, 0x0, 0x1,
    0xdf, 0xf0, 0x0, 0x0, 0x1d, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xfa, 0x0, 0x0, 0x1d, 0xff, 0xa0,
    0x0, 0x1, 0xdf, 0xfa, 0x0, 0x0, 0xc, 0xff,
    0xa0, 0x0, 0x0, 0xd, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x1b, 0x50,

    /* U+F054 "" */
    0x4, 0xa1, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0xaf, 0xfc, 0x10, 0x0, 0x0, 0xa, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0xaf, 0xfc, 0x10, 0x0, 0x0,
    0xa, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8, 0xff,
    0xd1, 0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8,
    0xff, 0xd1, 0x0, 0x0, 0xf, 0xfd, 0x10, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x48, 0x88, 0x8c, 0xff, 0xc8,
    0x88, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x48, 0x88, 0x8c, 0xff, 0xc8, 0x88, 0x84, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0,

    /* U+F068 "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F06E "" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf, 0xfd,
    0x40, 0x0, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x9e,
    0x80, 0x4f, 0xff, 0x70, 0x4f, 0xff, 0xc0, 0x0,
    0xaf, 0xf8, 0xc, 0xff, 0xf4, 0xdf, 0xff, 0x80,
    0x9a, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0xdf, 0xff,
    0x80, 0xef, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0x4f,
    0xff, 0xc0, 0x8f, 0xff, 0xf8, 0xc, 0xff, 0xf4,
    0x7, 0xff, 0xf4, 0x8, 0xee, 0x80, 0x4f, 0xff,
    0x70, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4, 0xef,
    0xf8, 0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x5, 0xad, 0xff,
    0xda, 0x50, 0x0, 0x0,

    /* U+F070 "" */
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x80, 0x49,
    0xdf, 0xfd, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xd8, 0x8c, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xf8, 0x0, 0x0, 0x4e, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x69, 0xe8,
    0x4, 0xff, 0xf7, 0x0, 0x4, 0xe3, 0x0, 0x9f,
    0xfe, 0xff, 0x80, 0xcf, 0xff, 0x40, 0xd, 0xff,
    0x70, 0x5, 0xff, 0xff, 0xe0, 0x8f, 0xff, 0xd0,
    0xd, 0xff, 0xf7, 0x0, 0x2d, 0xff, 0xe0, 0x8f,
    0xff, 0xd0, 0x4, 0xff, 0xfc, 0x0, 0x0, 0xaf,
    0xf8, 0xcf, 0xff, 0x30, 0x0, 0x7f, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x3e, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xc8, 0x82, 0x1, 0xbf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfc,
    0x10, 0x8, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc8,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd8, 0x8d,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xb0, 0xb, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xc0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd0, 0xd,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf9, 0x9f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xe2, 0x2e, 0xff, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x90, 0x9, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xe3, 0x3e,
    0xff, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0x78, 0x8e, 0xff, 0x15, 0xff, 0xe8, 0xff, 0xe2,
    0x0, 0x2, 0xe5, 0x4f, 0xfe, 0x20, 0xfe, 0x20,
    0x0, 0x0, 0x13, 0xff, 0xf3, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x31, 0x0, 0x52, 0x0,
    0x0, 0x2, 0xef, 0xf4, 0x5e, 0x20, 0xfe, 0x20,
    0x78, 0x8e, 0xff, 0x51, 0xff, 0xe8, 0xff, 0xe2,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0x99,
    0xff, 0xd1, 0x0, 0x1, 0xdf, 0xf9, 0x0, 0x9f,
    0xfd, 0x10, 0x1d, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xd1, 0xbf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0xbf, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x1d, 0xff, 0x90,
    0x0, 0x9, 0xff, 0xd1, 0x1, 0xdf, 0xf9, 0x0,
    0x9f, 0xfd, 0x10, 0x0, 0x1d, 0xff, 0x99, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x1d, 0xff,
    0xff, 0xd1, 0xaf, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x6b, 0x1f, 0xf1, 0xb6, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x6b, 0x1f,
    0xf1, 0xb6, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xdf, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x8f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf0, 0xdf, 0xfd, 0xf, 0xff, 0xfd,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xea,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x4f, 0xff, 0x90, 0x0, 0x2, 0x8f,
    0xf3, 0x0, 0x6f, 0xff, 0xd0, 0x0, 0xa, 0xff,
    0xff, 0xe4, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xdb, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x8, 0xee, 0x80, 0x0, 0x0, 0x6, 0x61, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x2d, 0xff, 0xd0, 0xef,
    0x33, 0xfe, 0x0, 0x2e, 0xff, 0xf3, 0xe, 0xf3,
    0x3f, 0xe0, 0x2e, 0xff, 0xf3, 0x0, 0x8f, 0xff,
    0xff, 0x6e, 0xff, 0xf3, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xf6, 0xef,
    0xff, 0x30, 0x0, 0xef, 0x33, 0xfe, 0x2, 0xef,
    0xff, 0x30, 0xe, 0xf3, 0x3f, 0xe0, 0x2, 0xef,
    0xff, 0x30, 0x8f, 0xff, 0xf8, 0x0, 0x2, 0xdf,
    0xfd, 0x0, 0x8e, 0xe8, 0x0, 0x0, 0x0, 0x66,
    0x10,

    /* U+F0C5 "" */
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xd, 0x10, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf, 0xd1, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf, 0xfd, 0xdf, 0xf0, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,

    /* U+F0C7 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xd1, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x11, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x11, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21,

    /* U+F0E0 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xd2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2d,
    0xff, 0x62, 0xcf, 0xff, 0xff, 0xfc, 0x26, 0xff,
    0xff, 0xfa, 0x18, 0xff, 0xff, 0x81, 0xaf, 0xff,
    0xff, 0xff, 0xe3, 0x4d, 0xd4, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0x18, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F0E7 "" */
    0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xe, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xd, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x4, 0xde, 0x40, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x99, 0xff, 0xfd, 0x0, 0x0, 0xff, 0xff,
    0x99, 0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xd, 0xff, 0xff,
    0xd, 0x10, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf,
    0xd1, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf, 0xfd,
    0xff, 0xff, 0xf, 0xff, 0xff, 0x20, 0x0, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfd,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xee, 0x40, 0x0, 0x0,

    /* U+F11C "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0, 0xf0,
    0xf, 0x0, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0,
    0xf0, 0xf, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff, 0xf8,
    0x8, 0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0xff, 0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x17,
    0xef, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x18,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xdf, 0xff, 0xff, 0xf0, 0xd1, 0x0, 0xff, 0xff,
    0xff, 0xf0, 0xfd, 0x10, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xd1, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x70, 0x0, 0x4, 0xdf,
    0xff, 0xfc, 0xa8, 0x8a, 0xcf, 0xff, 0xfd, 0x40,
    0x6f, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0xf6, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x1a, 0x30, 0x0, 0x5a,
    0xdf, 0xfd, 0xa5, 0x0, 0x3, 0xa1, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xa8, 0x8a, 0xef, 0xff,
    0x50, 0x0, 0x0, 0x1, 0xdf, 0x70, 0x0, 0x0,
    0x7, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F241 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F242 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F243 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F244 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x29, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0x80, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x3, 0x70, 0x0, 0xdf,
    0xff, 0x77, 0xf7, 0x55, 0x55, 0x55, 0x55, 0x8f,
    0xd3, 0xf, 0xff, 0xfd, 0xcc, 0xdf, 0xdc, 0xcc,
    0xcc, 0xcd, 0xff, 0xb0, 0x8f, 0xfe, 0x10, 0x0,
    0xaa, 0x0, 0x0, 0x0, 0x4d, 0x40, 0x0, 0x46,
    0x10, 0x0, 0x1, 0xf2, 0x2, 0x33, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb1, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x18, 0xdf, 0xfd, 0x92, 0x0, 0x2, 0xef,
    0xfb, 0xef, 0xff, 0x40, 0xd, 0xff, 0xfa, 0x2e,
    0xff, 0xe0, 0x4f, 0xff, 0xfa, 0x3, 0xff, 0xf5,
    0x9f, 0xfa, 0xfa, 0x35, 0x4f, 0xfa, 0xcf, 0xc0,
    0x8a, 0x3d, 0xb, 0xfd, 0xef, 0xfb, 0x3, 0x11,
    0x8f, 0xfe, 0xff, 0xff, 0xb0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x8, 0xff, 0xff, 0xef, 0xfd,
    0x11, 0x10, 0x9f, 0xff, 0xdf, 0xd1, 0x59, 0x3b,
    0xb, 0xfd, 0xaf, 0xd6, 0xfa, 0x37, 0x1d, 0xfb,
    0x5f, 0xff, 0xfa, 0x1, 0xdf, 0xf7, 0xd, 0xff,
    0xfa, 0x1d, 0xff, 0xf1, 0x3, 0xef, 0xfc, 0xdf,
    0xff, 0x50, 0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x7f, 0xff, 0xf7, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9, 0x9f,
    0xf0, 0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0,
    0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8,
    0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f, 0x88,
    0xf8, 0x8f, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9,
    0x9f, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0x1d,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa,
    0x1d, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xfa, 0x1d, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0xdb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1d, 0xff, 0xff,
    0xfa, 0xef, 0xfe, 0xaf, 0xff, 0xff, 0x1, 0xdf,
    0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x2, 0xef, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x2, 0xef, 0xff, 0xff, 0x1d, 0xff,
    0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e, 0xff, 0xff,
    0x1, 0xdf, 0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa,
    0xff, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0xef,
    0xfe, 0xaf, 0xff, 0xff, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F7C2 "" */
    0x0, 0x7, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0x7, 0xf8, 0xf, 0xb,
    0x40, 0xff, 0x7f, 0xf8, 0xf, 0xb, 0x40, 0xff,
    0xff, 0xf8, 0xf, 0xb, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xe0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x10, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf1, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x11, 0xcf, 0xff, 0x77, 0x77, 0x77,
    0x77, 0xbf, 0xf1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 69, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 58, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13, .adv_w = 78, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 21, .adv_w = 159, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 81, .adv_w = 142, .box_w = 9, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 162, .adv_w = 193, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 234, .adv_w = 176, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 312, .adv_w = 43, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 316, .adv_w = 82, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 348, .adv_w = 82, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 380, .adv_w = 105, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 401, .adv_w = 142, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 433, .adv_w = 58, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 441, .adv_w = 126, .box_w = 6, .box_h = 1, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 444, .adv_w = 56, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 446, .adv_w = 94, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 482, .adv_w = 142, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 536, .adv_w = 142, .box_w = 5, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 566, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 614, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 662, .adv_w = 142, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 716, .adv_w = 142, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 758, .adv_w = 142, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 812, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 860, .adv_w = 142, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 914, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 962, .adv_w = 62, .box_w = 2, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 971, .adv_w = 64, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 983, .adv_w = 142, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1015, .adv_w = 142, .box_w = 8, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1031, .adv_w = 142, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1063, .adv_w = 107, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1109, .adv_w = 253, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1237, .adv_w = 166, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1303, .adv_w = 161, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1357, .adv_w = 164, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1417, .adv_w = 181, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1477, .adv_w = 148, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1525, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1573, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1639, .adv_w = 186, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1699, .adv_w = 63, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1711, .adv_w = 111, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1747, .adv_w = 166, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1807, .adv_w = 141, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1855, .adv_w = 218, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1927, .adv_w = 186, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1987, .adv_w = 193, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2059, .adv_w = 147, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2107, .adv_w = 193, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2197, .adv_w = 156, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2251, .adv_w = 141, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2305, .adv_w = 140, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2359, .adv_w = 181, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2413, .adv_w = 164, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2479, .adv_w = 244, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2575, .adv_w = 163, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2635, .adv_w = 153, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2695, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2749, .adv_w = 80, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2781, .adv_w = 94, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2817, .adv_w = 80, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2849, .adv_w = 114, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 2870, .adv_w = 102, .box_w = 7, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2874, .adv_w = 67, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 2880, .adv_w = 138, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2916, .adv_w = 154, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2968, .adv_w = 125, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3004, .adv_w = 154, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3063, .adv_w = 137, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3099, .adv_w = 84, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3138, .adv_w = 154, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3197, .adv_w = 146, .box_w = 7, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3243, .adv_w = 59, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3255, .adv_w = 59, .box_w = 5, .box_h = 16, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 3295, .adv_w = 128, .box_w = 7, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3341, .adv_w = 58, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3354, .adv_w = 216, .box_w = 12, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3408, .adv_w = 146, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3440, .adv_w = 151, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3481, .adv_w = 154, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3533, .adv_w = 154, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3592, .adv_w = 91, .box_w = 5, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3615, .adv_w = 114, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3647, .adv_w = 89, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3683, .adv_w = 146, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3715, .adv_w = 126, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3751, .adv_w = 195, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3805, .adv_w = 122, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3841, .adv_w = 127, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3893, .adv_w = 119, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3925, .adv_w = 87, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3973, .adv_w = 40, .box_w = 2, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3989, .adv_w = 87, .box_w = 5, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4029, .adv_w = 142, .box_w = 7, .box_h = 3, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 4040, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4168, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4296, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4424, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4552, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4672, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4800, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4913, .adv_w = 256, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5018, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5131, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5251, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5371, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5484, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5612, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5748, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5844, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5956, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6052, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6118, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6246, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6374, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6500, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6628, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6736, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6864, .adv_w = 128, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6920, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7004, .adv_w = 288, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7148, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7244, .adv_w = 176, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7332, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 7412, .adv_w = 224, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7538, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7643, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7741, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 7821, .adv_w = 224, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 7933, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8003, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8073, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8171, .adv_w = 224, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 8199, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8307, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8467, .adv_w = 288, .box_w = 20, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8627, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8755, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8825, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8895, .adv_w = 320, .box_w = 20, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9035, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9131, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9259, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9404, .adv_w = 224, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9509, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9621, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9719, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9817, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9913, .adv_w = 160, .box_w = 12, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10009, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10121, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10233, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10341, .adv_w = 256, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10503, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10599, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10749, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 10849, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 10949, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11049, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11149, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11249, .adv_w = 320, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11396, .adv_w = 224, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11492, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11604, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11749, .adv_w = 320, .box_w = 20, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11869, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11965, .adv_w = 258, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xc79, 0xfe3, 0x11ae, 0x1368, 0x165f, 0x1989, 0x1e97,
    0x2afd, 0x2b02, 0x311b, 0x3813, 0x3c1c, 0x9c3f, 0x9c46, 0x9c49,
    0x9c4a, 0x9c4b, 0x9c4f, 0x9c51, 0x9c53, 0x9c57, 0x9c5a, 0x9c5f,
    0x9c64, 0x9c65, 0x9c66, 0x9c7c, 0x9c81, 0x9c86, 0x9c89, 0x9c8a,
    0x9c8b, 0x9c8f, 0x9c90, 0x9c91, 0x9c92, 0x9ca5, 0x9ca6, 0x9cac,
    0x9cae, 0x9caf, 0x9cb2, 0x9cb5, 0x9cb6, 0x9cb7, 0x9cb9, 0x9cd1,
    0x9cd3, 0x9d02, 0x9d03, 0x9d05, 0x9d07, 0x9d1e, 0x9d25, 0x9d28,
    0x9d31, 0x9d5a, 0x9d62, 0x9d99, 0x9e29, 0x9e7e, 0x9e7f, 0x9e80,
    0x9e81, 0x9e82, 0x9ec5, 0x9ed1, 0x9f2b, 0x9f42, 0xa198, 0xa400,
    0xa4e0
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 21442, .range_length = 42209, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 73, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 28, 30, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 5, 0, 5, 5, 3,
    0, 3, 0, 0, 13, 0, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 6, 5, -10, -33, -21, 7, -7,
    0, -33, -1, 5, 0, 0, 0, 0,
    0, 0, -18, 0, -17, -5, 0, -12,
    -14, -1, -11, -8, -11, -7, 0, 0,
    0, -7, -25, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    -11, -7, 0, 0, 0, -10, 0, -8,
    0, -7, -4, -7, -12, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, -27, 0, -14, 6, 0,
    -17, -7, 0, 0, 0, -21, -3, -21,
    -15, 0, -24, 5, 0, 0, -2, 0,
    0, 0, 0, 0, 0, -8, 0, 0,
    0, -2, 0, 0, 0, -3, 0, 0,
    0, 4, 0, -9, 0, -9, -3, 0,
    -13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 6,
    0, -6, 5, 0, 8, -4, 0, 0,
    0, 1, 0, 1, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    2, 0, 2, -3, 0, 2, 0, 0,
    0, -3, 0, 0, -3, 0, -4, 0,
    -3, -4, 0, 0, -3, -3, -3, -4,
    -2, 0, -3, 6, 0, 1, -34, -16,
    8, -3, 0, -41, 0, 4, 0, 0,
    0, 0, 0, 0, -14, 0, -10, -5,
    0, -8, 0, -5, 0, -6, -10, -9,
    0, 0, 0, 0, 4, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, -3, 0, 0, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, 0, -26, 5, 0,
    2, -13, -2, 0, -2, 0, -4, 0,
    0, 0, 0, 0, -6, 0, -7, -9,
    0, -3, -3, -7, -8, -12, -6, 0,
    -12, -25, 0, -23, 6, 0, -19, -13,
    0, 4, -4, -34, -12, -38, -27, 0,
    -45, 0, -4, 0, -7, -7, 0, -1,
    -2, -8, -8, -23, 0, 0, -3, 5,
    0, 2, -35, -17, 4, 0, 0, -40,
    0, 0, 0, 1, 1, -3, 0, -8,
    -9, 0, -9, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, -1, 0, -2,
    10, 0, -1, -3, 0, 0, 2, -2,
    -3, -5, -3, 0, -10, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 5, 0, 0, -3,
    0, 0, -3, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 7, 0, -22, -26, -21, 8, -8,
    0, -35, 0, 4, 0, 5, 5, 0,
    0, 0, -30, 0, -25, -11, 0, -23,
    -28, -12, -22, -23, -25, -23, -3, 4,
    0, -5, -18, -15, 0, -4, 0, -17,
    0, 5, 0, 0, 0, 0, 0, 0,
    -18, 0, -15, -3, 0, -11, -10, 0,
    -6, -4, -6, -8, 0, 0, 5, -21,
    3, 0, 6, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -7, 0, 0, -3, -3, -5, -6, -10,
    0, 0, -8, 5, 5, -17, -30, -24,
    3, -12, 0, -35, -3, 0, 0, 0,
    0, 0, 0, 0, -27, 0, -24, -11,
    0, -19, -22, -7, -17, -15, -16, -18,
    0, 0, 3, -12, 5, 0, 2, -8,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -4, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, -10,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, -21, 0, -17, -15, -2, -23, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, -12, 0, -3,
    -8, 0, -9, 0, 0, 0, 0, -25,
    0, -17, -15, -7, -24, 0, -3, 0,
    0, -2, 0, 0, 0, -1, 0, -4,
    -5, -6, 0, 1, 0, 6, 6, 0,
    -3, 0, 0, 0, 0, -17, 0, -10,
    -5, 5, -16, 0, 0, 0, -1, 2,
    0, 0, 0, 5, 0, 0, 3, 3,
    0, 0, 3, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 4, 1, 0, -6, 0, 0, 0,
    0, -16, 0, -13, -9, -2, -18, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 2, 1, 0, 0, 12, 0, -2,
    -18, 0, 10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -5, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, -23, 0, -12,
    -11, 0, -19, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 6, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    0, -4, 7, 0, -10, 0, 0, 0,
    0, -20, 0, -12, -8, 0, -16, 0,
    -6, 0, -5, 0, 0, 0, -3, 0,
    -1, 0, 0, 0, 0, 7, 0, 2,
    -24, -8, -7, 0, 0, -26, 0, 0,
    0, -8, 0, -8, -14, 0, -11, 0,
    -8, 0, 0, 0, -2, 6, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, -23, 0, -16,
    -13, 0, -24, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, -1, 0, 3,
    0, -1, 1, -3, 6, 0, -7, 0,
    0, 0, 0, -20, 0, -10, 0, 0,
    -15, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -18, -8, -5, 0, 0, -18,
    0, -23, 0, -8, -4, -10, -15, 0,
    -3, 0, -4, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    3, 0, -9, 0, 0, 0, 0, -25,
    0, -11, -6, 0, -16, 0, -4, 0,
    -5, 0, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -9, 0, 0, 0, 0, -25, 0, -8,
    -6, 0, -19, 0, -5, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 30,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Light_16 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Light_16 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_LIGHT_16*/

