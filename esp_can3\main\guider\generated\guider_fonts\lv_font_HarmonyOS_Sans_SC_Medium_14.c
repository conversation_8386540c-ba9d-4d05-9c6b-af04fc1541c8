/*******************************************************************************
 * Size: 14 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_14
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_14 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_14

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x70, 0xf6, 0xf, 0x60, 0xf5, 0xf, 0x50,
    0xe4, 0xe, 0x40, 0xd4, 0x2, 0x1, 0xe6, 0x1e,
    0x60,

    /* U+0022 "\"" */
    0x3f, 0xb, 0x73, 0xf0, 0xc7, 0x3f, 0xb, 0x72,
    0xf0, 0xb7,

    /* U+0023 "#" */
    0x0, 0xa, 0xa0, 0x3f, 0x10, 0x0, 0xd, 0x70,
    0x6e, 0x0, 0x1, 0x2f, 0x51, 0xab, 0x10, 0xd,
    0xff, 0xff, 0xff, 0xf1, 0x4, 0xad, 0x45, 0xf7,
    0x40, 0x0, 0xb9, 0x4, 0xf0, 0x0, 0x11, 0xf6,
    0x19, 0xd1, 0x0, 0xdf, 0xff, 0xff, 0xff, 0x20,
    0x39, 0xe4, 0x5f, 0x84, 0x0, 0xa, 0xb0, 0x3f,
    0x20, 0x0, 0xd, 0x70, 0x6e, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x7, 0xd0, 0x0, 0x0, 0x7, 0xd0, 0x0,
    0x2, 0xcf, 0xfe, 0x50, 0xd, 0xda, 0xec, 0xf3,
    0x3f, 0x37, 0xd0, 0xc3, 0x3f, 0x57, 0xd0, 0x0,
    0xb, 0xfd, 0xd0, 0x0, 0x0, 0x8e, 0xfd, 0x40,
    0x0, 0x7, 0xed, 0xf4, 0x4, 0x7, 0xd0, 0xe9,
    0x8e, 0x17, 0xd0, 0xd9, 0x1e, 0xec, 0xeb, 0xf3,
    0x2, 0xaf, 0xfd, 0x50, 0x0, 0x7, 0xd0, 0x0,
    0x0, 0x7, 0xd0, 0x0,

    /* U+0025 "%" */
    0xa, 0xfc, 0x20, 0x6, 0xf2, 0x8, 0xe5, 0xcb,
    0x0, 0xe9, 0x0, 0xb9, 0x5, 0xe0, 0x8f, 0x10,
    0x9, 0xc1, 0xac, 0x1f, 0x70, 0x0, 0x1d, 0xfe,
    0x39, 0xe0, 0x0, 0x0, 0x2, 0x2, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0x1b, 0xfc, 0x20, 0x0,
    0x4f, 0x39, 0xd4, 0xcb, 0x0, 0xd, 0xa0, 0xb8,
    0x6, 0xe0, 0x6, 0xf2, 0x8, 0xc2, 0xbb, 0x0,
    0xe9, 0x0, 0xa, 0xfc, 0x10,

    /* U+0026 "&" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x2, 0xdf,
    0xf6, 0x0, 0x0, 0x0, 0xdd, 0x5a, 0xf2, 0x0,
    0x0, 0x1f, 0x70, 0x2f, 0x60, 0x0, 0x0, 0xf9,
    0x6, 0xf4, 0x0, 0x0, 0xa, 0xf7, 0xfa, 0x0,
    0x0, 0x0, 0x5f, 0xf8, 0x1, 0x81, 0x0, 0x6f,
    0xbf, 0xa0, 0x5f, 0x0, 0x2f, 0x90, 0x6f, 0x79,
    0xd0, 0x5, 0xf2, 0x0, 0x8f, 0xf7, 0x0, 0x5f,
    0x40, 0x0, 0xdf, 0x40, 0x0, 0xed, 0x54, 0xbf,
    0xde, 0x20, 0x2, 0xbf, 0xfb, 0x31, 0xed, 0x0,

    /* U+0027 "'" */
    0x3f, 0x3, 0xf0, 0x3f, 0x2, 0xf0,

    /* U+0028 "(" */
    0x0, 0x1d, 0x60, 0xa, 0xd0, 0x2, 0xf5, 0x0,
    0x8e, 0x0, 0xc, 0xb0, 0x0, 0xf8, 0x0, 0xf,
    0x70, 0x0, 0xf7, 0x0, 0xf, 0x80, 0x0, 0xda,
    0x0, 0x8, 0xe0, 0x0, 0x3f, 0x40, 0x0, 0xbc,
    0x0, 0x1, 0xe6,

    /* U+0029 ")" */
    0x6d, 0x0, 0x0, 0xda, 0x0, 0x5, 0xf2, 0x0,
    0xf, 0x80, 0x0, 0xbc, 0x0, 0x8, 0xf0, 0x0,
    0x7f, 0x0, 0x7, 0xf0, 0x0, 0x8f, 0x0, 0xa,
    0xc0, 0x0, 0xe8, 0x0, 0x4f, 0x20, 0xc, 0xa0,
    0x6, 0xe1, 0x0,

    /* U+002A "*" */
    0x0, 0x7a, 0x0, 0x4c, 0x89, 0xa7, 0x18, 0xff,
    0x91, 0x2b, 0xff, 0xd4, 0x28, 0x79, 0x64, 0x0,
    0x57, 0x0,

    /* U+002B "+" */
    0x0, 0x2, 0x20, 0x0, 0x0, 0x9, 0xb0, 0x0,
    0x0, 0x9, 0xb0, 0x0, 0x1, 0x19, 0xb1, 0x10,
    0x5f, 0xff, 0xff, 0xf7, 0x14, 0x4b, 0xc4, 0x41,
    0x0, 0x9, 0xb0, 0x0, 0x0, 0x9, 0xb0, 0x0,

    /* U+002C "," */
    0x0, 0x0, 0xd8, 0xd, 0xc0, 0x4a, 0x1d, 0x20,
    0x10,

    /* U+002D "-" */
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0x3, 0x33,
    0x33,

    /* U+002E "." */
    0x0, 0x1, 0xe6, 0x1e, 0x60,

    /* U+002F "/" */
    0x0, 0x2, 0xf4, 0x0, 0x7, 0xe0, 0x0, 0xd,
    0x90, 0x0, 0x3f, 0x30, 0x0, 0x8e, 0x0, 0x0,
    0xe8, 0x0, 0x3, 0xf2, 0x0, 0x9, 0xd0, 0x0,
    0xe, 0x70, 0x0, 0x4f, 0x20, 0x0, 0x9c, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x8e, 0xf9, 0x0, 0x7, 0xfa, 0x9f, 0x90,
    0xe, 0xb0, 0x9, 0xf1, 0x3f, 0x60, 0x4, 0xf5,
    0x5f, 0x40, 0x2, 0xf6, 0x5f, 0x30, 0x2, 0xf7,
    0x5f, 0x40, 0x2, 0xf6, 0x3f, 0x60, 0x4, 0xf5,
    0xe, 0xb0, 0x9, 0xf1, 0x8, 0xfb, 0xaf, 0x90,
    0x0, 0x8e, 0xf9, 0x0,

    /* U+0031 "1" */
    0x0, 0x7f, 0x83, 0xdf, 0xf8, 0xac, 0x2f, 0x83,
    0x0, 0xf8, 0x0, 0xf, 0x80, 0x0, 0xf8, 0x0,
    0xf, 0x80, 0x0, 0xf8, 0x0, 0xf, 0x80, 0x0,
    0xf8, 0x0, 0xf, 0x80,

    /* U+0032 "2" */
    0x0, 0x9e, 0xfa, 0x10, 0xb, 0xf9, 0x9f, 0xa0,
    0x1f, 0x50, 0xa, 0xf0, 0x1, 0x0, 0x8, 0xf0,
    0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x5, 0xf9, 0x0, 0x0, 0x3f, 0xb0, 0x0,
    0x2, 0xec, 0x0, 0x0, 0x1d, 0xf8, 0x77, 0x72,
    0x4f, 0xff, 0xff, 0xf6,

    /* U+0033 "3" */
    0x1, 0xae, 0xf9, 0x0, 0xc, 0xf8, 0xaf, 0xa0,
    0x1e, 0x40, 0xb, 0xf0, 0x1, 0x0, 0xa, 0xf0,
    0x0, 0x2, 0x6f, 0x90, 0x0, 0x1f, 0xfe, 0x10,
    0x0, 0x5, 0x7e, 0xe0, 0x0, 0x0, 0x5, 0xf4,
    0x2c, 0x10, 0x6, 0xf4, 0xe, 0xe8, 0x8f, 0xe0,
    0x2, 0xae, 0xfb, 0x20,

    /* U+0034 "4" */
    0x0, 0x1, 0xf9, 0x0, 0x0, 0x8, 0xf1, 0x0,
    0x0, 0x1f, 0x80, 0x0, 0x0, 0x9f, 0x10, 0x0,
    0x2, 0xf7, 0xb, 0x50, 0xa, 0xe0, 0xf, 0x70,
    0x2f, 0x81, 0x1f, 0x81, 0x7f, 0xff, 0xff, 0xff,
    0x24, 0x44, 0x4f, 0xa4, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x0, 0xf, 0x70,

    /* U+0035 "5" */
    0x3, 0xff, 0xff, 0xe0, 0x5, 0xf7, 0x66, 0x50,
    0x7, 0xf0, 0x0, 0x0, 0x8, 0xe0, 0x10, 0x0,
    0xa, 0xff, 0xfc, 0x20, 0xc, 0xd6, 0x7f, 0xd0,
    0x1, 0x10, 0x7, 0xf3, 0x0, 0x0, 0x4, 0xf5,
    0x8, 0x50, 0x8, 0xf2, 0xb, 0xf8, 0x9f, 0xb0,
    0x1, 0xaf, 0xe9, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x8, 0xf2, 0x0,
    0x0, 0x2f, 0x70, 0x0, 0x0, 0xbd, 0x10, 0x0,
    0x4, 0xff, 0xfe, 0x40, 0xc, 0xf7, 0x6d, 0xf1,
    0x2f, 0x80, 0x3, 0xf7, 0x3f, 0x50, 0x1, 0xf8,
    0x1f, 0x90, 0x4, 0xf6, 0xa, 0xfa, 0x8f, 0xe0,
    0x0, 0x9e, 0xfb, 0x20,

    /* U+0037 "7" */
    0x3f, 0xff, 0xff, 0xf5, 0x16, 0x66, 0x6b, 0xf2,
    0x0, 0x0, 0xc, 0xc0, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0xea, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0x1f, 0x80, 0x0, 0x0, 0x7f, 0x20, 0x0,
    0x0, 0xdc, 0x0, 0x0,

    /* U+0038 "8" */
    0x1, 0xae, 0xfb, 0x20, 0xb, 0xf8, 0x7e, 0xd0,
    0xf, 0x90, 0x7, 0xf0, 0xb, 0xe3, 0x3c, 0xd0,
    0x1, 0xef, 0xff, 0x20, 0xb, 0xf7, 0x6e, 0xc0,
    0x3f, 0x60, 0x4, 0xf4, 0x5f, 0x40, 0x2, 0xf7,
    0x3f, 0x70, 0x5, 0xf5, 0xc, 0xf9, 0x8f, 0xd0,
    0x1, 0xae, 0xfb, 0x20,

    /* U+0039 "9" */
    0x0, 0x9e, 0xea, 0x10, 0xc, 0xf9, 0x9f, 0xc0,
    0x3f, 0x70, 0x7, 0xf3, 0x6f, 0x30, 0x3, 0xf6,
    0x5f, 0x50, 0x5, 0xf4, 0x1f, 0xd3, 0x3d, 0xe0,
    0x4, 0xff, 0xff, 0x60, 0x0, 0x14, 0xbd, 0x0,
    0x0, 0x4, 0xf4, 0x0, 0x0, 0xd, 0xa0, 0x0,
    0x0, 0x8f, 0x20, 0x0,

    /* U+003A ":" */
    0xd, 0x80, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x90, 0xd8,

    /* U+003B ";" */
    0xb, 0xa0, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xa0, 0xbe, 0x2, 0xc0, 0xd4, 0x1,
    0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xb6,
    0x0, 0x5, 0xcf, 0xc3, 0x7, 0xef, 0xb4, 0x0,
    0x5f, 0xe3, 0x0, 0x0, 0x19, 0xff, 0x92, 0x0,
    0x0, 0x17, 0xef, 0xb2, 0x0, 0x0, 0x5, 0xc7,
    0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x5f, 0xff, 0xff, 0xf7, 0x15, 0x55, 0x55, 0x52,
    0x1, 0x11, 0x11, 0x10, 0x5f, 0xff, 0xff, 0xf7,
    0x14, 0x44, 0x44, 0x41,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x4c, 0x50, 0x0, 0x0,
    0x2b, 0xfd, 0x60, 0x0, 0x0, 0x3a, 0xfe, 0x81,
    0x0, 0x0, 0x2e, 0xf7, 0x0, 0x18, 0xff, 0xa2,
    0x1a, 0xfe, 0x81, 0x0, 0x5d, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x8e, 0xfc, 0x30, 0xa, 0xf9, 0x8f, 0xe0,
    0xa, 0x50, 0x8, 0xf2, 0x0, 0x0, 0xb, 0xd0,
    0x0, 0x0, 0xae, 0x20, 0x0, 0x8, 0xe2, 0x0,
    0x0, 0xd, 0x80, 0x0, 0x0, 0x8, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x0,
    0x0, 0xb, 0xb0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x7d, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x3d,
    0xd7, 0x44, 0x6c, 0xf4, 0x0, 0x1, 0xe9, 0x0,
    0x10, 0x1, 0x8f, 0x20, 0x9, 0xc0, 0x1c, 0xfe,
    0x7e, 0xb, 0xa0, 0xf, 0x40, 0xbe, 0x67, 0xfe,
    0x4, 0xf0, 0x3f, 0x1, 0xf5, 0x0, 0x9e, 0x1,
    0xf2, 0x4e, 0x3, 0xf3, 0x0, 0x6e, 0x0, 0xf3,
    0x3f, 0x1, 0xf4, 0x0, 0x8e, 0x1, 0xf2, 0x1f,
    0x30, 0xcc, 0x13, 0xef, 0x37, 0xd0, 0xb, 0xa0,
    0x2d, 0xff, 0x5b, 0xff, 0x50, 0x3, 0xf6, 0x0,
    0x21, 0x0, 0x21, 0x0, 0x0, 0x6f, 0xa4, 0x11,
    0x4a, 0x10, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xfb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x1, 0xf8, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0xd, 0xcf, 0x40, 0x0, 0x0,
    0x3f, 0x3e, 0xa0, 0x0, 0x0, 0x9e, 0x9, 0xf1,
    0x0, 0x0, 0xe8, 0x3, 0xf6, 0x0, 0x4, 0xf5,
    0x11, 0xec, 0x0, 0xa, 0xff, 0xff, 0xff, 0x20,
    0xf, 0xa4, 0x44, 0x5f, 0x80, 0x5f, 0x20, 0x0,
    0xc, 0xe0, 0xbc, 0x0, 0x0, 0x5, 0xf4,

    /* U+0042 "B" */
    0xdf, 0xff, 0xd8, 0x0, 0xdc, 0x66, 0xaf, 0xa0,
    0xda, 0x0, 0x9, 0xf0, 0xda, 0x0, 0x8, 0xf1,
    0xda, 0x11, 0x5e, 0xc0, 0xdf, 0xff, 0xff, 0x50,
    0xdb, 0x44, 0x5b, 0xf4, 0xda, 0x0, 0x0, 0xea,
    0xda, 0x0, 0x0, 0xfa, 0xdc, 0x66, 0x7d, 0xf4,
    0xdf, 0xff, 0xfc, 0x50,

    /* U+0043 "C" */
    0x0, 0x8, 0xdf, 0xd6, 0x0, 0x1d, 0xfa, 0x8c,
    0xf7, 0x9, 0xf3, 0x0, 0xa, 0x80, 0xfa, 0x0,
    0x0, 0x0, 0x3f, 0x50, 0x0, 0x0, 0x5, 0xf4,
    0x0, 0x0, 0x0, 0x3f, 0x50, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0xa,
    0x80, 0xd, 0xfa, 0x8c, 0xf7, 0x0, 0x8, 0xdf,
    0xd6, 0x0,

    /* U+0044 "D" */
    0xdf, 0xff, 0xd7, 0x0, 0xd, 0xc6, 0x6a, 0xfd,
    0x10, 0xda, 0x0, 0x3, 0xfb, 0xd, 0xa0, 0x0,
    0x8, 0xf2, 0xda, 0x0, 0x0, 0x3f, 0x5d, 0xa0,
    0x0, 0x2, 0xf6, 0xda, 0x0, 0x0, 0x3f, 0x5d,
    0xa0, 0x0, 0x8, 0xf2, 0xda, 0x0, 0x3, 0xfb,
    0xd, 0xc6, 0x7a, 0xfd, 0x10, 0xdf, 0xff, 0xd7,
    0x0, 0x0,

    /* U+0045 "E" */
    0xdf, 0xff, 0xff, 0x9d, 0xc6, 0x66, 0x64, 0xda,
    0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0xdb, 0x11,
    0x11, 0xd, 0xff, 0xff, 0xf1, 0xdc, 0x44, 0x44,
    0xd, 0xa0, 0x0, 0x0, 0xda, 0x0, 0x0, 0xd,
    0xd7, 0x77, 0x75, 0xdf, 0xff, 0xff, 0xb0,

    /* U+0046 "F" */
    0xdf, 0xff, 0xff, 0x9d, 0xc6, 0x66, 0x64, 0xda,
    0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0xdb, 0x11,
    0x11, 0xd, 0xff, 0xff, 0xf1, 0xdc, 0x44, 0x44,
    0xd, 0xa0, 0x0, 0x0, 0xda, 0x0, 0x0, 0xd,
    0xa0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x7, 0xdf, 0xe8, 0x0, 0x0, 0xcf, 0xa8,
    0xaf, 0xc0, 0x9, 0xf4, 0x0, 0x4, 0x80, 0xf,
    0xa0, 0x0, 0x0, 0x0, 0x3f, 0x50, 0x0, 0x22,
    0x20, 0x5f, 0x40, 0x5, 0xff, 0xf4, 0x3f, 0x50,
    0x1, 0x57, 0xf4, 0xf, 0xa0, 0x0, 0x3, 0xf4,
    0x9, 0xf5, 0x0, 0x4, 0xf4, 0x0, 0xcf, 0xb8,
    0x9f, 0xe1, 0x0, 0x7, 0xdf, 0xea, 0x20,

    /* U+0048 "H" */
    0xda, 0x0, 0x0, 0x1f, 0x6d, 0xa0, 0x0, 0x1,
    0xf6, 0xda, 0x0, 0x0, 0x1f, 0x6d, 0xa0, 0x0,
    0x1, 0xf6, 0xdb, 0x22, 0x22, 0x3f, 0x6d, 0xff,
    0xff, 0xff, 0xf6, 0xdc, 0x55, 0x55, 0x6f, 0x6d,
    0xa0, 0x0, 0x1, 0xf6, 0xda, 0x0, 0x0, 0x1f,
    0x6d, 0xa0, 0x0, 0x1, 0xf6, 0xda, 0x0, 0x0,
    0x1f, 0x60,

    /* U+0049 "I" */
    0xda, 0xda, 0xda, 0xda, 0xda, 0xda, 0xda, 0xda,
    0xda, 0xda, 0xda,

    /* U+004A "J" */
    0x0, 0x0, 0xea, 0x0, 0x0, 0xea, 0x0, 0x0,
    0xea, 0x0, 0x0, 0xea, 0x0, 0x0, 0xea, 0x0,
    0x0, 0xea, 0x0, 0x0, 0xea, 0x0, 0x0, 0xe9,
    0x64, 0x1, 0xf8, 0xce, 0x8c, 0xf3, 0x2b, 0xfd,
    0x50,

    /* U+004B "K" */
    0xda, 0x0, 0x1, 0xed, 0x1d, 0xa0, 0x0, 0xce,
    0x20, 0xda, 0x0, 0xaf, 0x30, 0xd, 0xa0, 0x8f,
    0x40, 0x0, 0xda, 0x6f, 0x80, 0x0, 0xd, 0xdf,
    0xfd, 0x0, 0x0, 0xdf, 0xa5, 0xf9, 0x0, 0xd,
    0xc0, 0xa, 0xf4, 0x0, 0xda, 0x0, 0xe, 0xe1,
    0xd, 0xa0, 0x0, 0x4f, 0xa0, 0xda, 0x0, 0x0,
    0x8f, 0x60,

    /* U+004C "L" */
    0xda, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0xda,
    0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0xd, 0xa0, 0x0, 0x0, 0xda, 0x0, 0x0,
    0xd, 0xa0, 0x0, 0x0, 0xda, 0x0, 0x0, 0xd,
    0xd7, 0x77, 0x74, 0xdf, 0xff, 0xff, 0x90,

    /* U+004D "M" */
    0xdb, 0x0, 0x0, 0x0, 0x8f, 0xdf, 0x50, 0x0,
    0x1, 0xff, 0xdf, 0xd0, 0x0, 0xa, 0xff, 0xdb,
    0xf7, 0x0, 0x4f, 0xbf, 0xd9, 0x8f, 0x10, 0xda,
    0x7f, 0xd9, 0xd, 0xa6, 0xf1, 0x7f, 0xd9, 0x5,
    0xff, 0x70, 0x7f, 0xd9, 0x0, 0xbd, 0x0, 0x7f,
    0xd9, 0x0, 0x1, 0x0, 0x7f, 0xd9, 0x0, 0x0,
    0x0, 0x7f, 0xd9, 0x0, 0x0, 0x0, 0x7f,

    /* U+004E "N" */
    0xdb, 0x0, 0x0, 0x2f, 0x4d, 0xf6, 0x0, 0x2,
    0xf4, 0xdf, 0xf2, 0x0, 0x2f, 0x4d, 0xae, 0xb0,
    0x2, 0xf4, 0xd9, 0x4f, 0x60, 0x2f, 0x4d, 0x90,
    0xaf, 0x22, 0xf4, 0xd9, 0x1, 0xec, 0x2f, 0x4d,
    0x90, 0x4, 0xf8, 0xf4, 0xd9, 0x0, 0xa, 0xff,
    0x4d, 0x90, 0x0, 0x1e, 0xf4, 0xd9, 0x0, 0x0,
    0x4f, 0x40,

    /* U+004F "O" */
    0x0, 0x18, 0xef, 0xd7, 0x0, 0x0, 0x1d, 0xfa,
    0x8b, 0xfa, 0x0, 0xa, 0xf4, 0x0, 0x7, 0xf6,
    0x1, 0xfa, 0x0, 0x0, 0xd, 0xc0, 0x3f, 0x50,
    0x0, 0x0, 0x9f, 0x5, 0xf4, 0x0, 0x0, 0x8,
    0xf1, 0x3f, 0x50, 0x0, 0x0, 0x9f, 0x1, 0xfa,
    0x0, 0x0, 0xd, 0xc0, 0xa, 0xf3, 0x0, 0x7,
    0xf6, 0x0, 0x1d, 0xfa, 0x8b, 0xfa, 0x0, 0x0,
    0x19, 0xef, 0xd6, 0x0, 0x0,

    /* U+0050 "P" */
    0xdf, 0xff, 0xd7, 0x0, 0xdc, 0x66, 0xbf, 0x80,
    0xda, 0x0, 0xb, 0xf0, 0xda, 0x0, 0x7, 0xf2,
    0xda, 0x0, 0x9, 0xf1, 0xdb, 0x12, 0x6f, 0xb0,
    0xdf, 0xff, 0xfb, 0x10, 0xdc, 0x44, 0x20, 0x0,
    0xda, 0x0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x0,
    0xda, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x18, 0xef, 0xd7, 0x0, 0x0, 0x1d, 0xfa,
    0x8b, 0xfa, 0x0, 0xa, 0xf4, 0x0, 0x7, 0xf6,
    0x1, 0xfa, 0x0, 0x0, 0xd, 0xc0, 0x3f, 0x50,
    0x0, 0x0, 0x9f, 0x5, 0xf4, 0x0, 0x0, 0x8,
    0xf1, 0x3f, 0x50, 0x0, 0x0, 0x9f, 0x1, 0xfa,
    0x0, 0x0, 0xd, 0xc0, 0xa, 0xf3, 0x0, 0x7,
    0xf6, 0x0, 0x1d, 0xfa, 0x8b, 0xfa, 0x0, 0x0,
    0x19, 0xef, 0xff, 0x20, 0x0, 0x0, 0x0, 0x3,
    0xee, 0x30, 0x0, 0x0, 0x0, 0x1, 0xcf, 0x60,

    /* U+0052 "R" */
    0xdf, 0xff, 0xd8, 0x0, 0xd, 0xc6, 0x6a, 0xfa,
    0x0, 0xda, 0x0, 0x9, 0xf1, 0xd, 0xa0, 0x0,
    0x7f, 0x10, 0xdb, 0x12, 0x5e, 0xc0, 0xd, 0xff,
    0xff, 0xc2, 0x0, 0xdc, 0x47, 0xf6, 0x0, 0xd,
    0xa0, 0xb, 0xf1, 0x0, 0xda, 0x0, 0x2f, 0xa0,
    0xd, 0xa0, 0x0, 0x8f, 0x40, 0xda, 0x0, 0x0,
    0xed, 0x0,

    /* U+0053 "S" */
    0x2, 0xae, 0xfc, 0x40, 0xe, 0xe8, 0x8d, 0xf3,
    0x4f, 0x50, 0x0, 0xc4, 0x4f, 0x60, 0x0, 0x0,
    0xc, 0xfa, 0x40, 0x0, 0x0, 0x7d, 0xfe, 0x70,
    0x0, 0x0, 0x2b, 0xf6, 0x2, 0x0, 0x0, 0xeb,
    0x9e, 0x10, 0x0, 0xea, 0x2f, 0xe9, 0x8c, 0xf4,
    0x2, 0xae, 0xfc, 0x50,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xff, 0x56, 0x6d, 0xe6, 0x66,
    0x0, 0xb, 0xd0, 0x0, 0x0, 0xb, 0xd0, 0x0,
    0x0, 0xb, 0xd0, 0x0, 0x0, 0xb, 0xd0, 0x0,
    0x0, 0xb, 0xd0, 0x0, 0x0, 0xb, 0xd0, 0x0,
    0x0, 0xb, 0xd0, 0x0, 0x0, 0xb, 0xd0, 0x0,
    0x0, 0xb, 0xd0, 0x0,

    /* U+0055 "U" */
    0xf9, 0x0, 0x0, 0x3f, 0x4f, 0x90, 0x0, 0x3,
    0xf4, 0xf9, 0x0, 0x0, 0x3f, 0x4f, 0x90, 0x0,
    0x3, 0xf4, 0xf9, 0x0, 0x0, 0x3f, 0x4f, 0x90,
    0x0, 0x3, 0xf4, 0xe9, 0x0, 0x0, 0x4f, 0x4d,
    0xc0, 0x0, 0x6, 0xf2, 0x9f, 0x30, 0x0, 0xde,
    0x1, 0xef, 0x98, 0xdf, 0x50, 0x2, 0xae, 0xfc,
    0x50, 0x0,

    /* U+0056 "V" */
    0xbe, 0x0, 0x0, 0x5, 0xf3, 0x5f, 0x40, 0x0,
    0xa, 0xd0, 0xf, 0xa0, 0x0, 0xf, 0x70, 0x9,
    0xf0, 0x0, 0x5f, 0x20, 0x3, 0xf5, 0x0, 0xac,
    0x0, 0x0, 0xdb, 0x0, 0xf6, 0x0, 0x0, 0x7f,
    0x15, 0xf1, 0x0, 0x0, 0x1f, 0x6b, 0xb0, 0x0,
    0x0, 0xb, 0xcf, 0x50, 0x0, 0x0, 0x5, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,

    /* U+0057 "W" */
    0xbd, 0x0, 0x0, 0xdb, 0x0, 0x0, 0xf7, 0x7f,
    0x10, 0x1, 0xff, 0x0, 0x4, 0xf2, 0x2f, 0x60,
    0x5, 0xff, 0x40, 0x8, 0xe0, 0xd, 0xb0, 0xa,
    0xbe, 0x80, 0xc, 0x90, 0x8, 0xf0, 0xe, 0x6a,
    0xd0, 0xf, 0x40, 0x3, 0xf4, 0x2f, 0x25, 0xf1,
    0x5f, 0x0, 0x0, 0xe8, 0x6d, 0x1, 0xf6, 0x9b,
    0x0, 0x0, 0xad, 0xb9, 0x0, 0xcb, 0xd7, 0x0,
    0x0, 0x5f, 0xf4, 0x0, 0x7f, 0xf2, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0xb,
    0xb0, 0x0, 0xd, 0x90, 0x0,

    /* U+0058 "X" */
    0x6f, 0x60, 0x0, 0xd, 0xd0, 0xb, 0xf1, 0x0,
    0x8f, 0x30, 0x2, 0xfb, 0x2, 0xf8, 0x0, 0x0,
    0x6f, 0x6c, 0xd0, 0x0, 0x0, 0xb, 0xff, 0x30,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x50, 0x0, 0x0, 0x8f, 0x3c, 0xe1, 0x0,
    0x3, 0xf8, 0x2, 0xfa, 0x0, 0xd, 0xd0, 0x0,
    0x8f, 0x50, 0x8f, 0x30, 0x0, 0xd, 0xe1,

    /* U+0059 "Y" */
    0x9f, 0x20, 0x0, 0x1f, 0x81, 0xfb, 0x0, 0x9,
    0xe1, 0x7, 0xf4, 0x2, 0xf7, 0x0, 0xd, 0xd0,
    0xad, 0x0, 0x0, 0x5f, 0x9f, 0x50, 0x0, 0x0,
    0xbf, 0xc0, 0x0, 0x0, 0x4, 0xf5, 0x0, 0x0,
    0x0, 0x3f, 0x40, 0x0, 0x0, 0x3, 0xf4, 0x0,
    0x0, 0x0, 0x3f, 0x40, 0x0, 0x0, 0x3, 0xf4,
    0x0, 0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xff, 0xfa, 0x16, 0x66, 0x6c, 0xf3,
    0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x4, 0xf6, 0x0, 0x0, 0xd, 0xd0, 0x0,
    0x0, 0x7f, 0x30, 0x0, 0x2, 0xf9, 0x0, 0x0,
    0xb, 0xe1, 0x0, 0x0, 0x4f, 0xc7, 0x77, 0x76,
    0xaf, 0xff, 0xff, 0xfd,

    /* U+005B "[" */
    0x11, 0x11, 0xdf, 0xfb, 0xdb, 0x43, 0xd9, 0x0,
    0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0,
    0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0,
    0xd9, 0x0, 0xda, 0x21, 0xdf, 0xfb, 0x34, 0x43,

    /* U+005C "\\" */
    0xac, 0x0, 0x0, 0x4f, 0x20, 0x0, 0xe, 0x70,
    0x0, 0x9, 0xd0, 0x0, 0x3, 0xf2, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x3f, 0x30,
    0x0, 0xd, 0x90, 0x0, 0x7, 0xe0, 0x0, 0x2,
    0xf4,

    /* U+005D "]" */
    0x11, 0x11, 0xbf, 0xfc, 0x34, 0xbc, 0x0, 0xac,
    0x0, 0xac, 0x0, 0xac, 0x0, 0xac, 0x0, 0xac,
    0x0, 0xac, 0x0, 0xac, 0x0, 0xac, 0x0, 0xac,
    0x0, 0xac, 0x12, 0xac, 0xbf, 0xfc, 0x34, 0x43,

    /* U+005E "^" */
    0x0, 0x6f, 0x40, 0x0, 0xd, 0xfa, 0x0, 0x3,
    0xf8, 0xf1, 0x0, 0x9c, 0xe, 0x70, 0xf, 0x60,
    0x8d, 0x6, 0xf0, 0x2, 0xf4,

    /* U+005F "_" */
    0x11, 0x11, 0x11, 0xf, 0xff, 0xff, 0xf1, 0x33,
    0x33, 0x33, 0x0,

    /* U+0060 "`" */
    0x7, 0x40, 0x8, 0xd0, 0x0, 0xe5,

    /* U+0061 "a" */
    0x1, 0xaf, 0xe8, 0x0, 0xcd, 0x68, 0xf6, 0x1,
    0x10, 0xb, 0xb0, 0x3c, 0xfe, 0xec, 0x1f, 0xb3,
    0x4c, 0xd4, 0xf3, 0x0, 0xbd, 0x2f, 0x91, 0x6f,
    0xd0, 0x4d, 0xfb, 0x9d,

    /* U+0062 "b" */
    0xf7, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0xf, 0x8a, 0xfd, 0x50, 0xff, 0xc7,
    0xcf, 0x4f, 0xd0, 0x0, 0xdc, 0xf8, 0x0, 0x9,
    0xff, 0x80, 0x0, 0x9f, 0xfc, 0x0, 0xd, 0xcf,
    0xeb, 0x7b, 0xf4, 0xf6, 0xaf, 0xd6, 0x0,

    /* U+0063 "c" */
    0x0, 0x8e, 0xfa, 0x10, 0xaf, 0x98, 0xe9, 0x2f,
    0x70, 0x2, 0x5, 0xf2, 0x0, 0x0, 0x5f, 0x20,
    0x0, 0x2, 0xf7, 0x0, 0x20, 0xa, 0xf9, 0x8e,
    0x90, 0x8, 0xef, 0xa1,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xd9, 0x0, 0x0, 0x0, 0xd9,
    0x0, 0x0, 0x0, 0xd9, 0x0, 0xaf, 0xe5, 0xd9,
    0xb, 0xf9, 0x8e, 0xf9, 0x2f, 0x70, 0x3, 0xf9,
    0x5f, 0x20, 0x0, 0xe9, 0x5f, 0x20, 0x0, 0xe9,
    0x2f, 0x50, 0x2, 0xf9, 0xb, 0xe5, 0x5d, 0xf9,
    0x0, 0xaf, 0xe7, 0xb9,

    /* U+0065 "e" */
    0x0, 0x9e, 0xfa, 0x10, 0xa, 0xe7, 0x7e, 0xc0,
    0x2f, 0x50, 0x5, 0xf2, 0x5f, 0xff, 0xff, 0xf4,
    0x5f, 0x53, 0x33, 0x30, 0x2f, 0x70, 0x1, 0x10,
    0xa, 0xf8, 0x6d, 0xc0, 0x0, 0x8e, 0xfb, 0x20,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf5, 0x3, 0xf8,
    0x61, 0x6, 0xf0, 0x0, 0x8, 0xe0, 0x0, 0xcf,
    0xff, 0xd0, 0x3a, 0xf5, 0x40, 0x8, 0xe0, 0x0,
    0x8, 0xe0, 0x0, 0x8, 0xe0, 0x0, 0x8, 0xe0,
    0x0, 0x8, 0xe0, 0x0, 0x8, 0xe0, 0x0,

    /* U+0067 "g" */
    0x0, 0x9f, 0xe6, 0xb9, 0xb, 0xf9, 0x8e, 0xe9,
    0x2f, 0x60, 0x3, 0xf9, 0x5f, 0x20, 0x0, 0xe9,
    0x5f, 0x20, 0x0, 0xe9, 0x2f, 0x60, 0x3, 0xf9,
    0xa, 0xf8, 0x7e, 0xf9, 0x0, 0x9f, 0xe6, 0xe8,
    0x1, 0x10, 0x2, 0xf6, 0xe, 0xd8, 0x7d, 0xe0,
    0x2, 0xbf, 0xea, 0x10,

    /* U+0068 "h" */
    0xf7, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0xf, 0x8a, 0xfd, 0x30, 0xff, 0xb7,
    0xee, 0xf, 0xc0, 0x5, 0xf3, 0xf8, 0x0, 0x2f,
    0x5f, 0x70, 0x2, 0xf5, 0xf7, 0x0, 0x2f, 0x5f,
    0x70, 0x2, 0xf5, 0xf7, 0x0, 0x2f, 0x50,

    /* U+0069 "i" */
    0xe, 0x70, 0xe7, 0x0, 0x0, 0xf7, 0xf, 0x70,
    0xf7, 0xf, 0x70, 0xf7, 0xf, 0x70, 0xf7, 0xf,
    0x70,

    /* U+006A "j" */
    0x0, 0x1e, 0x60, 0x0, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0xf7, 0x0, 0xf, 0x70, 0x0, 0xf7, 0x0,
    0xf, 0x70, 0x0, 0xf7, 0x0, 0xf, 0x70, 0x0,
    0xf7, 0x0, 0xf, 0x70, 0x1, 0xf6, 0x38, 0xaf,
    0x26, 0xee, 0x60,

    /* U+006B "k" */
    0xf7, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0xf, 0x70, 0xc, 0xd1, 0xf7, 0x9,
    0xf2, 0xf, 0x75, 0xf4, 0x0, 0xfa, 0xfd, 0x0,
    0xf, 0xfc, 0xf6, 0x0, 0xfc, 0xb, 0xf1, 0xf,
    0x70, 0x1f, 0xa0, 0xf7, 0x0, 0x7f, 0x40,

    /* U+006C "l" */
    0xf7, 0xf7, 0xf7, 0xf7, 0xf7, 0xf7, 0xf7, 0xf7,
    0xf7, 0xf7, 0xf7,

    /* U+006D "m" */
    0xf7, 0xcf, 0xb1, 0x9f, 0xd2, 0xf, 0xf7, 0x6f,
    0xea, 0x5d, 0xc0, 0xfa, 0x0, 0xcf, 0x0, 0x6f,
    0xf, 0x70, 0xa, 0xd0, 0x4, 0xf1, 0xf7, 0x0,
    0xac, 0x0, 0x4f, 0x2f, 0x70, 0xa, 0xc0, 0x4,
    0xf2, 0xf7, 0x0, 0xac, 0x0, 0x4f, 0x2f, 0x70,
    0xa, 0xc0, 0x4, 0xf2,

    /* U+006E "n" */
    0xf6, 0xbf, 0xd4, 0xf, 0xf8, 0x4c, 0xe0, 0xfb,
    0x0, 0x4f, 0x3f, 0x70, 0x2, 0xf5, 0xf7, 0x0,
    0x2f, 0x5f, 0x70, 0x2, 0xf5, 0xf7, 0x0, 0x2f,
    0x5f, 0x70, 0x2, 0xf5,

    /* U+006F "o" */
    0x0, 0x8e, 0xfa, 0x10, 0x9, 0xf9, 0x7e, 0xd0,
    0x2f, 0x70, 0x2, 0xf7, 0x5f, 0x20, 0x0, 0xda,
    0x5f, 0x20, 0x0, 0xda, 0x2f, 0x70, 0x2, 0xf7,
    0x9, 0xf9, 0x7e, 0xd0, 0x0, 0x8e, 0xfa, 0x10,

    /* U+0070 "p" */
    0xf6, 0xaf, 0xd5, 0xf, 0xf9, 0x49, 0xf4, 0xfc,
    0x0, 0xc, 0xcf, 0x80, 0x0, 0x8f, 0xf8, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0xdc, 0xff, 0xc7, 0xcf,
    0x4f, 0x8a, 0xfd, 0x60, 0xf7, 0x0, 0x0, 0xf,
    0x70, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0xaf, 0xe6, 0xb9, 0xb, 0xf8, 0x7e, 0xe9,
    0x2f, 0x60, 0x3, 0xf9, 0x5f, 0x20, 0x0, 0xe9,
    0x5f, 0x20, 0x0, 0xe9, 0x2f, 0x60, 0x3, 0xf9,
    0xb, 0xf8, 0x8e, 0xf9, 0x0, 0xaf, 0xe6, 0xd9,
    0x0, 0x0, 0x0, 0xd9, 0x0, 0x0, 0x0, 0xd9,
    0x0, 0x0, 0x0, 0xd9,

    /* U+0072 "r" */
    0xf7, 0xcf, 0x4f, 0xf8, 0x40, 0xfb, 0x0, 0xf,
    0x80, 0x0, 0xf7, 0x0, 0xf, 0x70, 0x0, 0xf7,
    0x0, 0xf, 0x70, 0x0,

    /* U+0073 "s" */
    0x5, 0xdf, 0xc2, 0x3, 0xfa, 0x6d, 0xc0, 0x5f,
    0x30, 0x11, 0x1, 0xdf, 0xa5, 0x0, 0x0, 0x5a,
    0xfa, 0x1, 0x40, 0x8, 0xf0, 0x7f, 0x76, 0xdd,
    0x0, 0x8e, 0xfc, 0x20,

    /* U+0074 "t" */
    0x5, 0x60, 0x0, 0xa, 0xc0, 0x0, 0xa, 0xc0,
    0x0, 0xef, 0xff, 0xe0, 0x4c, 0xd5, 0x40, 0xa,
    0xc0, 0x0, 0xa, 0xc0, 0x0, 0xa, 0xc0, 0x0,
    0x9, 0xd0, 0x0, 0x6, 0xf8, 0x80, 0x0, 0xaf,
    0xd2,

    /* U+0075 "u" */
    0x1f, 0x50, 0x3, 0xf3, 0x1f, 0x50, 0x3, 0xf3,
    0x1f, 0x50, 0x3, 0xf3, 0x1f, 0x50, 0x3, 0xf3,
    0x1f, 0x60, 0x4, 0xf3, 0xf, 0x80, 0x7, 0xf3,
    0xb, 0xe5, 0x6e, 0xf3, 0x2, 0xbf, 0xc5, 0xf3,

    /* U+0076 "v" */
    0xbe, 0x0, 0x5, 0xf2, 0x5f, 0x40, 0xa, 0xc0,
    0xe, 0x90, 0xf, 0x60, 0x8, 0xf0, 0x5f, 0x10,
    0x2, 0xf5, 0xaa, 0x0, 0x0, 0xcb, 0xf4, 0x0,
    0x0, 0x6f, 0xe0, 0x0, 0x0, 0xf, 0x80, 0x0,

    /* U+0077 "w" */
    0xbc, 0x0, 0x1f, 0x50, 0x8, 0xd6, 0xf1, 0x6,
    0xfa, 0x0, 0xd7, 0x1f, 0x60, 0xbe, 0xf0, 0x1f,
    0x20, 0xbb, 0xf, 0x5f, 0x46, 0xd0, 0x5, 0xf4,
    0xe0, 0xb9, 0xb8, 0x0, 0xf, 0xd9, 0x6, 0xef,
    0x30, 0x0, 0xbf, 0x40, 0x1f, 0xe0, 0x0, 0x5,
    0xf0, 0x0, 0xb9, 0x0,

    /* U+0078 "x" */
    0x8f, 0x30, 0xd, 0xb0, 0xd, 0xd0, 0x9e, 0x10,
    0x3, 0xfa, 0xf5, 0x0, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0x8f, 0xb0, 0x0, 0x3, 0xf9, 0xf6, 0x0,
    0xd, 0xb0, 0xae, 0x10, 0x8f, 0x20, 0x1e, 0xb0,

    /* U+0079 "y" */
    0xbe, 0x0, 0x4, 0xf3, 0x5f, 0x40, 0xa, 0xc0,
    0xe, 0xa0, 0xf, 0x60, 0x8, 0xf0, 0x5f, 0x10,
    0x1, 0xf6, 0xba, 0x0, 0x0, 0xbd, 0xf4, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0xf, 0x70, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0x57, 0xea, 0x0, 0x0,
    0xaf, 0xc1, 0x0, 0x0,

    /* U+007A "z" */
    0x5f, 0xff, 0xff, 0x11, 0x44, 0x5f, 0xa0, 0x0,
    0xa, 0xe1, 0x0, 0x4, 0xf6, 0x0, 0x0, 0xdc,
    0x0, 0x0, 0x8f, 0x20, 0x0, 0x2f, 0xb4, 0x44,
    0x18, 0xff, 0xff, 0xf4,

    /* U+007B "{" */
    0x0, 0x0, 0x10, 0x0, 0x4e, 0xf1, 0x0, 0xde,
    0x50, 0x0, 0xf8, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0xf8, 0x0, 0x2, 0xf6, 0x0, 0x5e, 0xe1, 0x0,
    0x7f, 0xa0, 0x0, 0x5, 0xf5, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0xf8, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0xdc, 0x20, 0x0, 0x6f, 0xf1, 0x0, 0x1, 0x40,

    /* U+007C "|" */
    0x4f, 0x4, 0xf1, 0x4f, 0x14, 0xf1, 0x4f, 0x14,
    0xf1, 0x4f, 0x14, 0xf1, 0x4f, 0x14, 0xf1, 0x4f,
    0x14, 0xf1, 0x4f, 0x14, 0xf1,

    /* U+007D "}" */
    0x10, 0x0, 0xb, 0xf8, 0x0, 0x3a, 0xf2, 0x0,
    0x3f, 0x40, 0x2, 0xf5, 0x0, 0x2f, 0x50, 0x1,
    0xf7, 0x0, 0xa, 0xf9, 0x0, 0x6f, 0xd0, 0xf,
    0xa1, 0x2, 0xf5, 0x0, 0x2f, 0x50, 0x2, 0xf5,
    0x1, 0x8f, 0x30, 0xbf, 0xb0, 0x3, 0x30, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x10, 0x5, 0xfd, 0x10, 0xf4,
    0xe, 0x9b, 0xa5, 0xf1, 0x2f, 0x11, 0xef, 0xa0,
    0x14, 0x0, 0x13, 0x0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xea, 0x0, 0x0, 0x0, 0x4, 0x55, 0x55,
    0xab, 0x55, 0x55, 0x30, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0, 0x0,
    0x0, 0x1, 0x44, 0x44, 0xea, 0x44, 0x44, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0x0, 0x0, 0x0, 0x24, 0x44, 0x44, 0xea,
    0x44, 0x44, 0x40, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1,

    /* U+4EF6 "件" */
    0x0, 0xc, 0x30, 0x0, 0xc9, 0x0, 0x0, 0x0,
    0x5f, 0x17, 0xc0, 0xc9, 0x0, 0x0, 0x0, 0xbb,
    0xb, 0x90, 0xc9, 0x0, 0x0, 0x2, 0xf6, 0xf,
    0xff, 0xff, 0xff, 0xa0, 0x9, 0xf5, 0x5f, 0x55,
    0xdb, 0x55, 0x30, 0x2f, 0xf5, 0xbb, 0x0, 0xc9,
    0x0, 0x0, 0xad, 0xf5, 0x64, 0x0, 0xc9, 0x0,
    0x0, 0x73, 0xe5, 0x45, 0x55, 0xdb, 0x55, 0x50,
    0x0, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0xe5, 0x0, 0x0, 0xc9, 0x0, 0x0, 0x0, 0xe5,
    0x0, 0x0, 0xc9, 0x0, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0xc9, 0x0, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0xc9, 0x0, 0x0,

    /* U+5165 "入" */
    0x0, 0xb, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x66, 0xda, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xc, 0x9a, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x24, 0xf3, 0x0,
    0x0, 0x0, 0x1, 0xe8, 0x0, 0xbd, 0x0, 0x0,
    0x0, 0xc, 0xd0, 0x0, 0x2f, 0xa0, 0x0, 0x1,
    0xce, 0x20, 0x0, 0x4, 0xfa, 0x0, 0x2d, 0xe3,
    0x0, 0x0, 0x0, 0x5f, 0xc2, 0x3d, 0x20, 0x0,
    0x0, 0x0, 0x3, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+5199 "写" */
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x9b,
    0x46, 0x33, 0x33, 0x33, 0xbb, 0x9, 0xa7, 0xe0,
    0x0, 0x0, 0x9, 0xb0, 0x11, 0x9f, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0xb, 0xa2, 0x22, 0x22, 0x20,
    0x0, 0x0, 0xda, 0x44, 0x44, 0x44, 0x40, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf0, 0x1f, 0xff, 0xff, 0xff,
    0xf8, 0x5f, 0x0, 0x44, 0x44, 0x44, 0x44, 0x27,
    0xd0, 0x0, 0x0, 0x0, 0x4, 0x33, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x30,

    /* U+52A1 "务" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0xdd, 0xdd, 0xdb, 0x0, 0x0, 0x7f, 0xf5,
    0x44, 0x49, 0xf9, 0x0, 0x9, 0xf5, 0xbd, 0x31,
    0x9f, 0x70, 0x0, 0x3, 0x20, 0x9, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x26, 0xbf, 0xdb, 0xfd, 0x95,
    0x10, 0x1e, 0xfe, 0x97, 0x60, 0x16, 0xbf, 0xf7,
    0x6, 0x41, 0x19, 0xd1, 0x11, 0x11, 0x31, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x11,
    0x3f, 0x61, 0x11, 0x7e, 0x0, 0x0, 0x1, 0xdc,
    0x0, 0x0, 0x9c, 0x0, 0x2, 0x8f, 0xb1, 0x4,
    0x22, 0xe9, 0x0, 0x9, 0xd5, 0x0, 0xb, 0xef,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+53C2 "参" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xae, 0x30, 0x9b, 0x10, 0x0, 0x0, 0x5e,
    0xc3, 0x33, 0x5e, 0xe4, 0x0, 0x0, 0xee, 0xdd,
    0xfc, 0xaa, 0xaf, 0x30, 0x3, 0x33, 0x3b, 0xd3,
    0x33, 0x35, 0x30, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x8, 0xf4, 0x2, 0x6f, 0x60,
    0x0, 0x2, 0xbe, 0x66, 0xbf, 0x54, 0xfb, 0x20,
    0x5f, 0xab, 0xfb, 0x62, 0x7d, 0x3b, 0xf5, 0x3,
    0x1, 0x37, 0xcf, 0xa4, 0x13, 0x30, 0x0, 0x8f,
    0xd9, 0x51, 0x39, 0xfb, 0x0, 0x0, 0x11, 0x25,
    0x9e, 0xe9, 0x30, 0x0, 0x4, 0xcf, 0xfc, 0x84,
    0x0, 0x0, 0x0, 0x1, 0x52, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+542F "启" */
    0x0, 0x0, 0x0, 0x68, 0x0, 0x0, 0x0, 0x3,
    0x44, 0x49, 0xf4, 0x44, 0x42, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xe, 0x60, 0x0, 0x0,
    0x0, 0xd8, 0x0, 0xe8, 0x44, 0x44, 0x44, 0x4e,
    0x80, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x55,
    0x55, 0x55, 0x55, 0x51, 0x5, 0xf5, 0xfe, 0xee,
    0xee, 0xef, 0x30, 0xad, 0x3f, 0x10, 0x0, 0x2,
    0xf3, 0x2f, 0x73, 0xf1, 0x0, 0x0, 0x2f, 0x3a,
    0xe0, 0x3f, 0xff, 0xff, 0xff, 0xf3, 0x13, 0x3,
    0xf4, 0x33, 0x33, 0x5f, 0x30,

    /* U+5668 "器" */
    0x6, 0xff, 0xff, 0x57, 0xff, 0xff, 0x50, 0x6,
    0xb0, 0xd, 0x57, 0xb0, 0xc, 0x50, 0x6, 0xfe,
    0xef, 0x57, 0xff, 0xff, 0x50, 0x0, 0x22, 0x22,
    0x73, 0x26, 0x82, 0x0, 0x3, 0x33, 0x34, 0xf8,
    0x38, 0xe3, 0x30, 0xe, 0xee, 0xef, 0xff, 0xfe,
    0xee, 0xd0, 0x0, 0x38, 0xeb, 0x11, 0xbd, 0x62,
    0x0, 0x5f, 0xfd, 0x73, 0x11, 0x37, 0xdf, 0xe4,
    0x18, 0xfe, 0xef, 0x67, 0xfe, 0xef, 0x70, 0x4,
    0xd0, 0xc, 0x67, 0xb0, 0xe, 0x40, 0x4, 0xfe,
    0xef, 0x67, 0xfe, 0xef, 0x40, 0x4, 0xd2, 0x2d,
    0x67, 0xb2, 0x2e, 0x40,

    /* U+56FA "固" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xe7, 0x22,
    0x2d, 0x62, 0x22, 0xe5, 0xe5, 0x33, 0x3f, 0x73,
    0x31, 0xe5, 0xe5, 0xef, 0xff, 0xff, 0xf8, 0xe5,
    0xe5, 0x0, 0xe, 0x50, 0x0, 0xe5, 0xe5, 0xf,
    0xff, 0xff, 0xc0, 0xe5, 0xe5, 0x1f, 0x21, 0x15,
    0xd0, 0xe5, 0xe5, 0x1f, 0x32, 0x26, 0xd0, 0xe5,
    0xe5, 0xe, 0xee, 0xee, 0xb0, 0xe5, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0xe5, 0xef, 0xee, 0xee, 0xee,
    0xee, 0xf5, 0xe7, 0x33, 0x33, 0x33, 0x33, 0xf5,

    /* U+5730 "地" */
    0x0, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,
    0xe5, 0x0, 0x20, 0xe4, 0x0, 0x0, 0x0, 0xe5,
    0x5, 0xe0, 0xe4, 0x0, 0x20, 0x0, 0xe5, 0x5,
    0xe0, 0xea, 0xbf, 0x80, 0x6f, 0xff, 0x85, 0xfc,
    0xfd, 0x7d, 0x70, 0x24, 0xe8, 0x8f, 0xf8, 0xf4,
    0xc, 0x70, 0x0, 0xe5, 0x28, 0xe0, 0xe4, 0xd,
    0x60, 0x0, 0xe5, 0x5, 0xe0, 0xe4, 0x1f, 0x40,
    0x0, 0xe5, 0x5, 0xe0, 0xe6, 0xff, 0x10, 0x0,
    0xeb, 0xa5, 0xe0, 0xe4, 0x22, 0xc1, 0x4c, 0xfb,
    0x44, 0xe0, 0x0, 0x2, 0xf1, 0x7a, 0x30, 0x3,
    0xf6, 0x43, 0x4a, 0xf0, 0x0, 0x0, 0x0, 0xae,
    0xff, 0xfd, 0x70,

    /* U+5740 "址" */
    0x0, 0xd5, 0x0, 0x0, 0x3f, 0x10, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0x3f, 0x10, 0x0, 0x0, 0xe6,
    0x1, 0x30, 0x3f, 0x10, 0x0, 0x37, 0xfa, 0x66,
    0xf0, 0x3f, 0x10, 0x0, 0x5d, 0xfe, 0xc6, 0xf0,
    0x3f, 0xff, 0xa0, 0x0, 0xe6, 0x6, 0xf0, 0x3f,
    0x76, 0x40, 0x0, 0xe6, 0x6, 0xf0, 0x3f, 0x10,
    0x0, 0x0, 0xe6, 0x6, 0xf0, 0x3f, 0x10, 0x0,
    0x0, 0xe6, 0x16, 0xf0, 0x3f, 0x10, 0x0, 0x1,
    0xfe, 0xf6, 0xf0, 0x3f, 0x10, 0x0, 0x9f, 0xe9,
    0x46, 0xf0, 0x3f, 0x10, 0x0, 0x44, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x55, 0x55,
    0x55, 0x55, 0x50,

    /* U+59CB "始" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x1,
    0xf3, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x6, 0xf1, 0x0, 0x0, 0x26, 0xf4, 0x30,
    0xd, 0x90, 0xc7, 0x0, 0xaf, 0xff, 0xf1, 0x7f,
    0x10, 0x7e, 0x10, 0x8, 0xb3, 0xf3, 0xfa, 0x57,
    0x9f, 0x80, 0xb, 0x95, 0xe7, 0xfe, 0xca, 0x9a,
    0xe0, 0xe, 0x67, 0xc0, 0x0, 0x0, 0x0, 0x20,
    0x1f, 0x3b, 0x80, 0xef, 0xff, 0xff, 0x50, 0x1d,
    0xdf, 0x30, 0xe8, 0x44, 0x4f, 0x60, 0x1, 0xcf,
    0x30, 0xe6, 0x0, 0xe, 0x60, 0x1, 0xfd, 0xe3,
    0xe6, 0x0, 0xe, 0x60, 0x1d, 0xb0, 0xa4, 0xef,
    0xff, 0xff, 0x60, 0x5d, 0x10, 0x0, 0xe8, 0x44,
    0x4f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5DF2 "已" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x2, 0x55,
    0x55, 0x55, 0x55, 0x9e, 0x0, 0x3, 0x0, 0x0,
    0x0, 0x7, 0xe0, 0x3, 0xf3, 0x0, 0x0, 0x0,
    0x7e, 0x0, 0x3f, 0x75, 0x55, 0x55, 0x5a, 0xe0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x3f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3, 0xf3, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x3f, 0x30, 0x0, 0x0,
    0x0, 0x5f, 0x3, 0xf3, 0x0, 0x0, 0x0, 0x6,
    0xf0, 0x1f, 0xb5, 0x54, 0x44, 0x56, 0xdd, 0x0,
    0x8e, 0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+5F00 "开" */
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x2,
    0x46, 0xf6, 0x44, 0x5f, 0x74, 0x20, 0x0, 0x2,
    0xf3, 0x0, 0xf, 0x40, 0x0, 0x0, 0x2, 0xf3,
    0x0, 0xf, 0x40, 0x0, 0x0, 0x2, 0xf3, 0x0,
    0xf, 0x40, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x15, 0x57, 0xf6, 0x55, 0x5f, 0x85,
    0x51, 0x0, 0x6, 0xf0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0xb, 0xb0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0x6f, 0x40, 0x0, 0xf, 0x40, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0xf, 0x40, 0x0, 0x9, 0xa0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0xe5, 0x0, 0x2, 0xf4, 0x0, 0x0, 0x0, 0xe5,
    0x3, 0x33, 0xfa, 0x33, 0x20, 0x0, 0xe5, 0xe,
    0xff, 0xee, 0xfe, 0x90, 0x7f, 0xff, 0x80, 0xa9,
    0x0, 0xf3, 0x0, 0x13, 0xe7, 0x24, 0x9d, 0x47,
    0xf4, 0x40, 0x0, 0xe5, 0x4e, 0xee, 0xee, 0xee,
    0xe0, 0x4, 0xee, 0x60, 0xc, 0x40, 0x0, 0x0,
    0x9e, 0xf6, 0x8f, 0xff, 0xff, 0xff, 0xf0, 0x10,
    0xe5, 0x13, 0xf7, 0x33, 0xe8, 0x30, 0x0, 0xe5,
    0x7, 0xf9, 0x27, 0xe1, 0x0, 0x0, 0xe5, 0x0,
    0x4b, 0xff, 0x80, 0x0, 0x13, 0xf5, 0x4, 0x8e,
    0xeb, 0xfb, 0x30, 0x4f, 0xd1, 0x9f, 0xb6, 0x0,
    0x3b, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+63A7 "控" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x8b, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0x8b,
    0x3, 0x55, 0x9f, 0x55, 0x51, 0x3, 0xac, 0x3a,
    0xff, 0xff, 0xff, 0xf3, 0x1f, 0xff, 0xea, 0x90,
    0x0, 0x0, 0xf3, 0x0, 0x8b, 0x6, 0x58, 0x72,
    0xc2, 0x92, 0x0, 0x8b, 0x20, 0x9f, 0x40, 0xaf,
    0x60, 0x0, 0xaf, 0xdb, 0xd3, 0x0, 0x6, 0xf5,
    0x3f, 0xfd, 0x24, 0x54, 0x44, 0x44, 0x50, 0x16,
    0x9b, 0x3, 0xff, 0xff, 0xff, 0x90, 0x0, 0x8b,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x8b, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x3, 0xba, 0x3, 0x33,
    0x8f, 0x33, 0x31, 0xe, 0xf6, 0x3f, 0xff, 0xff,
    0xff, 0xf7,

    /* U+6570 "数" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x6, 0xc0, 0x61, 0x3f, 0x0, 0x0, 0xa, 0xa6,
    0xc4, 0xe1, 0x8d, 0x0, 0x0, 0x1, 0x96, 0xc6,
    0x50, 0xde, 0xbb, 0xb2, 0x2f, 0xff, 0xff, 0xf8,
    0xfa, 0xaf, 0xc2, 0x2, 0x7f, 0xf7, 0x2d, 0xf0,
    0xf, 0x30, 0x7, 0xe8, 0xdb, 0xba, 0xe3, 0x2f,
    0x0, 0x4d, 0x27, 0x50, 0x30, 0x97, 0x6d, 0x0,
    0x2, 0x5f, 0x21, 0x10, 0x5c, 0xb9, 0x0, 0x5f,
    0xff, 0xff, 0xe0, 0xf, 0xf3, 0x0, 0x3, 0xf4,
    0xb, 0x90, 0xc, 0xf0, 0x0, 0x2, 0xaf, 0xce,
    0x10, 0x7f, 0xea, 0x0, 0x2, 0x8e, 0xdf, 0xa7,
    0xf6, 0x3f, 0xa0, 0x5f, 0xb4, 0x2, 0x5d, 0x60,
    0x4, 0xe3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10,

    /* U+670D "服" */
    0x4, 0xff, 0xfe, 0x1f, 0xff, 0xff, 0xd0, 0x4,
    0xe3, 0x6e, 0x1f, 0x43, 0x38, 0xd0, 0x4, 0xd0,
    0x3e, 0x1f, 0x13, 0x3a, 0xc0, 0x4, 0xf9, 0xbe,
    0x1f, 0x1a, 0xee, 0x60, 0x4, 0xf9, 0xae, 0x1f,
    0x44, 0x44, 0x40, 0x4, 0xd0, 0x3e, 0x1f, 0xff,
    0xee, 0xf0, 0x5, 0xe4, 0x7e, 0x1f, 0x6c, 0x5,
    0xd0, 0x5, 0xfd, 0xee, 0x1f, 0x1e, 0x3b, 0x70,
    0x6, 0xb0, 0x3e, 0x1f, 0x17, 0xde, 0x0, 0x9,
    0x90, 0x3e, 0x1f, 0x13, 0xfc, 0x10, 0xe, 0x55,
    0x9e, 0x1f, 0x7f, 0x7c, 0xe4, 0x1c, 0x9, 0xc7,
    0x1f, 0x75, 0x0, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+672A "未" */
    0x0, 0x0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x55, 0x55,
    0xbb, 0x55, 0x55, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x4, 0x55, 0x55, 0xbb, 0x55,
    0x55, 0x40, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x1e, 0xdd, 0xe2, 0x0, 0x0,
    0x0, 0x1, 0xc9, 0x9a, 0x9d, 0x10, 0x0, 0x0,
    0x2d, 0xc0, 0x9a, 0xc, 0xd2, 0x0, 0x7, 0xfc,
    0x10, 0x9a, 0x0, 0xbf, 0x70, 0x3f, 0x80, 0x0,
    0x9a, 0x0, 0x8, 0xf3, 0x1, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x10,

    /* U+672C "本" */
    0x0, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbb, 0x0, 0x0, 0x0, 0x4, 0x55,
    0x55, 0xcc, 0x55, 0x55, 0x40, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0xd, 0xee,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xbb, 0xc6,
    0x0, 0x0, 0x0, 0x1, 0xf4, 0xbb, 0x4f, 0x10,
    0x0, 0x0, 0xc, 0xa0, 0xbb, 0xa, 0xc0, 0x0,
    0x0, 0xad, 0x10, 0xbb, 0x1, 0xea, 0x0, 0x9,
    0xf5, 0x55, 0xcc, 0x55, 0x5f, 0xb0, 0x5f, 0x37,
    0xee, 0xff, 0xee, 0x73, 0xf5, 0x2, 0x0, 0x0,
    0xbb, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0xbb,
    0x0, 0x0, 0x0,

    /* U+6D4F "浏" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x82,
    0x0, 0x9a, 0x0, 0x0, 0x4e, 0xd, 0xe2, 0x6,
    0xe0, 0x3, 0x34, 0xe0, 0x1c, 0x89, 0xaf, 0xa7,
    0x99, 0x4e, 0x1, 0x0, 0xaa, 0xaf, 0x89, 0x94,
    0xe5, 0xe4, 0x4, 0x13, 0xe0, 0x99, 0x4e, 0x8,
    0xf3, 0xba, 0x6c, 0x9, 0x94, 0xe0, 0x6, 0x2,
    0xfd, 0x80, 0x99, 0x4e, 0x0, 0x62, 0x8, 0xf4,
    0x9, 0x94, 0xe0, 0xf, 0x40, 0x6f, 0x50, 0x99,
    0x4e, 0x5, 0xf0, 0xe, 0xdd, 0x8, 0x94, 0xe0,
    0x9b, 0x8, 0xe1, 0xe6, 0x0, 0x4e, 0xe, 0x65,
    0xf4, 0x7, 0xa1, 0x38, 0xe2, 0xe1, 0x98, 0x0,
    0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7247 "片" */
    0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x4,
    0xf0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x4, 0xf3, 0x22, 0x27,
    0xe2, 0x22, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x4, 0xf1, 0x11, 0x11, 0x11, 0x10, 0x0,
    0x4f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x8d, 0x44, 0x44,
    0x4a, 0xc0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0x8c,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x8, 0xc0, 0x0,
    0x8f, 0x10, 0x0, 0x0, 0x8c, 0x0, 0xb, 0x80,
    0x0, 0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+7248 "版" */
    0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf0, 0xe5, 0x15, 0x78, 0xad, 0xf1, 0x4, 0xf0,
    0xe5, 0x3f, 0xb9, 0x75, 0x20, 0x4, 0xf0, 0xe5,
    0x3f, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x7f,
    0xff, 0xff, 0xf0, 0x4, 0xf4, 0x44, 0x4f, 0xd7,
    0x48, 0xf0, 0x4, 0xf0, 0x0, 0x4e, 0x98, 0x9,
    0xb0, 0x4, 0xff, 0xf4, 0x5d, 0x5d, 0xe, 0x60,
    0x4, 0xe2, 0xe4, 0x7b, 0xd, 0xae, 0x0, 0x6,
    0xd0, 0xe4, 0xb8, 0x6, 0xf7, 0x0, 0x8, 0xb0,
    0xe5, 0xf4, 0x1d, 0xfc, 0x0, 0xd, 0x70, 0xec,
    0xe3, 0xdd, 0x2d, 0xc2, 0x2f, 0x20, 0xe7, 0x6c,
    0xb1, 0x1, 0xc7, 0x2, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0,

    /* U+7F51 "网" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xe9, 0x44,
    0x44, 0x44, 0x44, 0xf6, 0xe6, 0x60, 0x3c, 0x0,
    0x56, 0xe6, 0xe7, 0xd6, 0x9a, 0xc2, 0xb7, 0xe6,
    0xe6, 0x5e, 0xe4, 0xab, 0xf2, 0xe6, 0xe6, 0xd,
    0xe0, 0x2f, 0xc0, 0xe6, 0xe6, 0xd, 0xe0, 0x1f,
    0xb0, 0xe6, 0xe6, 0x6f, 0xe7, 0x9d, 0xf3, 0xe6,
    0xe7, 0xe7, 0x57, 0xf3, 0xab, 0xe6, 0xeb, 0xd0,
    0xd, 0x90, 0x25, 0xe6, 0xe6, 0x0, 0x2, 0x1,
    0x44, 0xf5, 0xe6, 0x0, 0x0, 0x2, 0xff, 0xd1,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+82AF "芯" */
    0x0, 0x3, 0xf1, 0x0, 0x8c, 0x0, 0x0, 0x36,
    0x68, 0xf7, 0x66, 0xbe, 0x66, 0x60, 0x6c, 0xcd,
    0xfd, 0xcc, 0xef, 0xcc, 0xc0, 0x0, 0x3, 0xf1,
    0x0, 0x8c, 0x0, 0x0, 0x0, 0x1, 0x62, 0xa0,
    0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd9, 0x0,
    0x0, 0x0, 0x2, 0x74, 0xf1, 0x4f, 0x30, 0x94,
    0x0, 0x7, 0xd4, 0xf1, 0xa, 0xd0, 0xac, 0x0,
    0xb, 0x94, 0xf1, 0x1, 0x60, 0x2f, 0x40, 0x1f,
    0x54, 0xf1, 0x0, 0xb, 0x3a, 0xc0, 0x6f, 0x4,
    0xf1, 0x0, 0xf, 0x53, 0xf3, 0x37, 0x3, 0xf8,
    0x44, 0x7f, 0x30, 0x20, 0x0, 0x0, 0xcf, 0xff,
    0xea, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+89C8 "览" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0xd4, 0x0, 0x0, 0x6, 0xe0,
    0x6e, 0x5, 0xf7, 0x44, 0x41, 0x6, 0xe0, 0x6e,
    0xb, 0xff, 0xff, 0xf3, 0x6, 0xe0, 0x6e, 0x3f,
    0x4b, 0x70, 0x0, 0x6, 0xe0, 0x6e, 0x5a, 0x4,
    0xec, 0x10, 0x1, 0x54, 0x69, 0x44, 0x44, 0x5a,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x4f, 0x20, 0x34, 0x0, 0xe6, 0x0, 0x0,
    0x4f, 0x20, 0xa9, 0x0, 0xe6, 0x0, 0x0, 0x4f,
    0x20, 0xee, 0x40, 0xe7, 0x30, 0x0, 0x3, 0x1b,
    0xdf, 0x50, 0x24, 0xf2, 0x0, 0x38, 0xed, 0x2e,
    0x93, 0x38, 0xf1, 0xe, 0xfd, 0x60, 0x8, 0xff,
    0xff, 0xa0, 0x3, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8F93 "输" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x98, 0x0, 0x0, 0xcf, 0x30, 0x0, 0x14, 0xd8,
    0x41, 0x9, 0xe9, 0xd1, 0x0, 0x4e, 0xfe, 0xe6,
    0x9f, 0x30, 0xbd, 0x20, 0x4, 0xd0, 0xc, 0xfe,
    0xcc, 0xcc, 0xf4, 0x8, 0x9d, 0x7, 0x23, 0x44,
    0x41, 0x71, 0xd, 0x4f, 0x3, 0xaa, 0xa1, 0x33,
    0xa0, 0xf, 0x9f, 0x86, 0xd7, 0xe2, 0xf4, 0xc0,
    0xa, 0xaf, 0xa6, 0xc6, 0xe2, 0xf4, 0xc0, 0x0,
    0x1f, 0x5, 0xd8, 0xe2, 0xf4, 0xc0, 0x15, 0x8f,
    0xd7, 0xc5, 0xe2, 0xf4, 0xc0, 0x4e, 0xcf, 0x66,
    0xda, 0xe2, 0xc4, 0xc0, 0x0, 0x1f, 0x5, 0xa2,
    0xd2, 0x37, 0xb0, 0x0, 0x1f, 0x4, 0x9a, 0xc0,
    0xde, 0x60,

    /* U+8FDE "连" */
    0x0, 0x40, 0x0, 0x6, 0xc0, 0x0, 0x0, 0x5,
    0xf4, 0x6, 0x6d, 0xd6, 0x66, 0x61, 0x0, 0x9e,
    0x4d, 0xef, 0xdd, 0xdd, 0xd3, 0x0, 0x9, 0x10,
    0xbb, 0x26, 0x0, 0x0, 0x0, 0x0, 0x3, 0xf4,
    0x5f, 0x0, 0x0, 0x1f, 0xfe, 0xa, 0xfb, 0xdf,
    0xbb, 0xa0, 0x4, 0x8e, 0x5, 0x77, 0xaf, 0x77,
    0x70, 0x0, 0x5e, 0x1, 0x11, 0x6f, 0x11, 0x10,
    0x0, 0x5e, 0x3f, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x6f, 0x2, 0x22, 0x6f, 0x22, 0x20, 0x1, 0xef,
    0xa0, 0x0, 0x5f, 0x0, 0x0, 0xd, 0xc2, 0xed,
    0x85, 0x44, 0x33, 0x43, 0xd, 0x10, 0x18, 0xcf,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+914D "配" */
    0x1f, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xe0, 0x2,
    0x4e, 0x7b, 0x21, 0x35, 0x58, 0xf0, 0x2, 0x4e,
    0x7b, 0x30, 0x0, 0x5, 0xf0, 0xc, 0xff, 0xff,
    0xf5, 0x0, 0x5, 0xf0, 0xc, 0x4d, 0x67, 0xb5,
    0x68, 0x8b, 0xf0, 0xc, 0x6b, 0x59, 0xc5, 0xbd,
    0x99, 0x80, 0xc, 0xd6, 0x2d, 0xf5, 0xb8, 0x0,
    0x0, 0xc, 0x70, 0x0, 0xb5, 0xb8, 0x0, 0x10,
    0xc, 0xff, 0xff, 0xf5, 0xb8, 0x0, 0xc7, 0xc,
    0x52, 0x22, 0xc5, 0xb8, 0x0, 0xc7, 0xc, 0x52,
    0x22, 0xc5, 0xaa, 0x12, 0xe5, 0xc, 0xff, 0xff,
    0xf5, 0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x32, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7b, 0xfb, 0x0,
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xd0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xdf, 0xd0, 0x0, 0xa,
    0xff, 0xff, 0xb6, 0x10, 0xed, 0x0, 0x0, 0xaf,
    0x94, 0x0, 0x0, 0xe, 0xd0, 0x0, 0xa, 0xf1,
    0x0, 0x0, 0x0, 0xed, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0xe, 0xd0, 0x0, 0xa, 0xf1, 0x0,
    0x0, 0x45, 0xfd, 0x0, 0x0, 0xaf, 0x10, 0x1,
    0xef, 0xff, 0xd0, 0x17, 0x9d, 0xf1, 0x0, 0x5f,
    0xff, 0xfc, 0xe, 0xff, 0xff, 0x10, 0x0, 0xaf,
    0xfd, 0x31, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x1,
    0x0, 0x3, 0xbd, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F008 "" */
    0x50, 0x18, 0x88, 0x88, 0x88, 0x84, 0x5, 0xfa,
    0xbf, 0xdd, 0xdd, 0xdd, 0xfd, 0xaf, 0xe4, 0x7f,
    0x10, 0x0, 0x0, 0xca, 0x4e, 0xe0, 0x4f, 0x10,
    0x0, 0x0, 0xc8, 0xe, 0xfe, 0xef, 0x10, 0x0,
    0x0, 0xcf, 0xef, 0xe0, 0x3f, 0xee, 0xee, 0xee,
    0xf8, 0xe, 0xf6, 0x8f, 0x76, 0x66, 0x66, 0xeb,
    0x6f, 0xf8, 0xaf, 0x10, 0x0, 0x0, 0xcc, 0x8f,
    0xe0, 0x3f, 0x10, 0x0, 0x0, 0xc8, 0xe, 0xfc,
    0xdf, 0x65, 0x55, 0x55, 0xee, 0xcf, 0xc2, 0x5f,
    0xff, 0xff, 0xff, 0xf9, 0x2c,

    /* U+F00B "" */
    0x57, 0x75, 0x5, 0x77, 0x77, 0x77, 0x75, 0xff,
    0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xe,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0x10, 0x0, 0x11,
    0x11, 0x11, 0x10, 0xef, 0xfe, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x68, 0x87, 0x7, 0x88, 0x88, 0x88, 0x86, 0x68,
    0x87, 0x7, 0x88, 0x88, 0x88, 0x86, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xfd, 0xd, 0xff,
    0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xe2, 0x2d, 0x60, 0x0, 0x1,
    0xdf, 0xfe, 0x20, 0xdf, 0xf6, 0x0, 0x1d, 0xff,
    0xe2, 0x0, 0x8f, 0xff, 0x61, 0xdf, 0xfe, 0x20,
    0x0, 0x8, 0xff, 0xfe, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x20, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0,
    0xb, 0xe2, 0xef, 0xf6, 0x0, 0xbf, 0xf8, 0x4f,
    0xff, 0x6b, 0xff, 0xd1, 0x4, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x5f, 0xff, 0xe1, 0x0, 0x0, 0xbf,
    0xff, 0xf6, 0x0, 0xb, 0xff, 0xdf, 0xff, 0x60,
    0xbf, 0xfd, 0x14, 0xff, 0xf5, 0xcf, 0xd1, 0x0,
    0x4f, 0xf6, 0x17, 0x10, 0x0, 0x3, 0x60,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x21, 0xff, 0x12, 0xf7, 0x0, 0x6, 0xff, 0x61,
    0xff, 0x16, 0xff, 0x60, 0x1f, 0xf9, 0x1, 0xff,
    0x10, 0x9f, 0xf1, 0x6f, 0xe0, 0x1, 0xff, 0x10,
    0xe, 0xf6, 0xaf, 0x80, 0x1, 0xff, 0x10, 0x8,
    0xfa, 0xcf, 0x60, 0x1, 0xff, 0x10, 0x6, 0xfc,
    0xaf, 0x80, 0x0, 0xaa, 0x0, 0x8, 0xfb, 0x7f,
    0xd0, 0x0, 0x0, 0x0, 0xd, 0xf7, 0x1f, 0xf8,
    0x0, 0x0, 0x0, 0x8f, 0xf1, 0x7, 0xff, 0x91,
    0x0, 0x2a, 0xff, 0x70, 0x0, 0x9f, 0xff, 0xee,
    0xff, 0xf9, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xfd,
    0x50, 0x0, 0x0, 0x0, 0x2, 0x44, 0x20, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xd6, 0xdf,
    0xff, 0xfd, 0x6d, 0x30, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x5f, 0xff, 0xff, 0xaa, 0xff,
    0xff, 0xf5, 0x1a, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0xa1, 0x3, 0xff, 0xd0, 0x0, 0xd, 0xff, 0x30,
    0x4, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x40, 0x4f,
    0xff, 0xfb, 0x22, 0xbf, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x9, 0xfe, 0xff,
    0xff, 0xff, 0xef, 0x90, 0x0, 0x50, 0x5e, 0xff,
    0xe5, 0x5, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x77, 0x40, 0x0,
    0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x3, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0xd, 0xf5, 0x0,
    0x0, 0x0, 0x1b, 0xfd, 0xff, 0x8d, 0xf5, 0x0,
    0x0, 0x2, 0xdf, 0xb1, 0x2d, 0xff, 0xf5, 0x0,
    0x0, 0x4f, 0xf8, 0x3e, 0xc2, 0xbf, 0xf5, 0x0,
    0x7, 0xff, 0x55, 0xff, 0xfe, 0x39, 0xfe, 0x40,
    0x9f, 0xe3, 0x8f, 0xff, 0xff, 0xf5, 0x6f, 0xf6,
    0xac, 0x2a, 0xff, 0xff, 0xff, 0xff, 0x73, 0xe6,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x6f, 0xff, 0xd7, 0x7f, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xd, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xd, 0xff, 0xf2, 0x0,
    0x0, 0x4f, 0xff, 0x70, 0xb, 0xff, 0xe1, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xe2, 0x0, 0x0, 0x79, 0x99,
    0x82, 0xde, 0x28, 0x99, 0x97, 0xff, 0xff, 0xfb,
    0x22, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xb3, 0xcf, 0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca,

    /* U+F01C "" */
    0x0, 0x6, 0xbb, 0xbb, 0xbb, 0xba, 0x30, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x6, 0xfb, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50,
    0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe1,
    0xdf, 0x84, 0x42, 0x0, 0x0, 0x34, 0x4b, 0xf9,
    0xff, 0xff, 0xfd, 0x0, 0x1, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0x98, 0x8b, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0,
    0x1, 0x8d, 0xff, 0xc6, 0x0, 0xef, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xe4, 0xdf, 0x4, 0xff, 0xb3,
    0x0, 0x4c, 0xff, 0xff, 0xe, 0xf9, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x6f, 0xc0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x8e, 0x50, 0x0, 0x1, 0xde, 0xee,
    0xed, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x21, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x8, 0xf8, 0xff, 0xfb,
    0xbc, 0x10, 0x0, 0x1e, 0xf4, 0xff, 0xfc, 0x10,
    0x0, 0x1, 0xdf, 0xc0, 0xfe, 0xef, 0xe8, 0x44,
    0x8e, 0xfe, 0x10, 0xfe, 0x1a, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0xfd, 0x0, 0x28, 0xbb, 0x94, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2, 0x70, 0x0, 0x2, 0xef, 0x0,
    0x2, 0xef, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x34, 0x47, 0xff, 0xf0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x5, 0xc0, 0x0,
    0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x2, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0x2, 0x20, 0xff, 0xff,
    0xff, 0xf0, 0x8e, 0x1f, 0xff, 0xff, 0xff, 0x0,
    0xe7, 0xff, 0xff, 0xff, 0xf0, 0x3f, 0x5f, 0xff,
    0xff, 0xff, 0x8, 0x90, 0x34, 0x47, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x70, 0x0,
    0x0, 0x0, 0x2, 0x70, 0x0, 0x5, 0xfa, 0x0,
    0x0, 0x0, 0x2e, 0xf0, 0x0, 0x81, 0x4f, 0x60,
    0x0, 0x2, 0xef, 0xf0, 0x1, 0xdd, 0x7, 0xf0,
    0xdf, 0xff, 0xff, 0xf0, 0x32, 0x1e, 0x80, 0xf6,
    0xff, 0xff, 0xff, 0xf0, 0x8e, 0x27, 0xe0, 0xb9,
    0xff, 0xff, 0xff, 0xf0, 0xe, 0x73, 0xf1, 0x9b,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0x54, 0xf0, 0x9a,
    0xff, 0xff, 0xff, 0xf0, 0x89, 0xa, 0xc0, 0xd8,
    0x34, 0x47, 0xff, 0xf0, 0x0, 0x7f, 0x43, 0xf3,
    0x0, 0x0, 0x5f, 0xf0, 0x2, 0xf6, 0xc, 0xb0,
    0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0xbf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,

    /* U+F03E "" */
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x73, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0x32,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x7f,
    0xff, 0xfd, 0xff, 0xff, 0xfd, 0x10, 0xcf, 0xff,
    0xa0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x7, 0xff, 0xff, 0xf3, 0x5f, 0xa0, 0x0, 0x0,
    0xcf, 0xff, 0x30, 0x3, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x1, 0xfa,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0xd, 0xff, 0x70, 0x0, 0x0, 0x6f, 0xff, 0xf1,
    0x0, 0x1, 0xef, 0xff, 0xfa, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x60, 0x5f, 0xff, 0xff, 0xff, 0xe0,
    0xcf, 0xff, 0xff, 0xff, 0xf6, 0xfe, 0xbf, 0xff,
    0xff, 0xf9, 0xfd, 0x4f, 0xff, 0xff, 0xf9, 0xbf,
    0x49, 0xff, 0xff, 0xf5, 0x3f, 0xe5, 0x2e, 0xff,
    0xd0, 0x6, 0xff, 0xff, 0xfd, 0x20, 0x0, 0x28,
    0xba, 0x60, 0x0,

    /* U+F048 "" */
    0x4, 0x30, 0x0, 0x0, 0x31, 0x1f, 0xe0, 0x0,
    0x6, 0xf9, 0x1f, 0xe0, 0x0, 0x7f, 0xfa, 0x1f,
    0xe0, 0x9, 0xff, 0xfa, 0x1f, 0xe0, 0xaf, 0xff,
    0xfa, 0x1f, 0xeb, 0xff, 0xff, 0xfa, 0x1f, 0xff,
    0xff, 0xff, 0xfa, 0x1f, 0xff, 0xff, 0xff, 0xfa,
    0x1f, 0xe6, 0xff, 0xff, 0xfa, 0x1f, 0xe0, 0x5f,
    0xff, 0xfa, 0x1f, 0xe0, 0x4, 0xff, 0xfa, 0x1f,
    0xe0, 0x0, 0x3e, 0xfa, 0xf, 0xd0, 0x0, 0x2,
    0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfb,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0xf,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x6a, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x14, 0x44, 0x20, 0x1, 0x44, 0x42, 0xd, 0xff,
    0xff, 0x10, 0xdf, 0xff, 0xf1, 0xff, 0xff, 0xf3,
    0xf, 0xff, 0xff, 0x3f, 0xff, 0xff, 0x40, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0x40, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xf4, 0xf, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0x40, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xf4, 0xf,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0x40, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xf4, 0xf, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0x30, 0xff, 0xff, 0xf3, 0x9f, 0xff,
    0xc0, 0x9, 0xff, 0xfc, 0x0,

    /* U+F04D "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x42, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0,

    /* U+F051 "" */
    0x2, 0x10, 0x0, 0x0, 0x42, 0xf, 0xe2, 0x0,
    0x3, 0xfb, 0xf, 0xfe, 0x30, 0x4, 0xfb, 0xf,
    0xff, 0xf4, 0x4, 0xfb, 0xf, 0xff, 0xff, 0x54,
    0xfb, 0xf, 0xff, 0xff, 0xfa, 0xfb, 0xf, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0xff, 0xff, 0xff, 0xfb,
    0xf, 0xff, 0xff, 0xd6, 0xfb, 0xf, 0xff, 0xfd,
    0x14, 0xfb, 0xf, 0xff, 0xc1, 0x4, 0xfb, 0xf,
    0xfb, 0x0, 0x4, 0xfb, 0xc, 0xa0, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x3, 0x99, 0x99, 0x99, 0x99, 0x99, 0x50, 0x5,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd1,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x90, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x3f,
    0xfd, 0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x3f,
    0xfd, 0x10, 0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0xa4, 0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xfd,
    0x10, 0x0, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x3f, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xfd, 0x0,
    0x0, 0x7, 0xff, 0x90, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x7, 0xff, 0x90, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x7, 0x80,
    0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0,
    0x6, 0x99, 0x9a, 0xff, 0xc9, 0x99, 0x80, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x1, 0x11, 0x3f, 0xf7,
    0x11, 0x10, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xd3, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x69, 0x99, 0x99, 0x99, 0x99, 0x98, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x1, 0x56, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xfe, 0xef, 0xf9, 0x10, 0x0,
    0x0, 0x7f, 0xfa, 0x10, 0x3, 0xdf, 0xe4, 0x0,
    0x8, 0xff, 0xa0, 0x9, 0xb4, 0x1e, 0xff, 0x50,
    0x4f, 0xff, 0x20, 0xb, 0xff, 0x26, 0xff, 0xe1,
    0xef, 0xff, 0x9, 0xcf, 0xff, 0x63, 0xff, 0xfa,
    0xbf, 0xff, 0x9, 0xff, 0xff, 0x54, 0xff, 0xf6,
    0x1e, 0xff, 0x51, 0xdf, 0xfb, 0x9, 0xff, 0xb0,
    0x3, 0xef, 0xe2, 0x4, 0x30, 0x5f, 0xfc, 0x10,
    0x0, 0x2c, 0xff, 0x95, 0x6a, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x49, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x0, 0x14, 0x66, 0x40,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xac, 0xff, 0xef,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xa1,
    0x0, 0x4d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x9f,
    0xf5, 0xab, 0x31, 0xef, 0xf4, 0x0, 0x7, 0xb1,
    0x5, 0xff, 0xff, 0xe1, 0x7f, 0xfe, 0x10, 0xf,
    0xfe, 0x30, 0x2d, 0xff, 0xf5, 0x4f, 0xff, 0x90,
    0xc, 0xff, 0xe0, 0x0, 0xaf, 0xf6, 0x5f, 0xff,
    0x60, 0x2, 0xff, 0xf4, 0x0, 0x6, 0xff, 0xef,
    0xfb, 0x0, 0x0, 0x4f, 0xfd, 0x10, 0x0, 0x3e,
    0xff, 0xc0, 0x0, 0x0, 0x2, 0xdf, 0xe8, 0x54,
    0x1, 0xbf, 0xe3, 0x0, 0x0, 0x0, 0x5, 0xae,
    0xff, 0x60, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xa1,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xc0,
    0xf, 0xff, 0x70, 0x0, 0x0, 0x4, 0xff, 0xfd,
    0x1, 0xff, 0xff, 0x10, 0x0, 0x0, 0xdf, 0xff,
    0xe0, 0x2f, 0xff, 0xfa, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x9b, 0xff, 0xff, 0xf3, 0x0, 0x1f, 0xff,
    0xff, 0xb0, 0xe, 0xff, 0xff, 0xc0, 0xa, 0xff,
    0xff, 0xfe, 0x24, 0xff, 0xff, 0xff, 0x60, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x6,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x30,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x36, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x80, 0xdd, 0xdb,
    0x0, 0x0, 0x8d, 0xef, 0xf8, 0xff, 0xff, 0xb0,
    0x7, 0xff, 0xff, 0xfd, 0x55, 0x6f, 0xf4, 0x6f,
    0xf8, 0xaf, 0xe2, 0x0, 0x5, 0x74, 0xff, 0x90,
    0x7e, 0x20, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xc2, 0x50, 0x4a, 0x0,
    0x1, 0x2e, 0xfd, 0x1d, 0xf4, 0x8f, 0xb0, 0xff,
    0xff, 0xe1, 0xb, 0xff, 0xff, 0xfb, 0xff, 0xfe,
    0x20, 0x0, 0xcf, 0xff, 0xfb, 0x12, 0x21, 0x0,
    0x0, 0x2, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x7, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x8, 0xff, 0x95, 0xff,
    0xb0, 0x0, 0x8, 0xff, 0x90, 0x5, 0xff, 0xb0,
    0x7, 0xff, 0x90, 0x0, 0x5, 0xff, 0xb0, 0x9f,
    0x90, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x3, 0x10,

    /* U+F078 "" */
    0x4c, 0x20, 0x0, 0x0, 0x0, 0xb6, 0xb, 0xfe,
    0x20, 0x0, 0x0, 0xcf, 0xf0, 0x2e, 0xfe, 0x20,
    0x0, 0xcf, 0xf4, 0x0, 0x2e, 0xfe, 0x20, 0xcf,
    0xf4, 0x0, 0x0, 0x2e, 0xfe, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf3, 0x8, 0xbb, 0xbb, 0xbb,
    0x90, 0x0, 0xb, 0xff, 0xff, 0x39, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x8f, 0xcf, 0xcf, 0xf0, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x38, 0x2f, 0x94, 0x80,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x2f,
    0x90, 0x0, 0x0, 0x3, 0xa, 0xf1, 0x30, 0x0,
    0x2f, 0x90, 0x0, 0x0, 0x1f, 0xcb, 0xf8, 0xf8,
    0x0, 0x2f, 0xeb, 0xbb, 0xbb, 0x39, 0xff, 0xff,
    0xe2, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xb0, 0x9f,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xd1, 0x0,

    /* U+F07B "" */
    0x37, 0x88, 0x87, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0xcc, 0xcc, 0xb6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x1, 0x1c, 0xff, 0xc1, 0x10,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x79, 0x99,
    0x3b, 0xff, 0xb3, 0x99, 0x97, 0xff, 0xff, 0xb2,
    0x44, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xdd,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xb3, 0xcf, 0xac, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xc7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xf3, 0x0, 0x0, 0x4a, 0x30, 0x2,
    0xdf, 0xf8, 0x0, 0x5, 0xdf, 0xfe, 0x15, 0xef,
    0xfb, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x2, 0xba, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x4, 0x86, 0x0, 0x0, 0x0, 0x10, 0x6, 0xff,
    0xfa, 0x0, 0x2, 0xdf, 0xd1, 0xef, 0x3c, 0xf1,
    0x1, 0xdf, 0xfa, 0xe, 0xe0, 0xaf, 0x21, 0xdf,
    0xfa, 0x0, 0x9f, 0xef, 0xf6, 0xdf, 0xfa, 0x0,
    0x0, 0x8d, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x48, 0xef,
    0xff, 0xf6, 0x0, 0x0, 0x6f, 0xff, 0xfb, 0xff,
    0xf6, 0x0, 0xe, 0xf3, 0xcf, 0x23, 0xff, 0xf6,
    0x0, 0xee, 0xa, 0xf2, 0x4, 0xff, 0xf6, 0x9,
    0xfe, 0xfc, 0x0, 0x4, 0xff, 0xf1, 0x8, 0xda,
    0x10, 0x0, 0x2, 0x62, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf9, 0x86, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x98, 0xf6, 0x8, 0xa6, 0x8f, 0xff, 0xf9,
    0x59, 0x90, 0xff, 0xa8, 0xff, 0xff, 0xfc, 0xcc,
    0xf, 0xfa, 0x8f, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xa8, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xfa, 0x8f,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xa8, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xfa, 0x8f, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xa8, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xfa, 0x7f, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xe3,
    0x12, 0x22, 0x22, 0x21, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0xac, 0xcc, 0xcc, 0xcb, 0x50,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x49, 0x99, 0x99, 0x99, 0x95, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0xfd, 0x22, 0x22,
    0x22, 0x4f, 0xf6, 0xf, 0xc0, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0xfc, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x6f, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xdc, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xb0, 0x5,
    0xff, 0xff, 0x6f, 0xff, 0xf6, 0x0, 0xf, 0xff,
    0xf6, 0xff, 0xff, 0xc0, 0x6, 0xff, 0xff, 0x6f,
    0xff, 0xff, 0xed, 0xff, 0xff, 0xf6, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xde,
    0xee, 0xee, 0xee, 0xee, 0xee, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xe2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x37, 0x88, 0x88, 0x88, 0x88, 0x88, 0x73, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0xd2, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x2d, 0xff, 0x64, 0xef, 0xff, 0xfe,
    0x45, 0xff, 0xff, 0xfa, 0x2b, 0xff, 0xb2, 0xaf,
    0xff, 0xff, 0xff, 0xd3, 0x55, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf0, 0x0, 0x4, 0xff, 0xff, 0xd0, 0x0, 0x6,
    0xff, 0xff, 0x80, 0x0, 0x8, 0xff, 0xff, 0x30,
    0x0, 0xa, 0xff, 0xff, 0xaa, 0xa6, 0xc, 0xff,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xe1,
    0xb, 0xdd, 0xdf, 0xff, 0x60, 0x0, 0x0, 0x4f,
    0xfd, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x4, 0x55,
    0xef, 0xb5, 0x52, 0x0, 0x0, 0xff, 0xfd, 0x1f,
    0xff, 0xb0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xff, 0xff, 0x53, 0x33, 0x20, 0x0,
    0xf, 0xff, 0x97, 0xff, 0xfb, 0x57, 0x0, 0xff,
    0xf8, 0xaf, 0xff, 0xc6, 0xf8, 0xf, 0xff, 0x8a,
    0xff, 0xfc, 0x4a, 0xa1, 0xff, 0xf8, 0xaf, 0xff,
    0xe3, 0x22, 0xf, 0xff, 0x8a, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xf8, 0xaf, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0x8a, 0xff, 0xff, 0xff, 0xf4, 0x35, 0x52,
    0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xfe, 0x20,

    /* U+F0F3 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8f,
    0xfa, 0x30, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x2, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x8, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xa2, 0x0,
    0x0, 0x0,

    /* U+F11C "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xfc, 0xc, 0x30, 0xe1, 0x1d, 0xd, 0x11, 0xfc,
    0xfc, 0xb, 0x30, 0xe0, 0x1d, 0xd, 0x10, 0xfc,
    0xff, 0xfe, 0xff, 0xef, 0xfe, 0xfe, 0xef, 0xfc,
    0xff, 0xf1, 0x5a, 0x8, 0x70, 0xa0, 0x5f, 0xfc,
    0xff, 0xf3, 0x7b, 0x29, 0x92, 0xc2, 0x7f, 0xfc,
    0xff, 0xbf, 0xcb, 0xbb, 0xbb, 0xbf, 0xcb, 0xfc,
    0xfc, 0xb, 0x20, 0x0, 0x0, 0xd, 0x0, 0xfc,
    0xff, 0xcf, 0xcc, 0xcc, 0xcc, 0xcf, 0xcc, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x29, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x4, 0x9a, 0xaa, 0xaf, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xb3, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x35, 0x55, 0x55, 0x2, 0x0, 0xf, 0xff, 0xff,
    0xf2, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x2f, 0xf4,
    0xf, 0xff, 0xff, 0xf2, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0x32, 0x22, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x8a, 0xaa, 0xaa,
    0xaa, 0xaa, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x24, 0x55, 0x31, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xaf, 0xff, 0xff, 0xff, 0xc7,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xfe, 0xde, 0xff,
    0xff, 0xf6, 0x0, 0x5f, 0xff, 0xb5, 0x10, 0x0,
    0x3, 0x8e, 0xff, 0xb0, 0xdf, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x18, 0x0, 0x5,
    0xae, 0xfe, 0xc8, 0x10, 0x4, 0x60, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x95, 0x34, 0x7d, 0xff, 0x40, 0x0,
    0x0, 0x2, 0xa2, 0x0, 0x0, 0x0, 0x77, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x96, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xda, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2c, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x21, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x27, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F241 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x22, 0x21,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x66, 0x63, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F242 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x22, 0x10, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x66, 0x50, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F243 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x12, 0x22, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x5f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x5f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x26,
    0x66, 0x10, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F244 "" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfa, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xb1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x7, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xdf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa9, 0x3d, 0xf5,
    0x0, 0x0, 0x0, 0x4, 0x40, 0x2, 0xe0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0xf8, 0xb, 0x60,
    0x0, 0x0, 0x0, 0x6c, 0x30, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xaf, 0xf9,
    0x0, 0xc, 0x50, 0x0, 0x0, 0x6d, 0x40, 0x5,
    0x50, 0x0, 0x4, 0xc0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0x3e, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xef, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xb3, 0x0, 0x0, 0xaf, 0xfd, 0x8f,
    0xff, 0x20, 0x4, 0xff, 0xfd, 0x9, 0xff, 0xb0,
    0xa, 0xfe, 0xfd, 0x12, 0xaf, 0xf0, 0xe, 0xf5,
    0x5d, 0x2c, 0xe, 0xf3, 0xf, 0xff, 0x33, 0x12,
    0x9f, 0xf5, 0xf, 0xff, 0xf3, 0x7, 0xff, 0xf6,
    0xf, 0xff, 0xe2, 0x6, 0xff, 0xf6, 0xf, 0xfe,
    0x24, 0x13, 0x7f, 0xf5, 0xd, 0xf5, 0x7d, 0x2c,
    0xd, 0xf3, 0xa, 0xff, 0xfd, 0x11, 0xbf, 0xf0,
    0x3, 0xff, 0xfe, 0xb, 0xff, 0xa0, 0x0, 0x7f,
    0xfe, 0xbf, 0xfe, 0x10, 0x0, 0x3, 0xac, 0xdc,
    0x81, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x34, 0x43, 0x0, 0x0, 0x5, 0x66,
    0x7f, 0xff, 0xf9, 0x66, 0x50, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x35, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x50, 0x1c, 0xcc, 0xcc, 0xcc, 0xcc, 0xc4,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x2f,
    0xf3, 0xfb, 0x7f, 0x6d, 0xf6, 0x2, 0xff, 0x2f,
    0xb7, 0xf5, 0xdf, 0x60, 0x2f, 0xf2, 0xfb, 0x7f,
    0x5d, 0xf6, 0x2, 0xff, 0x2f, 0xb7, 0xf5, 0xdf,
    0x60, 0x2f, 0xf2, 0xfb, 0x7f, 0x5d, 0xf6, 0x2,
    0xff, 0x2f, 0xb7, 0xf5, 0xdf, 0x60, 0x2f, 0xf3,
    0xfb, 0x7f, 0x6d, 0xf6, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x7, 0xbc, 0xcc, 0xcc, 0xcc,
    0x90, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x4, 0x39, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x39, 0xff, 0xa0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x39, 0xb0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xa8, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x17, 0x88, 0x88, 0x88, 0x88, 0x87,
    0x40, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x3e, 0xff, 0xff, 0xcf, 0xff,
    0xcf, 0xff, 0xf7, 0x3, 0xef, 0xff, 0xf9, 0x8,
    0xf8, 0x9, 0xff, 0xf8, 0x3e, 0xff, 0xff, 0xfe,
    0x20, 0x40, 0x2e, 0xff, 0xf8, 0xdf, 0xff, 0xff,
    0xff, 0xe1, 0x1, 0xef, 0xff, 0xf8, 0x9f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x8f, 0xff, 0xf8, 0x9,
    0xff, 0xff, 0xf9, 0x2, 0xc2, 0x9, 0xff, 0xf8,
    0x0, 0x9f, 0xff, 0xfe, 0x4e, 0xfe, 0x4e, 0xff,
    0xf8, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc1,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xff, 0xe2, 0x3, 0xfb, 0xfb, 0xce, 0xbf,
    0xa4, 0xff, 0x1d, 0x3, 0xa1, 0xfa, 0xff, 0xf1,
    0xd0, 0x3a, 0x1f, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x29, 0xaa, 0xaa,
    0xaa, 0xa8, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf1, 0x0,
    0x8, 0x20, 0x0, 0x0, 0x1, 0xff, 0x10, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0xc, 0xff,
    0x94, 0x44, 0x44, 0x45, 0xff, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x7f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+FF0C "，" */
    0x7, 0x70, 0xc, 0xf0, 0x1, 0xd0, 0xc, 0x50,
    0x1, 0x0,

    /* U+FF1A "：" */
    0x7, 0x50, 0xeb, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x60, 0xeb, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 60, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 55, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17, .adv_w = 84, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 27, .adv_w = 148, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 82, .adv_w = 130, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 142, .adv_w = 179, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 203, .adv_w = 161, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 275, .adv_w = 45, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 281, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 316, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 351, .adv_w = 99, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 369, .adv_w = 130, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 401, .adv_w = 57, .box_w = 3, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 410, .adv_w = 110, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 419, .adv_w = 54, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 424, .adv_w = 90, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 457, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 501, .adv_w = 130, .box_w = 5, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 529, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 573, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 617, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 661, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 705, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 749, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 793, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 837, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 881, .adv_w = 59, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 893, .adv_w = 62, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 910, .adv_w = 130, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 946, .adv_w = 130, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 966, .adv_w = 130, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1002, .adv_w = 101, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1046, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1151, .adv_w = 153, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1206, .adv_w = 148, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1250, .adv_w = 148, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1300, .adv_w = 162, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1350, .adv_w = 134, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1389, .adv_w = 128, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1428, .adv_w = 158, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1483, .adv_w = 169, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1533, .adv_w = 62, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1544, .adv_w = 107, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1577, .adv_w = 157, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1627, .adv_w = 128, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1666, .adv_w = 195, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1721, .adv_w = 167, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1771, .adv_w = 172, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1832, .adv_w = 136, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1876, .adv_w = 172, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1948, .adv_w = 147, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1998, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2042, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2086, .adv_w = 166, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2136, .adv_w = 152, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2191, .adv_w = 219, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2268, .adv_w = 152, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2323, .adv_w = 142, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2373, .adv_w = 130, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2417, .adv_w = 80, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2449, .adv_w = 90, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2482, .adv_w = 80, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2514, .adv_w = 110, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2535, .adv_w = 98, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2546, .adv_w = 71, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 2552, .adv_w = 124, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2580, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2619, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2647, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2691, .adv_w = 123, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2723, .adv_w = 80, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2762, .adv_w = 137, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2806, .adv_w = 132, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2845, .adv_w = 56, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2862, .adv_w = 56, .box_w = 5, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2897, .adv_w = 121, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2936, .adv_w = 56, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2947, .adv_w = 193, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2991, .adv_w = 132, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3019, .adv_w = 133, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3051, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3090, .adv_w = 138, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3134, .adv_w = 88, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3154, .adv_w = 103, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3182, .adv_w = 86, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3215, .adv_w = 132, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3247, .adv_w = 119, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3279, .adv_w = 178, .box_w = 11, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3323, .adv_w = 114, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3355, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3399, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3427, .adv_w = 86, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3475, .adv_w = 44, .box_w = 3, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3496, .adv_w = 86, .box_w = 5, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3536, .adv_w = 130, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3556, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3654, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3745, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3836, .adv_w = 224, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3914, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4019, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4117, .adv_w = 224, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4202, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4286, .adv_w = 224, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4358, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4449, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4540, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4645, .adv_w = 224, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4730, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4821, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4926, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5024, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5129, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5220, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5311, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5402, .adv_w = 224, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5500, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5591, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5689, .adv_w = 224, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5767, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5865, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5970, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6068, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6166, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6257, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6370, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6447, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6538, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6615, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6670, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6775, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6880, .adv_w = 252, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6984, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7089, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7177, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7282, .adv_w = 112, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7324, .adv_w = 168, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7390, .adv_w = 252, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7502, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7579, .adv_w = 154, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7654, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7724, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7822, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7907, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7992, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 8062, .adv_w = 196, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 8153, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8212, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8271, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8356, .adv_w = 196, .box_w = 13, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 8382, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8470, .adv_w = 280, .box_w = 18, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8605, .adv_w = 252, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8733, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8824, .adv_w = 196, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8876, .adv_w = 196, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8928, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9027, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9104, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9209, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9322, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9407, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9505, .adv_w = 196, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9590, .adv_w = 196, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9668, .adv_w = 224, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9745, .adv_w = 140, .box_w = 10, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9820, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9918, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10016, .adv_w = 252, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10104, .adv_w = 224, .box_w = 16, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10224, .adv_w = 168, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10307, .adv_w = 280, .box_w = 18, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10424, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10514, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10604, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10694, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10784, .adv_w = 280, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10874, .adv_w = 280, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10982, .adv_w = 196, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11072, .adv_w = 196, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11170, .adv_w = 224, .box_w = 15, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 11283, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11382, .adv_w = 168, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11465, .adv_w = 225, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11540, .adv_w = 224, .box_w = 4, .box_h = 5, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11550, .adv_w = 224, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xbb, 0x32a, 0x35e, 0x466, 0x587, 0x5f4, 0x82d,
    0x8bf, 0x8f5, 0x905, 0xb90, 0xfb7, 0x10c5, 0x156a, 0x156c,
    0x1735, 0x18d2, 0x18ef, 0x18f1, 0x1f14, 0x240c, 0x240d, 0x3116,
    0x3474, 0x3b8d, 0x4158, 0x41a3, 0x4312, 0xa1c6, 0xa1cd, 0xa1d0,
    0xa1d1, 0xa1d2, 0xa1d6, 0xa1d8, 0xa1da, 0xa1de, 0xa1e1, 0xa1e6,
    0xa1eb, 0xa1ec, 0xa1ed, 0xa203, 0xa208, 0xa20d, 0xa210, 0xa211,
    0xa212, 0xa216, 0xa217, 0xa218, 0xa219, 0xa22c, 0xa22d, 0xa233,
    0xa235, 0xa236, 0xa239, 0xa23c, 0xa23d, 0xa23e, 0xa240, 0xa258,
    0xa25a, 0xa289, 0xa28a, 0xa28c, 0xa28e, 0xa2a5, 0xa2ac, 0xa2af,
    0xa2b8, 0xa2e1, 0xa2e9, 0xa320, 0xa3b0, 0xa405, 0xa406, 0xa407,
    0xa408, 0xa409, 0xa44c, 0xa458, 0xa4b2, 0xa4c9, 0xa71f, 0xa987,
    0xaa67, 0xb0d1, 0xb0df
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20027, .range_length = 45280, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 91, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 4, 4,
    2, 0, 3, 0, 0, 16, 0, 0,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 4, 4, -10,
    -32, -21, 5, -8, 0, -27, -2, 4,
    0, 0, 0, 0, 0, 0, -17, 0,
    -17, -6, 0, -11, -13, -2, -11, -10,
    -12, -10, -12, 0, 0, 0, -7, -22,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, -10, -8,
    0, 0, 0, -8, 0, -7, 0, -8,
    -4, -8, -13, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, -26, 0, -14, 6, 0, -15,
    -8, 0, 0, 0, -19, -3, -21, -15,
    0, -25, 5, 0, 0, -2, 0, 0,
    0, 0, 0, 0, -10, 0, -10, 0,
    0, -3, 0, 0, 0, -3, 0, 0,
    0, 3, 0, -8, 0, -10, -4, 0,
    -13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, -7, 5, 0, 7, -4, 0,
    0, 0, 1, 0, -1, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, -5, 0, 0, 0,
    0, 0, 2, 0, 3, -2, 0, 2,
    0, 0, 0, -2, 0, 0, -3, 0,
    -2, 0, -2, -3, 0, 0, -2, -2,
    -2, -5, -2, -5, 0, -2, 6, 0,
    1, -29, -13, 9, -1, 0, -31, 0,
    5, 0, 0, 0, 0, 0, 0, -9,
    0, -6, -2, 0, -4, 0, -3, 0,
    -5, -8, -5, -6, 0, 0, 0, 0,
    4, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -7, -3,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 0, 0, -24, 4, 0, -1,
    -13, -3, 0, -4, 0, -6, 0, 0,
    0, 0, 0, -6, 0, -8, -9, 0,
    -3, -3, -9, -9, -15, -8, -15, 0,
    -11, -23, 0, -20, 6, 0, -16, -11,
    0, 4, -2, -29, -10, -33, -25, 0,
    -40, 0, -2, 0, -4, -4, 0, 0,
    0, -6, -6, -21, 0, -21, 0, -2,
    2, 0, 3, -32, -18, 4, 0, 0,
    -36, 0, 0, 0, -1, -1, -6, 0,
    -7, -7, 0, -7, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, -2,
    0, -2, 9, 0, -1, -2, 0, 0,
    2, -3, -3, -6, -4, 0, -11, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 5,
    0, 0, -3, 0, 0, -5, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 7, 0, -18,
    -26, -19, 8, -7, 0, -31, 0, 5,
    0, 4, 4, 0, 0, 0, -27, 0,
    -25, -10, 0, -21, -25, -7, -20, -24,
    -24, -24, -19, -2, 4, 0, -5, -17,
    -15, 0, -4, 0, -16, 0, 4, 0,
    0, 0, 0, 0, 0, -17, 0, -14,
    -4, 0, -10, -10, 0, -8, -5, -7,
    -5, -8, 0, 0, 4, -21, 2, 0,
    3, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, -8, 0,
    0, -4, -4, -7, -7, -14, 0, -14,
    0, -7, 3, 4, -16, -31, -25, 2,
    -13, 0, -31, -6, 0, 0, 0, 0,
    0, 0, 0, -25, 0, -24, -11, 0,
    -19, -21, -8, -17, -17, -16, -17, -17,
    0, 0, 2, -10, 4, 0, 2, -6,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, -7, 0, -7, 0, 0,
    -9, 0, 0, 0, 0, -7, 0, 0,
    0, 0, -19, 0, -17, -15, -2, -22,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -3, 0, 0, -11,
    0, -2, -7, 0, -9, 0, 0, 0,
    0, -25, 0, -17, -14, -8, -24, 0,
    -2, 0, 0, -2, 0, 0, 0, -1,
    0, -5, -6, -5, -5, 0, 1, 0,
    4, 5, 0, -2, 0, 0, 0, 0,
    -17, 0, -12, -8, 4, -17, 0, 0,
    0, -1, 3, 0, 0, 0, 5, 0,
    0, 2, 0, 3, 0, 0, 3, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 3, -1,
    0, -6, 0, 0, 0, 0, -16, 0,
    -15, -11, -3, -21, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, -2,
    0, 0, 0, 11, 0, -2, -17, 0,
    10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, -5, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, -21, 0, -11, -10,
    0, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, -6, 5, 0, -11, 0, 0,
    0, 0, -21, 0, -14, -13, 0, -19,
    0, -6, 0, -5, 0, 0, 0, -2,
    0, -2, 0, 0, 0, 0, 0, 5,
    0, 2, -24, -11, -6, 0, 0, -26,
    0, 0, 0, -10, 0, -11, -17, 0,
    -9, 0, -7, 0, 0, 0, -1, 6,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    -23, 0, -16, -11, -1, -24, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    -2, 0, -2, 3, 0, -2, 2, 0,
    6, 0, -5, 0, 0, 0, 0, -15,
    0, -11, 0, 0, -15, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -20,
    -10, -6, 0, 0, -18, 0, -24, 0,
    -10, -5, -14, -17, 0, -4, 0, -5,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 2, 0,
    -9, 0, 0, 0, 0, -24, 0, -12,
    -7, 0, -16, 0, -4, 0, -6, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, -7,
    0, 0, 0, 0, -22, 0, -13, -7,
    0, -17, 0, -3, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_14 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_14 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 14,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_14*/

