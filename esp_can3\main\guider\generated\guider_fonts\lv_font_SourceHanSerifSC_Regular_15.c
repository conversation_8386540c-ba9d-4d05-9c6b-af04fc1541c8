/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SOURCEHANSERIFSC_REGULAR_15
#define LV_FONT_SOURCEHANSERIFSC_REGULAR_15 1
#endif

#if LV_FONT_SOURCEHANSERIFSC_REGULAR_15

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x5e, 0x6, 0xf0, 0x5f, 0x3, 0xd0, 0x1b, 0x0,
    0xa0, 0x8, 0x0, 0x40, 0x0, 0x3, 0xa0, 0x5e,
    0x10,

    /* U+0022 "\"" */
    0x1e, 0x6, 0xb2, 0xf1, 0x7c, 0xf, 0x5, 0xb0,
    0xc0, 0x39, 0x7, 0x1, 0x60,

    /* U+0023 "#" */
    0x0, 0x8, 0x0, 0x80, 0x0, 0x1, 0x70, 0x8,
    0x0, 0x0, 0x34, 0x0, 0x80, 0x1, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x80, 0x2, 0x60, 0x0, 0x8,
    0x0, 0x44, 0x0, 0x0, 0x80, 0x6, 0x20, 0x9,
    0xff, 0xff, 0xff, 0xb0, 0x1, 0x70, 0x8, 0x0,
    0x0, 0x34, 0x0, 0x80, 0x0, 0x6, 0x20, 0x7,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x80, 0x0,
    0x0, 0x8a, 0xc8, 0x40, 0x7, 0x80, 0x82, 0xf1,
    0xc, 0x40, 0x80, 0x92, 0xb, 0x90, 0x80, 0x0,
    0x3, 0xfa, 0x90, 0x0, 0x0, 0x3b, 0xfa, 0x10,
    0x0, 0x0, 0xac, 0xd0, 0x0, 0x0, 0x70, 0xe5,
    0xa, 0x0, 0x70, 0xb5, 0xd, 0x40, 0x71, 0xd1,
    0x2, 0x88, 0xca, 0x20, 0x0, 0x0, 0x70, 0x0,
    0x0, 0x0, 0x30, 0x0,

    /* U+0025 "%" */
    0x5, 0x78, 0x20, 0x0, 0x0, 0x60, 0x0, 0xd,
    0x1, 0xb0, 0x0, 0x6, 0x30, 0x0, 0x3b, 0x0,
    0xe0, 0x0, 0x27, 0x0, 0x0, 0x4a, 0x0, 0xe0,
    0x0, 0x80, 0x0, 0x0, 0x2b, 0x0, 0xe0, 0x6,
    0x21, 0x66, 0x0, 0xc, 0x3, 0x90, 0x26, 0xb,
    0x12, 0xa0, 0x1, 0x66, 0x0, 0x80, 0x1c, 0x0,
    0xd0, 0x0, 0x0, 0x7, 0x20, 0x3b, 0x0, 0xc2,
    0x0, 0x0, 0x35, 0x0, 0x2c, 0x0, 0xd1, 0x0,
    0x0, 0x80, 0x0, 0xd, 0x0, 0xc0, 0x0, 0x7,
    0x10, 0x0, 0x4, 0x88, 0x30, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x3a, 0x8a, 0x30, 0x0, 0x0, 0x0, 0xe1,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0xe0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0xc5, 0x9, 0x30, 0x0, 0x0,
    0x0, 0x2e, 0x72, 0x1, 0x7c, 0x70, 0x1, 0x97,
    0xe3, 0x0, 0x9, 0x0, 0xc, 0x30, 0x6e, 0x20,
    0x45, 0x0, 0x4d, 0x0, 0x7, 0xe2, 0x80, 0x0,
    0x4e, 0x0, 0x0, 0x9e, 0x40, 0x0, 0xe, 0x80,
    0x0, 0x6c, 0xd1, 0x0, 0x2, 0xbd, 0xa9, 0x20,
    0x9d, 0x80,

    /* U+0027 "'" */
    0x1e, 0x2, 0xf1, 0xf, 0x0, 0xc0, 0x7, 0x0,

    /* U+0028 "(" */
    0x0, 0x1, 0x0, 0x1, 0x80, 0x0, 0xa1, 0x0,
    0x57, 0x0, 0xd, 0x10, 0x2, 0xc0, 0x0, 0x69,
    0x0, 0x8, 0x70, 0x0, 0x97, 0x0, 0x8, 0x80,
    0x0, 0x69, 0x0, 0x2, 0xd0, 0x0, 0xc, 0x10,
    0x0, 0x48, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x80,
    0x0, 0x0, 0x0,

    /* U+0029 ")" */
    0x10, 0x0, 0x3, 0x60, 0x0, 0x8, 0x30, 0x0,
    0x1c, 0x0, 0x0, 0x94, 0x0, 0x5, 0xa0, 0x0,
    0x1e, 0x0, 0x0, 0xf0, 0x0, 0xf, 0x10, 0x0,
    0xf0, 0x0, 0x1e, 0x0, 0x5, 0x90, 0x0, 0xa4,
    0x0, 0x1b, 0x0, 0x9, 0x20, 0x3, 0x50, 0x0,
    0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0xc, 0x10, 0x0, 0x0, 0xc0, 0x0, 0x8b,
    0x38, 0x2a, 0xb0, 0x35, 0xb6, 0x30, 0x0, 0x93,
    0xb0, 0x0, 0x7a, 0x7, 0xa0, 0x1, 0x0, 0x2,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0xa, 0x0,
    0x0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0x0, 0xa,
    0x0, 0x0, 0x49, 0x99, 0xe9, 0x99, 0x10, 0x0,
    0xa, 0x0, 0x0, 0x0, 0x0, 0xa0, 0x0, 0x0,
    0x0, 0xa, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0,
    0x0,

    /* U+002C "," */
    0x4, 0xe3, 0x2, 0xe4, 0x0, 0x91, 0x6, 0x30,
    0x0, 0x0,

    /* U+002D "-" */
    0x4c, 0xcc, 0x60,

    /* U+002E "." */
    0x3b, 0x24, 0xe3,

    /* U+002F "/" */
    0x0, 0x0, 0x90, 0x0, 0x0, 0xa0, 0x0, 0x4,
    0x60, 0x0, 0x9, 0x10, 0x0, 0xa, 0x0, 0x0,
    0x37, 0x0, 0x0, 0x82, 0x0, 0x0, 0xa0, 0x0,
    0x2, 0x80, 0x0, 0x7, 0x30, 0x0, 0xa, 0x0,
    0x0, 0x19, 0x0, 0x0, 0x64, 0x0, 0x0, 0xa0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x59, 0x89, 0x0, 0x5, 0xb0, 0x6, 0xa0,
    0xc, 0x50, 0x0, 0xf2, 0x1f, 0x10, 0x0, 0xc6,
    0x2f, 0x0, 0x0, 0xb8, 0x4f, 0x0, 0x0, 0xa9,
    0x2f, 0x0, 0x0, 0xb8, 0x1f, 0x10, 0x0, 0xc6,
    0xc, 0x50, 0x0, 0xf2, 0x5, 0xb0, 0x6, 0xa0,
    0x0, 0x58, 0x79, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x0, 0x69, 0xe7, 0x0, 0x10, 0xc7,
    0x0, 0x0, 0xc7, 0x0, 0x0, 0xc6, 0x0, 0x0,
    0xc6, 0x0, 0x0, 0xc6, 0x0, 0x0, 0xc6, 0x0,
    0x0, 0xc7, 0x0, 0x0, 0xc7, 0x0, 0x0, 0xc7,
    0x0, 0x69, 0xec, 0x82,

    /* U+0032 "2" */
    0x0, 0x77, 0x8b, 0x30, 0xb, 0x70, 0x6, 0xe0,
    0x9, 0x10, 0x1, 0xf2, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x2d, 0x10,
    0x0, 0x0, 0xb2, 0x0, 0x0, 0x9, 0x20, 0x0,
    0x0, 0x82, 0x0, 0x0, 0x6, 0x40, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfa,

    /* U+0033 "3" */
    0x1, 0x87, 0x9a, 0x10, 0xb, 0x60, 0x7, 0xc0,
    0x5, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x5, 0xe0,
    0x0, 0x0, 0x1c, 0x50, 0x0, 0x29, 0xd6, 0x0,
    0x0, 0x0, 0x8, 0xb0, 0x0, 0x0, 0x0, 0xf4,
    0x4, 0x0, 0x0, 0xf5, 0x1f, 0x10, 0x5, 0xe1,
    0x4, 0x87, 0x8a, 0x20,

    /* U+0034 "4" */
    0x0, 0x0, 0x1e, 0x30, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x4, 0x3e, 0x30, 0x0, 0x1, 0x70,
    0xe3, 0x0, 0x0, 0x70, 0xe, 0x30, 0x0, 0x43,
    0x0, 0xe3, 0x0, 0x17, 0x0, 0xe, 0x30, 0x6,
    0xed, 0xdd, 0xfe, 0xd0, 0x0, 0x0, 0xe, 0x30,
    0x0, 0x0, 0x0, 0xe3, 0x0, 0x0, 0x0, 0xe,
    0x30, 0x0,

    /* U+0035 "5" */
    0x2, 0xff, 0xff, 0xf3, 0x3, 0x50, 0x0, 0x0,
    0x4, 0x40, 0x0, 0x0, 0x5, 0x20, 0x0, 0x0,
    0x6, 0x98, 0xa8, 0x10, 0x0, 0x0, 0x9, 0xd0,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0xd7,
    0x16, 0x0, 0x0, 0xe5, 0x1f, 0x10, 0x6, 0xd0,
    0x4, 0x87, 0x89, 0x10,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x77, 0x40,
    0x0, 0x1a, 0x50, 0x0, 0x0, 0xb6, 0x0, 0x0,
    0x5, 0xc0, 0x0, 0x0, 0xc, 0x77, 0x9a, 0x30,
    0xf, 0x80, 0x4, 0xf1, 0x1f, 0x20, 0x0, 0xc7,
    0xf, 0x30, 0x0, 0xa9, 0xd, 0x60, 0x0, 0xb7,
    0x6, 0xc0, 0x1, 0xe1, 0x0, 0x69, 0x79, 0x20,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x90,
    0x0, 0x0, 0x1, 0x90, 0x0, 0x0, 0x7, 0x30,
    0x0, 0x0, 0xc, 0x0, 0x0, 0x0, 0x67, 0x0,
    0x0, 0x0, 0xd1, 0x0, 0x0, 0x4, 0xb0, 0x0,
    0x0, 0xb, 0x50, 0x0, 0x0, 0x3e, 0x0, 0x0,
    0x0, 0xa9, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x88, 0x8a, 0x20, 0x8, 0x80, 0x4, 0xd0,
    0xc, 0x50, 0x0, 0xf0, 0xa, 0x90, 0x3, 0xc0,
    0x2, 0xe7, 0x18, 0x10, 0x0, 0x2b, 0xe8, 0x0,
    0x3, 0x80, 0xa, 0xd0, 0xe, 0x20, 0x0, 0xd6,
    0x1f, 0x0, 0x0, 0xa7, 0xd, 0x40, 0x0, 0xd2,
    0x2, 0xa8, 0x79, 0x40,

    /* U+0039 "9" */
    0x0, 0x88, 0x79, 0x10, 0x9, 0x80, 0x5, 0xc0,
    0xf, 0x20, 0x0, 0xe4, 0x2f, 0x10, 0x0, 0xc7,
    0xf, 0x30, 0x0, 0xb8, 0x9, 0xc1, 0x2, 0xd6,
    0x0, 0x6a, 0x86, 0xf3, 0x0, 0x0, 0x5, 0xc0,
    0x0, 0x0, 0x1e, 0x30, 0x0, 0x1, 0xb4, 0x0,
    0x0, 0x68, 0x20, 0x0, 0x2, 0x10, 0x0, 0x0,

    /* U+003A ":" */
    0x4e, 0x23, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3b, 0x24, 0xe3,

    /* U+003B ";" */
    0x4, 0xe2, 0x3, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x1, 0xc4,
    0x0, 0x90, 0x7, 0x20, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x4, 0xa3,
    0x0, 0x2, 0xa6, 0x0, 0x1, 0x88, 0x0, 0x0,
    0xd, 0x30, 0x0, 0x0, 0x2, 0xa6, 0x0, 0x0,
    0x0, 0x4, 0xa4, 0x0, 0x0, 0x0, 0x7, 0xa2,
    0x0, 0x0, 0x0, 0x14,

    /* U+003D "=" */
    0x49, 0x99, 0x99, 0x99, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x99, 0x99,
    0x99, 0x91,

    /* U+003E ">" */
    0x2, 0x0, 0x0, 0x0, 0x7, 0xa2, 0x0, 0x0,
    0x0, 0x19, 0x80, 0x0, 0x0, 0x0, 0x2a, 0x60,
    0x0, 0x0, 0x0, 0x6a, 0x0, 0x0, 0x18, 0x81,
    0x0, 0x6, 0xa2, 0x0, 0x4, 0xa4, 0x0, 0x0,
    0x5, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x46, 0x0, 0x8, 0xfe, 0x50, 0x0, 0x28, 0x30,
    0x0, 0x18, 0x0, 0x6, 0x60, 0x2a, 0xc1, 0xe,
    0x60, 0x0, 0x90, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0x10, 0x4, 0xe2, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x17, 0x77, 0x78, 0x40, 0x0, 0x0,
    0x4, 0x80, 0x0, 0x0, 0x57, 0x0, 0x0, 0x65,
    0x0, 0x0, 0x0, 0x5, 0x40, 0x1, 0x90, 0x1,
    0x99, 0x88, 0x0, 0x80, 0x9, 0x10, 0x1c, 0x10,
    0x78, 0x0, 0x80, 0xa, 0x0, 0x86, 0x0, 0xa5,
    0x0, 0x80, 0x48, 0x0, 0xe1, 0x0, 0xd1, 0x0,
    0x80, 0x55, 0x0, 0xf0, 0x0, 0xe0, 0x1, 0x70,
    0x46, 0x0, 0xf5, 0x17, 0xd0, 0x8, 0x0, 0x19,
    0x0, 0x4b, 0x70, 0x68, 0x60, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0x20, 0x0,
    0x14, 0x0, 0x0, 0x0, 0x1, 0x68, 0x77, 0x50,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x3e, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xac, 0x90, 0x0,
    0x0, 0x0, 0x37, 0x7e, 0x0, 0x0, 0x0, 0x9,
    0x12, 0xf4, 0x0, 0x0, 0x0, 0xb0, 0xd, 0x90,
    0x0, 0x0, 0x37, 0x0, 0x7e, 0x0, 0x0, 0x9,
    0x87, 0x78, 0xf4, 0x0, 0x0, 0xb0, 0x0, 0xc,
    0x90, 0x0, 0x36, 0x0, 0x0, 0x6e, 0x0, 0x5b,
    0x93, 0x0, 0x48, 0xf9, 0x40,

    /* U+0042 "B" */
    0x18, 0xdd, 0x88, 0xb7, 0x0, 0x0, 0xab, 0x0,
    0xd, 0x70, 0x0, 0xaa, 0x0, 0x8, 0xc0, 0x0,
    0xaa, 0x0, 0x9, 0xa0, 0x0, 0xaa, 0x0, 0x4d,
    0x20, 0x0, 0xad, 0x7a, 0xb3, 0x0, 0x0, 0xaa,
    0x0, 0x1a, 0xa0, 0x0, 0xaa, 0x0, 0x1, 0xf4,
    0x0, 0xaa, 0x0, 0x0, 0xf4, 0x0, 0xab, 0x0,
    0x7, 0xd0, 0x18, 0xdd, 0x78, 0x99, 0x10,

    /* U+0043 "C" */
    0x0, 0x3, 0xa9, 0x89, 0x81, 0x0, 0x7c, 0x20,
    0x0, 0x87, 0x4, 0xf1, 0x0, 0x0, 0x57, 0xc,
    0x90, 0x0, 0x0, 0x1, 0xf, 0x60, 0x0, 0x0,
    0x0, 0x1f, 0x40, 0x0, 0x0, 0x0, 0xf, 0x60,
    0x0, 0x0, 0x0, 0xc, 0x90, 0x0, 0x0, 0x1,
    0x4, 0xf1, 0x0, 0x0, 0x39, 0x0, 0x8c, 0x10,
    0x0, 0x69, 0x0, 0x3, 0xa8, 0x89, 0x92,

    /* U+0044 "D" */
    0x18, 0xdd, 0x78, 0x89, 0x10, 0x0, 0xa, 0xb0,
    0x0, 0x4d, 0x40, 0x0, 0xab, 0x0, 0x0, 0x6d,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0xf5, 0x0, 0xab,
    0x0, 0x0, 0xd, 0x80, 0xa, 0xa0, 0x0, 0x0,
    0xca, 0x0, 0xaa, 0x0, 0x0, 0xd, 0x80, 0xa,
    0xa0, 0x0, 0x0, 0xf5, 0x0, 0xab, 0x0, 0x0,
    0x7d, 0x0, 0xa, 0xb0, 0x0, 0x5d, 0x30, 0x18,
    0xdd, 0x88, 0x98, 0x0, 0x0,

    /* U+0045 "E" */
    0x18, 0xdd, 0x88, 0x89, 0xc0, 0x0, 0xab, 0x0,
    0x0, 0xc0, 0x0, 0xab, 0x0, 0x0, 0x60, 0x0,
    0xab, 0x0, 0x40, 0x0, 0x0, 0xab, 0x0, 0x90,
    0x0, 0x0, 0xad, 0x77, 0xd0, 0x0, 0x0, 0xaa,
    0x0, 0x80, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0x0, 0xab, 0x0, 0x0, 0x81, 0x0, 0xab, 0x0,
    0x0, 0xc1, 0x18, 0xdd, 0x88, 0x88, 0xf0,

    /* U+0046 "F" */
    0x18, 0xdd, 0x88, 0x89, 0xd0, 0xa, 0xb0, 0x0,
    0xc, 0x0, 0xab, 0x0, 0x0, 0x50, 0xa, 0xb0,
    0x5, 0x10, 0x0, 0xab, 0x0, 0x91, 0x0, 0xa,
    0xd7, 0x7c, 0x10, 0x0, 0xaa, 0x0, 0x81, 0x0,
    0xa, 0xa0, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0x18, 0xde, 0x93,
    0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3, 0xa9, 0x88, 0x92, 0x0, 0x7, 0xc2,
    0x0, 0x5, 0x90, 0x4, 0xf2, 0x0, 0x0, 0x2a,
    0x0, 0xc9, 0x0, 0x0, 0x0, 0x10, 0xf, 0x60,
    0x0, 0x0, 0x0, 0x1, 0xf4, 0x0, 0x1, 0x44,
    0x43, 0xf, 0x60, 0x0, 0x3, 0xae, 0x30, 0xc9,
    0x0, 0x0, 0x7, 0xd0, 0x4, 0xf1, 0x0, 0x0,
    0x7d, 0x0, 0x8, 0xc1, 0x0, 0x7, 0xc0, 0x0,
    0x3, 0x99, 0x89, 0x83, 0x0,

    /* U+0048 "H" */
    0x18, 0xde, 0x81, 0x3, 0x8f, 0xc8, 0x0, 0xa,
    0xb0, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xab, 0x0,
    0x0, 0xe, 0x70, 0x0, 0xa, 0xb0, 0x0, 0x0,
    0xe7, 0x0, 0x0, 0xab, 0x0, 0x0, 0xd, 0x70,
    0x0, 0xa, 0xd7, 0x77, 0x77, 0xe6, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0xd, 0x70, 0x0, 0xa, 0xa0,
    0x0, 0x0, 0xd7, 0x0, 0x0, 0xab, 0x0, 0x0,
    0xd, 0x70, 0x0, 0xa, 0xb0, 0x0, 0x0, 0xe7,
    0x0, 0x17, 0xdd, 0x71, 0x3, 0x8f, 0xc7, 0x0,

    /* U+0049 "I" */
    0x18, 0xde, 0x82, 0x0, 0xab, 0x0, 0x0, 0xab,
    0x0, 0x0, 0xab, 0x0, 0x0, 0xab, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0xab, 0x0, 0x0, 0xab, 0x0,
    0x0, 0xab, 0x0, 0x0, 0xab, 0x0, 0x18, 0xde,
    0x82,

    /* U+004A "J" */
    0x1, 0x8c, 0xf8, 0x30, 0x0, 0x6e, 0x0, 0x0,
    0x6, 0xe0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x0, 0x5e, 0x0, 0x0, 0x5, 0xe0,
    0x0, 0x0, 0x5e, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x6d, 0x0, 0x0, 0x7, 0xb0, 0x0, 0x0,
    0xa5, 0x0, 0x1f, 0xb9, 0x0, 0x0, 0x22, 0x0,
    0x0,

    /* U+004B "K" */
    0x18, 0xdd, 0x81, 0x18, 0xd9, 0x40, 0xa, 0xa0,
    0x0, 0x46, 0x0, 0x0, 0xaa, 0x0, 0x28, 0x0,
    0x0, 0xa, 0xa0, 0x9, 0x0, 0x0, 0x0, 0xaa,
    0xb, 0x60, 0x0, 0x0, 0xa, 0xa7, 0x9e, 0x0,
    0x0, 0x0, 0xae, 0x40, 0xd7, 0x0, 0x0, 0xa,
    0xa0, 0x5, 0xf1, 0x0, 0x0, 0xaa, 0x0, 0xc,
    0x90, 0x0, 0xa, 0xa0, 0x0, 0x3f, 0x20, 0x18,
    0xdd, 0x81, 0x8, 0xed, 0x70,

    /* U+004C "L" */
    0x18, 0xdd, 0x82, 0x0, 0x0, 0xa, 0xb0, 0x0,
    0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0xa, 0xb0,
    0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0xa,
    0xa0, 0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0xa, 0xb0, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0xa0, 0xa, 0xb0, 0x0, 0x1c, 0x18, 0xdd, 0x88,
    0x8a, 0xb0,

    /* U+004D "M" */
    0x28, 0xfb, 0x0, 0x0, 0x0, 0x4f, 0xa6, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0x9f, 0x40, 0x0, 0x8d,
    0x80, 0x0, 0x1, 0x7f, 0x40, 0x0, 0x86, 0xe0,
    0x0, 0x7, 0x1f, 0x40, 0x0, 0x81, 0xf5, 0x0,
    0x9, 0xf, 0x40, 0x0, 0x90, 0x9b, 0x0, 0x44,
    0xf, 0x40, 0x0, 0x90, 0x3f, 0x20, 0x90, 0xf,
    0x40, 0x0, 0x90, 0xc, 0x81, 0x70, 0xf, 0x40,
    0x0, 0x90, 0x6, 0xe7, 0x10, 0xf, 0x40, 0x0,
    0x90, 0x0, 0xfa, 0x0, 0xf, 0x40, 0x28, 0xd8,
    0x20, 0x94, 0x4, 0x9f, 0xa6,

    /* U+004E "N" */
    0x27, 0xf9, 0x0, 0x3, 0x8d, 0x82, 0x0, 0xcf,
    0x40, 0x0, 0x8, 0x0, 0x0, 0x98, 0xe1, 0x0,
    0x8, 0x0, 0x0, 0x90, 0xda, 0x0, 0x8, 0x0,
    0x0, 0x90, 0x3f, 0x40, 0x8, 0x0, 0x0, 0x90,
    0x8, 0xe1, 0x8, 0x0, 0x0, 0x90, 0x0, 0xca,
    0x8, 0x0, 0x0, 0x90, 0x0, 0x2f, 0x48, 0x0,
    0x0, 0x90, 0x0, 0x7, 0xe9, 0x0, 0x0, 0x90,
    0x0, 0x0, 0xce, 0x0, 0x28, 0xd8, 0x30, 0x0,
    0x2d, 0x0,

    /* U+004F "O" */
    0x0, 0x5, 0xa9, 0x99, 0x10, 0x0, 0x9, 0xa0,
    0x0, 0x3e, 0x30, 0x5, 0xe0, 0x0, 0x0, 0x7d,
    0x0, 0xc8, 0x0, 0x0, 0x1, 0xf4, 0xf, 0x60,
    0x0, 0x0, 0xe, 0x71, 0xf4, 0x0, 0x0, 0x0,
    0xc9, 0xf, 0x60, 0x0, 0x0, 0xe, 0x70, 0xd8,
    0x0, 0x0, 0x1, 0xf4, 0x5, 0xe0, 0x0, 0x0,
    0x7d, 0x0, 0xa, 0x90, 0x0, 0x3d, 0x30, 0x0,
    0x6, 0x98, 0x89, 0x10, 0x0,

    /* U+0050 "P" */
    0x18, 0xdd, 0x88, 0xa6, 0x0, 0x0, 0xab, 0x0,
    0xc, 0x80, 0x0, 0xab, 0x0, 0x5, 0xe0, 0x0,
    0xab, 0x0, 0x4, 0xf0, 0x0, 0xab, 0x0, 0x7,
    0xc0, 0x0, 0xaa, 0x0, 0x4e, 0x30, 0x0, 0xad,
    0x88, 0x71, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0,
    0x0, 0xab, 0x0, 0x0, 0x0, 0x0, 0xab, 0x0,
    0x0, 0x0, 0x17, 0xdd, 0x82, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x5, 0xa9, 0x99, 0x10, 0x0, 0x8, 0xb0,
    0x0, 0x4d, 0x20, 0x3, 0xf0, 0x0, 0x0, 0x8c,
    0x0, 0xc9, 0x0, 0x0, 0x2, 0xf4, 0xf, 0x60,
    0x0, 0x0, 0xe, 0x71, 0xf4, 0x0, 0x0, 0x0,
    0xd9, 0xf, 0x50, 0x0, 0x0, 0xd, 0x80, 0xe7,
    0x0, 0x0, 0x0, 0xf6, 0xa, 0xb0, 0x0, 0x0,
    0x3f, 0x20, 0x2f, 0x20, 0x0, 0xa, 0x90, 0x0,
    0x4d, 0x30, 0x18, 0xa0, 0x0, 0x0, 0x16, 0xea,
    0x30, 0x0, 0x0, 0x0, 0xc, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0x75, 0x0,

    /* U+0052 "R" */
    0x18, 0xdd, 0x78, 0xa9, 0x10, 0x0, 0xa, 0xb0,
    0x0, 0x8c, 0x0, 0x0, 0xaa, 0x0, 0x2, 0xf1,
    0x0, 0xa, 0xa0, 0x0, 0x4f, 0x0, 0x0, 0xaa,
    0x0, 0x1c, 0x60, 0x0, 0xa, 0xd7, 0x9c, 0x20,
    0x0, 0x0, 0xaa, 0x0, 0x8a, 0x0, 0x0, 0xa,
    0xa0, 0x1, 0xf2, 0x0, 0x0, 0xaa, 0x0, 0xc,
    0x70, 0x0, 0xa, 0xb0, 0x0, 0x8c, 0x0, 0x18,
    0xde, 0x82, 0x1, 0xd8, 0x30,

    /* U+0053 "S" */
    0x0, 0x7b, 0x9a, 0x81, 0x8, 0x80, 0x0, 0xb3,
    0xd, 0x40, 0x0, 0x72, 0xc, 0xa0, 0x0, 0x0,
    0x4, 0xfb, 0x30, 0x0, 0x0, 0x3b, 0xfc, 0x30,
    0x0, 0x0, 0x2a, 0xf4, 0x0, 0x0, 0x0, 0xaa,
    0x29, 0x0, 0x0, 0x7a, 0x2c, 0x0, 0x0, 0xc4,
    0x7, 0x88, 0x8a, 0x40,

    /* U+0054 "T" */
    0x9b, 0x88, 0xed, 0x88, 0xc7, 0x93, 0x0, 0xb9,
    0x0, 0x58, 0x80, 0x0, 0xb9, 0x0, 0x27, 0x0,
    0x0, 0xb9, 0x0, 0x0, 0x0, 0x0, 0xb9, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x0, 0x0, 0x0, 0x0,
    0xb9, 0x0, 0x0, 0x0, 0x0, 0xb9, 0x0, 0x0,
    0x0, 0x0, 0xb9, 0x0, 0x0, 0x0, 0x0, 0xb9,
    0x0, 0x0, 0x0, 0x38, 0xed, 0x82, 0x0,

    /* U+0055 "U" */
    0x28, 0xfc, 0x70, 0x3, 0x8d, 0x82, 0x0, 0xe7,
    0x0, 0x0, 0x9, 0x0, 0x0, 0xe7, 0x0, 0x0,
    0x9, 0x0, 0x0, 0xe7, 0x0, 0x0, 0x9, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0x9, 0x0, 0x0, 0xd7,
    0x0, 0x0, 0x9, 0x0, 0x0, 0xd7, 0x0, 0x0,
    0x9, 0x0, 0x0, 0xc9, 0x0, 0x0, 0x8, 0x0,
    0x0, 0x9c, 0x0, 0x0, 0x18, 0x0, 0x0, 0x2f,
    0x70, 0x1, 0xa1, 0x0, 0x0, 0x3, 0xbd, 0xcb,
    0x20, 0x0,

    /* U+0056 "V" */
    0x7c, 0xe8, 0x30, 0x8, 0xc9, 0x40, 0x4f, 0x10,
    0x0, 0xa, 0x0, 0x0, 0xe6, 0x0, 0x1, 0x80,
    0x0, 0x9, 0xc0, 0x0, 0x63, 0x0, 0x0, 0x4f,
    0x10, 0x9, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x80,
    0x0, 0x0, 0x8, 0xb0, 0x53, 0x0, 0x0, 0x0,
    0x3f, 0x19, 0x0, 0x0, 0x0, 0x0, 0xd7, 0x80,
    0x0, 0x0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x6b, 0xf8, 0x30, 0x6b, 0xe8, 0x20, 0x7c, 0xa5,
    0x3, 0xf1, 0x0, 0x8, 0xf0, 0x0, 0x9, 0x0,
    0x0, 0xe5, 0x0, 0x8, 0xe4, 0x0, 0x9, 0x0,
    0x0, 0xaa, 0x0, 0x26, 0xa9, 0x0, 0x36, 0x0,
    0x0, 0x6e, 0x0, 0x71, 0x5e, 0x0, 0x71, 0x0,
    0x0, 0x1f, 0x20, 0x80, 0xf, 0x30, 0x90, 0x0,
    0x0, 0xd, 0x70, 0x70, 0xb, 0x70, 0x80, 0x0,
    0x0, 0x8, 0xb5, 0x30, 0x6, 0xc4, 0x40, 0x0,
    0x0, 0x4, 0xf8, 0x0, 0x1, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xf9, 0x0, 0x0, 0xdb, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0x0, 0x0, 0x87, 0x0, 0x0,

    /* U+0058 "X" */
    0x4a, 0xfa, 0x50, 0x4a, 0xc8, 0x0, 0xc, 0xa0,
    0x0, 0x81, 0x0, 0x0, 0x3f, 0x40, 0x35, 0x0,
    0x0, 0x0, 0x9d, 0x8, 0x0, 0x0, 0x0, 0x1,
    0xeb, 0x20, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x90, 0x0, 0x0, 0x0,
    0x71, 0x3f, 0x30, 0x0, 0x0, 0x27, 0x0, 0xac,
    0x0, 0x0, 0x9, 0x0, 0x1, 0xf6, 0x0, 0x6a,
    0xc7, 0x0, 0x8d, 0xe8, 0x10,

    /* U+0059 "Y" */
    0x4b, 0xf9, 0x40, 0x39, 0xd8, 0x0, 0x1f, 0x60,
    0x0, 0x54, 0x0, 0x0, 0x8e, 0x0, 0x9, 0x0,
    0x0, 0x1, 0xf5, 0x4, 0x40, 0x0, 0x0, 0x8,
    0xd0, 0x90, 0x0, 0x0, 0x0, 0x1f, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7d, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xd0, 0x0, 0x0, 0x0,
    0x18, 0xce, 0x84, 0x0, 0x0,

    /* U+005A "Z" */
    0x4d, 0x88, 0x88, 0xaf, 0x44, 0x80, 0x0, 0xb,
    0xb0, 0x45, 0x0, 0x5, 0xf1, 0x0, 0x0, 0x1,
    0xe6, 0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x1e, 0x70, 0x0, 0x0,
    0xa, 0xc0, 0x0, 0x0, 0x4, 0xf2, 0x0, 0x1,
    0x80, 0xe8, 0x0, 0x0, 0x49, 0x8f, 0x88, 0x88,
    0x8b, 0x80,

    /* U+005B "[" */
    0x6e, 0x94, 0x5b, 0x0, 0x5b, 0x0, 0x5a, 0x0,
    0x5a, 0x0, 0x5a, 0x0, 0x5a, 0x0, 0x5a, 0x0,
    0x5a, 0x0, 0x5a, 0x0, 0x5a, 0x0, 0x5b, 0x0,
    0x5b, 0x0, 0x6b, 0x10, 0x37, 0x74,

    /* U+005C "\\" */
    0xa0, 0x0, 0x0, 0x64, 0x0, 0x0, 0x19, 0x0,
    0x0, 0xa, 0x0, 0x0, 0x7, 0x30, 0x0, 0x2,
    0x80, 0x0, 0x0, 0xa0, 0x0, 0x0, 0x82, 0x0,
    0x0, 0x37, 0x0, 0x0, 0xa, 0x0, 0x0, 0x9,
    0x10, 0x0, 0x4, 0x50, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0xa0,

    /* U+005D "]" */
    0x39, 0xd8, 0x0, 0x88, 0x0, 0x88, 0x0, 0x88,
    0x0, 0x88, 0x0, 0x87, 0x0, 0x87, 0x0, 0x87,
    0x0, 0x87, 0x0, 0x88, 0x0, 0x88, 0x0, 0x88,
    0x0, 0x88, 0x1, 0x98, 0x37, 0x74,

    /* U+005E "^" */
    0x0, 0x4d, 0x10, 0x0, 0xb, 0x39, 0x0, 0x7,
    0x40, 0x83, 0x2, 0x90, 0x0, 0xb0, 0x41, 0x0,
    0x3, 0x20,

    /* U+005F "_" */
    0xb, 0xbb, 0xbb, 0xbb, 0xb6,

    /* U+0060 "`" */
    0x79, 0x0, 0x1c, 0x40, 0x0, 0x90, 0x0, 0x0,

    /* U+0061 "a" */
    0x2, 0x87, 0x9a, 0x0, 0x0, 0xd6, 0x0, 0xb7,
    0x0, 0x5, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x56,
    0xba, 0x0, 0x6, 0xa2, 0x8, 0xa0, 0x1, 0xf2,
    0x0, 0x8a, 0x0, 0x2f, 0x30, 0x2b, 0xb0, 0x0,
    0x7d, 0xa6, 0x2d, 0x90,

    /* U+0062 "b" */
    0x2, 0x50, 0x0, 0x0, 0x3, 0x8f, 0x20, 0x0,
    0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x1f, 0x10,
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x0, 0x1f,
    0x38, 0xbc, 0x40, 0x1, 0xf6, 0x0, 0x3f, 0x30,
    0x1f, 0x20, 0x0, 0xb9, 0x1, 0xf2, 0x0, 0x8,
    0xc0, 0x1f, 0x20, 0x0, 0x8c, 0x1, 0xf2, 0x0,
    0xb, 0x90, 0x1f, 0x60, 0x4, 0xf2, 0x3b, 0xf3,
    0x9b, 0xc3, 0x0,

    /* U+0063 "c" */
    0x0, 0x4a, 0x88, 0x50, 0x6, 0xc0, 0x0, 0xf3,
    0xf, 0x40, 0x0, 0x41, 0x2f, 0x10, 0x0, 0x0,
    0x3f, 0x10, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0x8, 0xe2, 0x0, 0x42, 0x0, 0x7d, 0xca, 0x50,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x7,
    0xca, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x0, 0x0, 0x7c, 0xa8, 0x9a, 0x0, 0x7, 0xd1,
    0x0, 0xca, 0x0, 0xf, 0x50, 0x0, 0x9a, 0x0,
    0x3f, 0x20, 0x0, 0x9a, 0x0, 0x3f, 0x10, 0x0,
    0x9a, 0x0, 0x1f, 0x30, 0x0, 0x9a, 0x0, 0x9,
    0xb0, 0x0, 0xba, 0x0, 0x0, 0x8b, 0x86, 0x9d,
    0x80,

    /* U+0065 "e" */
    0x0, 0x58, 0x79, 0x40, 0x7, 0xa0, 0x0, 0xd2,
    0xf, 0x30, 0x0, 0xb7, 0x3f, 0x87, 0x77, 0xb5,
    0x3f, 0x20, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0x8, 0xe2, 0x0, 0x33, 0x0, 0x7c, 0xca, 0x50,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xf9, 0x0,
    0x58, 0x1, 0x20, 0xc, 0x30, 0x0, 0x0, 0xf2,
    0x0, 0x3, 0x8f, 0x98, 0x40, 0x1, 0xf2, 0x0,
    0x0, 0x1f, 0x20, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0x1f, 0x20, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x1f,
    0x20, 0x0, 0x39, 0xf9, 0x50, 0x0,

    /* U+0067 "g" */
    0x1, 0x97, 0x89, 0x8d, 0x0, 0xa8, 0x0, 0xa8,
    0x0, 0xf, 0x30, 0x6, 0xc0, 0x0, 0xe4, 0x0,
    0x7b, 0x0, 0x8, 0xa0, 0xc, 0x50, 0x0, 0x19,
    0x77, 0x40, 0x0, 0xb, 0x41, 0x10, 0x0, 0x0,
    0x6f, 0xff, 0xfe, 0x40, 0x9, 0x10, 0x0, 0x7e,
    0x4, 0x90, 0x0, 0x2, 0xe0, 0x3c, 0x0, 0x0,
    0x96, 0x0, 0x6a, 0x88, 0x94, 0x0,

    /* U+0068 "h" */
    0x39, 0xc2, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x0,
    0x0, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x1,
    0xf1, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x7b, 0xe7,
    0x0, 0x1, 0xf9, 0x20, 0x4f, 0x10, 0x1, 0xf2,
    0x0, 0xf, 0x30, 0x1, 0xf2, 0x0, 0xf, 0x30,
    0x1, 0xf2, 0x0, 0xf, 0x40, 0x1, 0xf2, 0x0,
    0xf, 0x40, 0x1, 0xf2, 0x0, 0xf, 0x40, 0x28,
    0xf9, 0x21, 0x8f, 0xa3,

    /* U+0069 "i" */
    0x3, 0xe2, 0x0, 0x29, 0x10, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x3b, 0xf3, 0x0, 0x1f, 0x20, 0x1,
    0xf2, 0x0, 0xf, 0x20, 0x0, 0xf2, 0x0, 0x1f,
    0x20, 0x1, 0xf2, 0x2, 0x8f, 0x92,

    /* U+006A "j" */
    0x0, 0x4, 0xe1, 0x0, 0x2, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x3b, 0xf2, 0x0,
    0x2, 0xf1, 0x0, 0x2, 0xf1, 0x0, 0x2, 0xf1,
    0x0, 0x1, 0xf1, 0x0, 0x1, 0xf1, 0x0, 0x2,
    0xf1, 0x0, 0x2, 0xf1, 0x0, 0x2, 0xf0, 0x0,
    0x3, 0xd0, 0x3, 0x7, 0x70, 0xd, 0xc7, 0x0,

    /* U+006B "k" */
    0x39, 0xc2, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x0,
    0x0, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x1,
    0xf2, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x8, 0xd8,
    0x50, 0x1, 0xf2, 0x6, 0x40, 0x0, 0x1, 0xf2,
    0x66, 0x0, 0x0, 0x1, 0xf7, 0xda, 0x0, 0x0,
    0x1, 0xf5, 0x2f, 0x30, 0x0, 0x1, 0xf2, 0x9,
    0xc0, 0x0, 0x1, 0xf2, 0x1, 0xe5, 0x0, 0x29,
    0xf9, 0x20, 0x7e, 0x70,

    /* U+006C "l" */
    0x39, 0xc2, 0x0, 0x1f, 0x20, 0x1, 0xf2, 0x0,
    0x1f, 0x20, 0x1, 0xf2, 0x0, 0x1f, 0x20, 0x1,
    0xf2, 0x0, 0x1f, 0x20, 0x1, 0xf2, 0x0, 0x1f,
    0x20, 0x1, 0xf2, 0x3, 0x8f, 0x93,

    /* U+006D "m" */
    0x39, 0xd1, 0x7b, 0xd3, 0x9, 0xcd, 0x30, 0x0,
    0x3f, 0x92, 0x7, 0xd8, 0x10, 0x8b, 0x0, 0x1,
    0xf2, 0x0, 0x3f, 0x0, 0x4, 0xe0, 0x0, 0x1f,
    0x20, 0x2, 0xf0, 0x0, 0x4e, 0x0, 0x1, 0xf2,
    0x0, 0x2f, 0x0, 0x4, 0xf0, 0x0, 0x1f, 0x20,
    0x2, 0xf0, 0x0, 0x4f, 0x0, 0x1, 0xf2, 0x0,
    0x3f, 0x0, 0x4, 0xf0, 0x2, 0x9f, 0x92, 0x39,
    0xf8, 0x13, 0xaf, 0x70,

    /* U+006E "n" */
    0x39, 0xd0, 0x7b, 0xe6, 0x0, 0x3, 0xf8, 0x20,
    0x4f, 0x0, 0x1, 0xf2, 0x0, 0xf, 0x20, 0x1,
    0xf2, 0x0, 0xf, 0x30, 0x1, 0xf2, 0x0, 0xf,
    0x30, 0x1, 0xf2, 0x0, 0xf, 0x30, 0x1, 0xf2,
    0x0, 0xf, 0x30, 0x29, 0xf9, 0x21, 0x8f, 0xa2,

    /* U+006F "o" */
    0x0, 0x69, 0x79, 0x50, 0x0, 0x7b, 0x0, 0xc,
    0x60, 0xf, 0x40, 0x0, 0x5e, 0x3, 0xf1, 0x0,
    0x3, 0xf1, 0x3f, 0x10, 0x0, 0x3f, 0x10, 0xf4,
    0x0, 0x5, 0xe0, 0x7, 0xa0, 0x0, 0xc6, 0x0,
    0x6, 0x97, 0x95, 0x0,

    /* U+0070 "p" */
    0x29, 0xd2, 0x8b, 0xc4, 0x0, 0x2f, 0x60, 0x3,
    0xf2, 0x1, 0xf2, 0x0, 0xb, 0x90, 0x1f, 0x20,
    0x0, 0x8c, 0x1, 0xf2, 0x0, 0x8, 0xb0, 0x1f,
    0x20, 0x0, 0xb9, 0x1, 0xf5, 0x0, 0x4f, 0x20,
    0x1f, 0x59, 0xbc, 0x30, 0x1, 0xf2, 0x0, 0x0,
    0x0, 0x1f, 0x20, 0x0, 0x0, 0x1, 0xf2, 0x0,
    0x0, 0x2, 0x9f, 0xa4, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x7c, 0xa8, 0x17, 0x0, 0x7c, 0x10, 0xb,
    0xa0, 0xf, 0x40, 0x0, 0x8a, 0x2, 0xf1, 0x0,
    0x8, 0xa0, 0x3f, 0x10, 0x0, 0x8a, 0x1, 0xf4,
    0x0, 0x8, 0xa0, 0x9, 0xc0, 0x1, 0xba, 0x0,
    0x9, 0xda, 0x78, 0xa0, 0x0, 0x0, 0x0, 0x8a,
    0x0, 0x0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x0,
    0x8a, 0x0, 0x0, 0x0, 0x8c, 0xd7,

    /* U+0072 "r" */
    0x29, 0xd0, 0x8e, 0x90, 0x3f, 0x63, 0x44, 0x1,
    0xf7, 0x0, 0x0, 0x1f, 0x20, 0x0, 0x1, 0xf2,
    0x0, 0x0, 0x1f, 0x20, 0x0, 0x1, 0xf2, 0x0,
    0x2, 0x9f, 0x94, 0x0,

    /* U+0073 "s" */
    0x3, 0xa8, 0x98, 0x0, 0xe2, 0x0, 0xc0, 0xf,
    0x50, 0x2, 0x0, 0x7f, 0xc5, 0x0, 0x0, 0x28,
    0xed, 0x0, 0x20, 0x1, 0xe5, 0x29, 0x0, 0xd,
    0x31, 0x98, 0x8a, 0x50,

    /* U+0074 "t" */
    0x0, 0x60, 0x0, 0x0, 0xf0, 0x0, 0x2, 0xf0,
    0x0, 0x6a, 0xf8, 0x80, 0x4, 0xe0, 0x0, 0x4,
    0xe0, 0x0, 0x4, 0xe0, 0x0, 0x4, 0xe0, 0x0,
    0x4, 0xe0, 0x0, 0x4, 0xf1, 0x0, 0x0, 0x9c,
    0x91,

    /* U+0075 "u" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xf0, 0x2,
    0xaf, 0x0, 0x4, 0xf0, 0x0, 0x3f, 0x0, 0x4,
    0xe0, 0x0, 0x3f, 0x0, 0x4, 0xe0, 0x0, 0x3f,
    0x0, 0x4, 0xe0, 0x0, 0x3f, 0x0, 0x4, 0xf0,
    0x0, 0x3f, 0x0, 0x1, 0xf4, 0x2, 0x9f, 0x0,
    0x0, 0x7e, 0xb7, 0x2f, 0xa1,

    /* U+0076 "v" */
    0x8, 0xfb, 0x40, 0x6c, 0x81, 0x0, 0xb9, 0x0,
    0x8, 0x0, 0x0, 0x5e, 0x0, 0x16, 0x0, 0x0,
    0xe, 0x50, 0x61, 0x0, 0x0, 0x9, 0xb0, 0x70,
    0x0, 0x0, 0x2, 0xf3, 0x40, 0x0, 0x0, 0x0,
    0xcc, 0x0, 0x0, 0x0, 0x0, 0x69, 0x0, 0x0,

    /* U+0077 "w" */
    0x8, 0xfa, 0x40, 0x8f, 0x92, 0x3a, 0xb3, 0x0,
    0xc7, 0x0, 0x3e, 0x60, 0x6, 0x20, 0x0, 0x6c,
    0x0, 0x76, 0xb0, 0x8, 0x0, 0x0, 0x1f, 0x10,
    0x71, 0xf1, 0x7, 0x0, 0x0, 0xb, 0x63, 0x30,
    0xb6, 0x42, 0x0, 0x0, 0x6, 0xb7, 0x0, 0x5b,
    0x70, 0x0, 0x0, 0x1, 0xf7, 0x0, 0xf, 0x70,
    0x0, 0x0, 0x0, 0xb2, 0x0, 0xb, 0x30, 0x0,

    /* U+0078 "x" */
    0x5c, 0xe6, 0x5, 0xd8, 0x0, 0x2f, 0x40, 0x36,
    0x0, 0x0, 0x6e, 0x18, 0x0, 0x0, 0x0, 0xcd,
    0x10, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x5,
    0x37, 0xe1, 0x0, 0x2, 0x70, 0xc, 0xa0, 0x6,
    0xd8, 0x2, 0xaf, 0x92,

    /* U+0079 "y" */
    0x8, 0xfa, 0x40, 0x5b, 0x91, 0x0, 0xb9, 0x0,
    0x8, 0x0, 0x0, 0x5e, 0x0, 0x7, 0x0, 0x0,
    0xe, 0x40, 0x52, 0x0, 0x0, 0x9, 0xa0, 0x70,
    0x0, 0x0, 0x2, 0xf1, 0x60, 0x0, 0x0, 0x0,
    0xcb, 0x10, 0x0, 0x0, 0x0, 0x6b, 0x0, 0x0,
    0x0, 0x0, 0x25, 0x0, 0x0, 0x0, 0x0, 0x70,
    0x0, 0x0, 0x5, 0x23, 0x50, 0x0, 0x0, 0xc,
    0xd6, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x3c, 0x77, 0x7d, 0xa4, 0x70, 0x4, 0xe1, 0x11,
    0x0, 0xe6, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x4e,
    0x10, 0x0, 0x1e, 0x50, 0x5, 0xa, 0xb0, 0x0,
    0xc4, 0xf9, 0x77, 0x7e,

    /* U+007B "{" */
    0x0, 0x5b, 0x81, 0x0, 0xf1, 0x0, 0x1, 0xe0,
    0x0, 0x0, 0xd1, 0x0, 0x0, 0xa3, 0x0, 0x0,
    0x75, 0x0, 0x1, 0xb1, 0x0, 0x3c, 0x50, 0x0,
    0x0, 0xa2, 0x0, 0x0, 0x75, 0x0, 0x0, 0xa3,
    0x0, 0x0, 0xe0, 0x0, 0x1, 0xe0, 0x0, 0x0,
    0xe2, 0x0, 0x0, 0x29, 0x80,

    /* U+007C "|" */
    0x5b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb0,

    /* U+007D "}" */
    0x49, 0xb2, 0x0, 0x7, 0xa0, 0x0, 0x4a, 0x0,
    0x7, 0x70, 0x0, 0xa3, 0x0, 0xb, 0x0, 0x0,
    0x67, 0x0, 0x0, 0xaa, 0x0, 0x84, 0x0, 0xb,
    0x0, 0x0, 0x94, 0x0, 0x6, 0x80, 0x0, 0x4b,
    0x0, 0x8, 0x80, 0x48, 0x70, 0x0,

    /* U+007E "~" */
    0x3, 0xb8, 0x0, 0x6, 0xa, 0x2, 0xa1, 0x19,
    0x6, 0x0, 0x2a, 0xa1,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xc7, 0x2c, 0xf1,
    0x0, 0x1, 0xff, 0xea, 0x51, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x4a, 0xbe, 0xf1,
    0x0, 0x1, 0xfc, 0x0, 0x5, 0xff, 0xff, 0xf1,
    0x8, 0xdd, 0xfc, 0x0, 0x6, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xfc, 0x0, 0x0, 0x8e, 0xfc, 0x30,
    0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0xdd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x20, 0x4, 0x44, 0x44, 0x44, 0x44, 0x0, 0x2e,
    0x57, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5e, 0xfa,
    0xbf, 0x52, 0x22, 0x22, 0x5f, 0xba, 0xff, 0x3,
    0xf3, 0x0, 0x0, 0x3, 0xf3, 0xf, 0xf8, 0x9f,
    0x30, 0x0, 0x0, 0x3f, 0xa8, 0xff, 0x79, 0xf8,
    0x55, 0x55, 0x58, 0xf9, 0x7f, 0xf0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xff, 0xbc, 0xf4, 0x11,
    0x11, 0x14, 0xfc, 0xbf, 0xf4, 0x6f, 0x30, 0x0,
    0x0, 0x3f, 0x64, 0xff, 0x3, 0xf3, 0x0, 0x0,
    0x3, 0xf3, 0xf, 0xfe, 0xef, 0x96, 0x66, 0x66,
    0x9f, 0xee, 0xfd, 0x14, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x1d,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xba,
    0x25, 0xbb, 0xbb, 0xbb, 0xbb, 0x84, 0x66, 0x50,
    0x26, 0x66, 0x66, 0x66, 0x64, 0xff, 0xff, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x44, 0x40, 0x14, 0x44,
    0x44, 0x44, 0x42, 0xac, 0xcc, 0x26, 0xcc, 0xcc,
    0xcc, 0xcc, 0xaf, 0xff, 0xf5, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xe3, 0x8f, 0xff, 0xff, 0xff,
    0xfc,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x4, 0x20,
    0x0, 0x0, 0xc, 0xff, 0xf6, 0x7, 0xfe, 0x20,
    0x0, 0xc, 0xff, 0xf6, 0x0, 0xef, 0xfe, 0x20,
    0xc, 0xff, 0xf6, 0x0, 0x3, 0xff, 0xfe, 0x3c,
    0xff, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd6, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00D "" */
    0x3c, 0x50, 0x0, 0x2, 0xc7, 0xe, 0xff, 0x50,
    0x2, 0xef, 0xf3, 0x9f, 0xff, 0x52, 0xef, 0xfd,
    0x0, 0x9f, 0xff, 0xef, 0xfd, 0x10, 0x0, 0x9f,
    0xff, 0xfd, 0x10, 0x0, 0x2, 0xff, 0xff, 0x60,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x50, 0x2, 0xef,
    0xfd, 0xaf, 0xff, 0x50, 0xdf, 0xfd, 0x10, 0x9f,
    0xff, 0x2b, 0xfd, 0x10, 0x0, 0x9f, 0xe1, 0x6,
    0x10, 0x0, 0x0, 0x52, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0x30, 0xef, 0x70, 0x97, 0x0, 0x0, 0x1e,
    0xfc, 0xe, 0xf7, 0x3f, 0xf8, 0x0, 0xb, 0xff,
    0x50, 0xef, 0x70, 0xbf, 0xf4, 0x3, 0xff, 0x60,
    0xe, 0xf7, 0x0, 0xdf, 0xb0, 0x8f, 0xe0, 0x0,
    0xef, 0x70, 0x5, 0xff, 0x1b, 0xfa, 0x0, 0xe,
    0xf7, 0x0, 0x1f, 0xf3, 0xbf, 0x90, 0x0, 0xdf,
    0x60, 0x0, 0xff, 0x49, 0xfc, 0x0, 0x2, 0x40,
    0x0, 0x3f, 0xf3, 0x5f, 0xf2, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x5, 0xff, 0xc3, 0x0, 0x7, 0xff,
    0xe1, 0x0, 0x8, 0xff, 0xff, 0xef, 0xff, 0xe2,
    0x0, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x75, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xdc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x8,
    0x25, 0xdf, 0xff, 0xd5, 0x28, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x4f, 0xff, 0xfe,
    0x41, 0x4e, 0xff, 0xff, 0x40, 0x2f, 0xff, 0x60,
    0x0, 0x6f, 0xff, 0x20, 0x1, 0xff, 0xf3, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x8f, 0xff, 0x50, 0x5f, 0xff, 0xff, 0x85, 0x8f,
    0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x6, 0xfd, 0xff, 0xff, 0xff, 0xfd,
    0xf6, 0x0, 0x4, 0x2, 0xbf, 0xff, 0xb2, 0x4,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x98, 0x20, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xd2, 0xff,
    0x40, 0x0, 0x0, 0x5, 0xff, 0x91, 0xbf, 0xef,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0x63, 0xb2, 0x8f,
    0xff, 0x40, 0x0, 0xb, 0xfe, 0x45, 0xff, 0xf4,
    0x5f, 0xf9, 0x0, 0x2d, 0xfd, 0x28, 0xff, 0xff,
    0xf6, 0x3e, 0xfc, 0x1d, 0xfb, 0x1b, 0xff, 0xff,
    0xff, 0xf9, 0x2c, 0xfb, 0x48, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x9, 0x30, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff,
    0xf4, 0x36, 0xff, 0xff, 0x10, 0x0, 0x3, 0xff,
    0xff, 0x0, 0x2f, 0xff, 0xf1, 0x0, 0x0, 0x3f,
    0xff, 0xf0, 0x2, 0xff, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xfe, 0x0, 0x1e, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x26, 0x6a,
    0xff, 0xfa, 0x66, 0x20, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xbb, 0x46, 0xf6, 0x4b,
    0xbb, 0xb9, 0xff, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xfd, 0xdd, 0xdd, 0xdd, 0xff,
    0x30, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf3, 0xdf, 0xca, 0xa9, 0x0, 0x0,
    0x19, 0xaa, 0xdf, 0xbf, 0xff, 0xff, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xec,
    0xcc, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x0, 0x4, 0x9c, 0xdc, 0x82, 0x0, 0xff, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xf0, 0x1d,
    0xff, 0xa5, 0x35, 0xaf, 0xfd, 0xff, 0xb, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xf3, 0xff, 0x30,
    0x0, 0x7, 0xdc, 0xdf, 0xff, 0x8f, 0xa0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf1, 0x31, 0x0, 0x0,
    0x1, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xee, 0xee, 0xe7, 0x0,
    0x0, 0x7, 0xe8, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xdf, 0x5f, 0xff, 0x91, 0x21, 0x0, 0x0,
    0x7f, 0xf0, 0xff, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xf7, 0xf, 0xfa, 0xff, 0xd7, 0x46, 0xcf, 0xfa,
    0x0, 0xff, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xa, 0xa0, 0x1, 0x7b, 0xdc, 0x82, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x6f, 0xf8, 0x79, 0x9a, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x1, 0xcf, 0xf8,
    0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x1, 0xb5,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x0, 0x0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xd, 0x40, 0xff, 0xff,
    0xff, 0xf8, 0xa, 0xf1, 0xff, 0xff, 0xff, 0xf8,
    0x3, 0xf3, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xe0,
    0xef, 0xff, 0xff, 0xf8, 0xa, 0x20, 0x0, 0x1,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb4, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x2, 0xa,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0x80, 0x6, 0xf6,
    0xc, 0xd0, 0x79, 0x9a, 0xff, 0xf8, 0x0, 0x9,
    0xf3, 0x3f, 0x4f, 0xff, 0xff, 0xff, 0x80, 0xd5,
    0xd, 0xb0, 0xda, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0xf1, 0x7f, 0xa, 0xcf, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0x36, 0xf0, 0x8d, 0xff, 0xff, 0xff, 0xf8,
    0xc, 0xe0, 0x8e, 0xa, 0xbe, 0xff, 0xff, 0xff,
    0x80, 0x92, 0x1e, 0x90, 0xe8, 0x0, 0x1, 0xcf,
    0xf8, 0x0, 0x1c, 0xe1, 0x5f, 0x30, 0x0, 0x1,
    0xcf, 0x80, 0x6, 0xe3, 0x1e, 0xb0, 0x0, 0x0,
    0x1, 0xb5, 0x0, 0x0, 0x1c, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xda, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x2f, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xf9, 0x6d,
    0xff, 0xfc, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0xbf,
    0xfc, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0x20, 0xac,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xf5, 0x0, 0x0, 0x9, 0xff, 0xff, 0xd0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xff, 0xff,
    0xfc, 0xd, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0x4e, 0xf1, 0xff, 0xff,
    0xff, 0xf3, 0xaf, 0x75, 0xef, 0xff, 0xff, 0x2,
    0xff, 0x82, 0x8f, 0xff, 0x70, 0x5, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x2, 0x9d, 0xda, 0x40, 0x0,

    /* U+F048 "" */
    0x2, 0x20, 0x0, 0x0, 0x1, 0x1, 0xff, 0x0,
    0x0, 0xb, 0xf2, 0x2f, 0xf0, 0x0, 0x1c, 0xff,
    0x42, 0xff, 0x0, 0x1d, 0xff, 0xf4, 0x2f, 0xf0,
    0x2e, 0xff, 0xff, 0x42, 0xff, 0x3e, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0xff, 0xff,
    0xff, 0x42, 0xff, 0x1c, 0xff, 0xff, 0xf4, 0x2f,
    0xf0, 0xb, 0xff, 0xff, 0x42, 0xff, 0x0, 0xa,
    0xff, 0xf4, 0x2f, 0xf0, 0x0, 0x9, 0xff, 0x41,
    0xfe, 0x0, 0x0, 0x8, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x2, 0x22, 0x10, 0x0, 0x12, 0x22, 0x0, 0xbf,
    0xff, 0xf5, 0x4, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xf9, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xfa, 0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x8, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xf9, 0x8,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xe3, 0x2, 0xdf,
    0xff, 0x90,

    /* U+F04D "" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0,

    /* U+F051 "" */
    0x1, 0x0, 0x0, 0x0, 0x22, 0x1, 0xfc, 0x10,
    0x0, 0xe, 0xf2, 0x3f, 0xfd, 0x10, 0x0, 0xef,
    0x33, 0xff, 0xfe, 0x20, 0xe, 0xf3, 0x3f, 0xff,
    0xfe, 0x30, 0xef, 0x33, 0xff, 0xff, 0xff, 0x4e,
    0xf3, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x33, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x3f, 0xff, 0xff, 0xfd,
    0xff, 0x33, 0xff, 0xff, 0xfd, 0x1e, 0xf3, 0x3f,
    0xff, 0xfc, 0x10, 0xef, 0x33, 0xff, 0xfb, 0x0,
    0xe, 0xf3, 0x3f, 0xfa, 0x0, 0x0, 0xef, 0x31,
    0xd9, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0x9, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xca, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xd1, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x9,
    0xff, 0xa0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x5, 0xff,
    0xd1, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0, 0x0,
    0x5, 0xff, 0x70, 0x0, 0x0, 0x5, 0x90,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9d, 0x10, 0x0,
    0x0, 0x1f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd,
    0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xfd, 0x10,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x9c, 0xcc, 0xcf, 0xff, 0xcc, 0xcc, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xeb, 0x0,
    0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9c, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xa0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x2, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xa4, 0x24, 0xbf, 0xfb,
    0x10, 0x0, 0x2d, 0xff, 0x60, 0x16, 0x30, 0x8f,
    0xfd, 0x10, 0xd, 0xff, 0xa0, 0x1, 0xff, 0x70,
    0xcf, 0xfc, 0x9, 0xff, 0xf5, 0x12, 0x8f, 0xff,
    0x17, 0xff, 0xf8, 0xef, 0xff, 0x35, 0xff, 0xff,
    0xf2, 0x5f, 0xff, 0xc7, 0xff, 0xf6, 0x1f, 0xff,
    0xfe, 0x8, 0xff, 0xf5, 0xb, 0xff, 0xc0, 0x5e,
    0xfd, 0x30, 0xef, 0xfa, 0x0, 0xc, 0xff, 0x90,
    0x1, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x8, 0xff,
    0xd8, 0x68, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0x7c, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x23,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x5b,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xfa, 0x42, 0x5b, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x41, 0x62, 0x9, 0xff, 0xd1,
    0x0, 0x1, 0x50, 0x9, 0xff, 0x9f, 0xf6, 0xd,
    0xff, 0xc0, 0x0, 0xbf, 0x90, 0x5, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x70, 0xf, 0xff, 0xc1, 0x2,
    0xdf, 0xff, 0x26, 0xff, 0xfb, 0x0, 0x7f, 0xff,
    0x50, 0x0, 0xaf, 0xf6, 0x9f, 0xff, 0x40, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x80,
    0x0, 0x1, 0xbf, 0xf9, 0x0, 0x0, 0x3e, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x87, 0x30,
    0x1b, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x28, 0xcf,
    0xfe, 0x30, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb4,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x6f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf5, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xb8, 0xcf, 0xff, 0xfc, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf8, 0x1a, 0xff, 0xff,
    0xf6, 0x0, 0x1, 0xff, 0xff, 0xff, 0x20, 0x4f,
    0xff, 0xff, 0xe0, 0x0, 0xaf, 0xff, 0xff, 0xf8,
    0x1a, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x7d,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x50,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x10, 0x45,
    0x54, 0x0, 0x0, 0x3, 0x5d, 0xfd, 0x1f, 0xff,
    0xf8, 0x0, 0x6, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf7, 0x5, 0xff, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0x74, 0xff, 0xc1, 0xcf, 0xa0, 0x0, 0x0, 0x63,
    0xff, 0xd1, 0x8, 0x90, 0x0, 0x0, 0x2, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xe2,
    0x93, 0xb, 0xd1, 0x4, 0x44, 0xef, 0xf3, 0x8f,
    0xe5, 0xdf, 0xd1, 0xff, 0xff, 0xf3, 0x2, 0xef,
    0xff, 0xff, 0xcf, 0xff, 0xf4, 0x0, 0x3, 0xff,
    0xff, 0xfa, 0x1, 0x10, 0x0, 0x0, 0x0, 0x1c,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0xff,
    0xc0, 0x0, 0x0, 0xbf, 0xf9, 0x7, 0xff, 0xc0,
    0x0, 0xbf, 0xf9, 0x0, 0x7, 0xff, 0xc1, 0x9f,
    0xf9, 0x0, 0x0, 0x7, 0xff, 0xb7, 0xf9, 0x0,
    0x0, 0x0, 0x7, 0xf9, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x6, 0x0, 0x0, 0x0, 0x0, 0x6, 0x1a, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x7f, 0xfc, 0x0,
    0x0, 0xa, 0xff, 0x90, 0x7f, 0xfc, 0x0, 0xb,
    0xff, 0x90, 0x0, 0x7f, 0xfc, 0x1b, 0xff, 0x90,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x10, 0x1, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x4, 0xff, 0xfd, 0x16, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x2a, 0xcc, 0xcc, 0xcf, 0xf0, 0x0, 0xbf, 0x9f,
    0xdb, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x1,
    0x51, 0xfc, 0x5, 0x0, 0x0, 0x0, 0xd, 0xf0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x6, 0xd, 0xf0, 0x62, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x7, 0xfc, 0xdf, 0x9f, 0xa0, 0x1, 0xff,
    0xdd, 0xdd, 0xdb, 0x2d, 0xff, 0xff, 0xe3, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf5, 0x1d, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0,

    /* U+F07B "" */
    0x3, 0x44, 0x44, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfc, 0x66, 0x66, 0x65, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x39, 0x9b,
    0xff, 0xfb, 0x99, 0x30, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0x9, 0xbb, 0xb8, 0x4f, 0xff, 0x48,
    0xbb, 0xb9, 0xff, 0xff, 0xf3, 0x22, 0x23, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0x2e,
    0xfb, 0xee, 0xee, 0xee, 0xee, 0xed, 0xed, 0xeb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x0,
    0x0, 0x6, 0xda, 0x0, 0xa, 0xff, 0xf3, 0x0,
    0x7, 0xef, 0xff, 0x84, 0xdf, 0xff, 0x50, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xdc, 0xa8, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x80, 0x0, 0x0, 0x79, 0x40, 0xbf, 0xdf,
    0xf6, 0x0, 0x1d, 0xff, 0xe0, 0xff, 0x5, 0xf9,
    0x2, 0xef, 0xfe, 0x20, 0xcf, 0xce, 0xf9, 0x3e,
    0xff, 0xe2, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x28, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5, 0xae, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xfd, 0xaf, 0xff, 0x40, 0x0, 0xef, 0x48,
    0xf8, 0xa, 0xff, 0xf4, 0x0, 0xef, 0x16, 0xf8,
    0x0, 0xaf, 0xff, 0x50, 0x9f, 0xff, 0xf3, 0x0,
    0xa, 0xff, 0xe0, 0x8, 0xdc, 0x50, 0x0, 0x0,
    0x46, 0x20,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf5, 0xb4, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf5, 0xbf, 0x40, 0x35, 0x44, 0xff,
    0xff, 0xf5, 0x8c, 0xb0, 0xff, 0xd4, 0xff, 0xff,
    0xfb, 0x66, 0x60, 0xff, 0xd4, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xd4, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xd4, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xd2, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xf6, 0x1, 0x11, 0x11,
    0x11, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbe, 0xee, 0xee, 0xee, 0xd2, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xff, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf5, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0xfe, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf0, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xf7, 0x27, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xf1, 0xff, 0xff,
    0xc0, 0x0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xf5,
    0x4, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x53, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x5f, 0xa1, 0xaf,
    0xff, 0xff, 0xff, 0xa1, 0x9f, 0xff, 0xd3, 0x6f,
    0xff, 0xff, 0x62, 0xdf, 0xff, 0xff, 0xf7, 0x2d,
    0xfd, 0x26, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x12,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9,

    /* U+F0E7 "" */
    0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x70, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xfa, 0x77,
    0x61, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0xdf,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x35, 0x55, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x20, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x3a, 0xfb, 0x33, 0x20, 0x0, 0x0, 0xff, 0xff,
    0x3e, 0xff, 0xf1, 0x0, 0x0, 0xff, 0xff, 0xcf,
    0xff, 0xf2, 0x0, 0x0, 0xff, 0xff, 0xc7, 0x77,
    0x71, 0x0, 0x0, 0xff, 0xfe, 0x2a, 0xaa, 0xa1,
    0x61, 0x0, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbc,
    0x10, 0xff, 0xfc, 0x6f, 0xff, 0xf2, 0xbf, 0xc0,
    0xff, 0xfc, 0x6f, 0xff, 0xf2, 0x12, 0x20, 0xff,
    0xfc, 0x6f, 0xff, 0xfc, 0x99, 0x90, 0xff, 0xfc,
    0x6f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xfc, 0x6f, 0xff,
    0xff, 0xff, 0xf1, 0x12, 0x21, 0x6f, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xd0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2a, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xd9, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xdd, 0xfd, 0xef, 0xdf, 0xdd, 0xfd,
    0xdf, 0xdf, 0xe0, 0x1d, 0x3, 0xb0, 0xd1, 0xd,
    0x10, 0xfe, 0xff, 0xbc, 0xfb, 0xcf, 0xbf, 0xcb,
    0xfc, 0xbf, 0xef, 0xff, 0x73, 0xc7, 0x3d, 0x38,
    0xb3, 0x9f, 0xfe, 0xff, 0xf4, 0xa, 0x40, 0xb0,
    0x6a, 0x6, 0xff, 0xef, 0xff, 0xfe, 0xff, 0xef,
    0xef, 0xfe, 0xff, 0xfe, 0xfe, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0xe2, 0x1f, 0xef, 0xe0, 0x1d, 0x0,
    0x0, 0x0, 0xd, 0x10, 0xfe, 0xff, 0xee, 0xfe,
    0xee, 0xee, 0xee, 0xfe, 0xef, 0xd9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x14, 0x55, 0x55,
    0x9f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xb1, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0x97, 0x80, 0x0, 0xff, 0xff, 0xff, 0x98,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0x98, 0xff, 0x80,
    0xff, 0xff, 0xff, 0x95, 0xbb, 0xb1, 0xff, 0xff,
    0xff, 0xd5, 0x55, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe2,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xfc,
    0x82, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0xaf, 0xff, 0xd9,
    0x54, 0x34, 0x6a, 0xef, 0xff, 0x70, 0xcf, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x87,
    0xf8, 0x0, 0x0, 0x46, 0x76, 0x30, 0x0, 0x1b,
    0xf4, 0x1, 0x0, 0x19, 0xff, 0xff, 0xff, 0xe7,
    0x0, 0x1, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xef,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0x7, 0xff, 0x82,
    0x0, 0x3, 0xaf, 0xf3, 0x0, 0x0, 0x0, 0x6,
    0x20, 0x0, 0x0, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xe8, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F241 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x33,
    0x30, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F242 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x44,
    0x44, 0x10, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x33, 0x33, 0x10,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F243 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x44, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x3f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x3f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x33, 0x32, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F244 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfd, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfb, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xcf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfb, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xfd, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xee,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xc0, 0x1b, 0xb1, 0x0, 0x0, 0x0, 0x18, 0x92,
    0x0, 0xd4, 0x0, 0x0, 0x0, 0x1, 0x20, 0xb,
    0xff, 0xe1, 0x7d, 0x11, 0x11, 0x11, 0x11, 0x6f,
    0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8a, 0xff, 0xc0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0x5e, 0x50, 0x5, 0x60, 0x0, 0x0,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xe1, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xfd, 0x70, 0x0, 0x0, 0x7f, 0xff,
    0x8f, 0xff, 0x90, 0x0, 0x2f, 0xff, 0xf3, 0x6f,
    0xff, 0x30, 0x8, 0xff, 0xff, 0x31, 0x7f, 0xf9,
    0x0, 0xcf, 0x96, 0xf3, 0xb2, 0x9f, 0xd0, 0xf,
    0xfe, 0x26, 0x39, 0x1c, 0xff, 0x0, 0xff, 0xfe,
    0x20, 0xa, 0xff, 0xf0, 0xf, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0x10, 0xff, 0xfd, 0x10, 0x9, 0xff,
    0xf0, 0xf, 0xfd, 0x17, 0x39, 0x1a, 0xff, 0x0,
    0xcf, 0x97, 0xf3, 0xa2, 0x8f, 0xd0, 0x8, 0xff,
    0xff, 0x30, 0x8f, 0xf9, 0x0, 0x1f, 0xff, 0xf4,
    0x7f, 0xff, 0x20, 0x0, 0x5f, 0xff, 0xaf, 0xff,
    0x70, 0x0, 0x0, 0x29, 0xcd, 0xda, 0x40, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xcd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc1, 0x2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x1f, 0xfd, 0xff, 0xdf, 0xfd,
    0xff, 0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff,
    0x30, 0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30,
    0x1f, 0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f,
    0xf4, 0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4,
    0xcf, 0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf4, 0xcf,
    0x1f, 0xe2, 0xff, 0x30, 0x1f, 0xf5, 0xdf, 0x3f,
    0xe3, 0xff, 0x30, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x8, 0xde, 0xee, 0xee, 0xee, 0xd9,
    0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x64, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x4f, 0xe3,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x64, 0x30,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xca, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x31, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x8, 0xff, 0xff,
    0xf8, 0x5f, 0xfc, 0x3e, 0xff, 0xfc, 0x8, 0xff,
    0xff, 0xff, 0x20, 0x5b, 0x0, 0xaf, 0xff, 0xc8,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x8f, 0xff,
    0xfc, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x3f,
    0xff, 0xff, 0xc4, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x10, 0x5f, 0xff, 0xfc, 0x4, 0xff, 0xff, 0xff,
    0x10, 0x8e, 0x20, 0x9f, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xfb, 0x9f, 0xfe, 0x7f, 0xff, 0xfc, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xbe, 0xdf, 0xdf,
    0xdf, 0xf0, 0xb, 0xf4, 0x1d, 0xf, 0xc, 0xf1,
    0xbf, 0xf4, 0x1d, 0xf, 0xc, 0xf1, 0xff, 0xf8,
    0x6e, 0x5f, 0x5d, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x50,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1,
    0x0, 0x4, 0x40, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x6, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1c, 0xff, 0xd6, 0x66, 0x66, 0x66, 0x66, 0x40,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 61, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 74, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17, .adv_w = 90, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 30, .adv_w = 138, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 80, .adv_w = 132, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 140, .adv_w = 222, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 224, .adv_w = 187, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 290, .adv_w = 47, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 298, .adv_w = 87, .box_w = 5, .box_h = 17, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 341, .adv_w = 87, .box_w = 5, .box_h = 17, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 384, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 409, .adv_w = 139, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 450, .adv_w = 78, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 460, .adv_w = 83, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 463, .adv_w = 78, .box_w = 3, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 466, .adv_w = 84, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 508, .adv_w = 134, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 552, .adv_w = 113, .box_w = 6, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 588, .adv_w = 134, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 632, .adv_w = 134, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 676, .adv_w = 133, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 726, .adv_w = 134, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 770, .adv_w = 134, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 818, .adv_w = 132, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 862, .adv_w = 134, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 906, .adv_w = 135, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 954, .adv_w = 78, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 966, .adv_w = 78, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 988, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1024, .adv_w = 139, .box_w = 9, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1042, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1078, .adv_w = 104, .box_w = 5, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1108, .adv_w = 219, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1206, .adv_w = 172, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1267, .adv_w = 161, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1322, .adv_w = 166, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1377, .adv_w = 185, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1438, .adv_w = 156, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1493, .adv_w = 151, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1543, .adv_w = 178, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1604, .adv_w = 205, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1676, .adv_w = 97, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1709, .adv_w = 97, .box_w = 7, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1758, .adv_w = 175, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1819, .adv_w = 150, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1869, .adv_w = 234, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1946, .adv_w = 191, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2012, .adv_w = 184, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2073, .adv_w = 154, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2128, .adv_w = 184, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2211, .adv_w = 172, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2272, .adv_w = 136, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2316, .adv_w = 158, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2371, .adv_w = 191, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2437, .adv_w = 171, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2498, .adv_w = 253, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2586, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2647, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2708, .adv_w = 145, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2758, .adv_w = 83, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2788, .adv_w = 84, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2830, .adv_w = 83, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2860, .adv_w = 139, .box_w = 7, .box_h = 5, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2878, .adv_w = 135, .box_w = 10, .box_h = 1, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2883, .adv_w = 104, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 2891, .adv_w = 134, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2927, .adv_w = 153, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2986, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3018, .adv_w = 151, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3083, .adv_w = 131, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3115, .adv_w = 93, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3161, .adv_w = 136, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3215, .adv_w = 159, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3275, .adv_w = 80, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3305, .adv_w = 75, .box_w = 6, .box_h = 16, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 3353, .adv_w = 146, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3413, .adv_w = 80, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3443, .adv_w = 234, .box_w = 15, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3503, .adv_w = 159, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3543, .adv_w = 143, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3579, .adv_w = 153, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3633, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3687, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3715, .adv_w = 114, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3743, .adv_w = 88, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3776, .adv_w = 157, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3821, .adv_w = 132, .box_w = 10, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3861, .adv_w = 201, .box_w = 14, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3917, .adv_w = 135, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3953, .adv_w = 132, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4013, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4041, .adv_w = 90, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4086, .adv_w = 76, .box_w = 1, .box_h = 17, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4095, .adv_w = 90, .box_w = 5, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4133, .adv_w = 139, .box_w = 8, .box_h = 3, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 4145, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4273, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4363, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4468, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4558, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4619, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4739, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4859, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4978, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5098, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5200, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5320, .adv_w = 120, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5368, .adv_w = 180, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5440, .adv_w = 270, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5568, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5658, .adv_w = 165, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5746, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5829, .adv_w = 210, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5948, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6046, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6144, .adv_w = 210, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6227, .adv_w = 210, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 6332, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6395, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6458, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6556, .adv_w = 210, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 6584, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6686, .adv_w = 300, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6838, .adv_w = 270, .box_w = 19, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6990, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7103, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 7162, .adv_w = 210, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 7221, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7345, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7435, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7555, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7683, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7781, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7893, .adv_w = 210, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7991, .adv_w = 210, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8082, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8172, .adv_w = 150, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8260, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8372, .adv_w = 210, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8484, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8586, .adv_w = 240, .box_w = 17, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8722, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8818, .adv_w = 300, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8951, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9056, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9161, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9266, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9371, .adv_w = 300, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9476, .adv_w = 300, .box_w = 19, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9600, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9704, .adv_w = 210, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9809, .adv_w = 240, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9937, .adv_w = 300, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10051, .adv_w = 180, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10147, .adv_w = 241, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 2, 0, 0, 3,
    1, 4, 5, 6, 0, 7, 8, 7,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 10, 10, 0, 0, 0,
    11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 20, 21, 22, 23, 20, 24,
    25, 26, 25, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 4, 36, 5, 0,
    37, 0, 38, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 41, 45, 45,
    39, 39, 49, 50, 51, 52, 53, 54,
    54, 55, 54, 56, 4, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 10,
    11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, 0, 0, 0,
    14, 15, 16, 17, 18, 17, 17, 17,
    18, 17, 17, 19, 17, 17, 20, 20,
    18, 17, 18, 17, 21, 22, 23, 24,
    25, 26, 27, 28, 5, 29, 6, 0,
    30, 0, 31, 32, 33, 33, 33, 34,
    35, 32, 36, 37, 32, 32, 38, 38,
    33, 39, 33, 38, 40, 41, 42, 43,
    43, 44, 45, 46, 5, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 0, -27,
    0, -27, -21, 0, 0, 0, 0, -32,
    0, -6, -2, 0, -2, 8, -1, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    -10, -1, -13, 0, 0, -2, -5, -8,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, -10, 0, 3,
    0, 0, 0, 0, 0, -5, -5, 0,
    -1, 0, 0, 0, 0, 0, 0, -1,
    -2, 0, 0, 0, 0, 0, 2, 0,
    1, 0, 1, 0, 0, 0, 0, 0,
    0, -5, 0, -2, -6, -2, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, -14, -7, -16, -15, 0, -15, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -5, -13, 3,
    0, 0, -10, 0, 0, -10, -10, 0,
    0, -8, 0, -5, 9, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -7, -1, 0, 0, 25, -4,
    0, -5, -2, -6, -5, -2, 4, -2,
    0, 0, 0, 0, 0, -13, 0, -10,
    0, -10, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, -5, -5, -13,
    -10, -5, -10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -26, 0, -26,
    0, 0, 0, 0, 0, -29, 0, -2,
    -5, 0, 0, 6, 0, 0, 0, -2,
    0, 0, 0, 0, -10, 0, -6, -2,
    -11, 0, 0, -2, -2, -6, 0, 0,
    0, 0, 0, -3, 0, -21, 0, 0,
    -13, -12, -34, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, -14, 0, 0,
    0, -21, -16, -35, -31, 0, -30, 0,
    -34, 0, -5, 0, -8, 0, -5, 0,
    2, 0, -8, -4, -10, -11, -23, 0,
    -10, -5, 0, 0, 0, 0, 0, -10,
    0, -13, 0, -13, -8, 0, 0, 0,
    0, -8, 0, 2, -5, -2, 0, -11,
    -2, -20, -18, -10, -18, -2, -10, 0,
    0, -1, 2, -2, 1, 0, 0, 0,
    0, 0, 0, 0, -2, -5, -2, 0,
    0, 0, -5, 0, 0, 0, 0, -36,
    -16, -36, -20, 0, 0, 0, 0, -18,
    0, 0, 0, 0, 0, 1, 0, 3,
    3, 0, 0, 0, 0, 0, -8, 0,
    -8, 0, -8, 0, 0, -5, -5, -5,
    0, -3, 0, -2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -2, -8, -8, -2,
    -13, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, -29, 0, -29, -18, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -4, 0, 0, 0, 0,
    0, -1, 0, 1, 0, 0, 0, 0,
    0, -1, -1, -1, -1, 0, 0, -21,
    0, -3, 0, -1, -2, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 1, 0,
    0, -29, 0, 0, -5, -8, -28, 0,
    -8, 0, 0, 0, 0, 0, 0, 2,
    -2, -9, -3, -2, 0, -15, -17, -32,
    -26, 0, -23, 0, -16, 0, -1, 0,
    -5, 0, -1, 0, -2, 0, -7, 0,
    -5, -5, -14, 0, -12, 0, 0, -5,
    0, 0, 0, -4, -2, -7, 0, -7,
    -5, 0, 0, 0, 0, -8, -6, -2,
    -3, -4, 0, -5, -8, -13, -10, -8,
    -13, -2, -4, -6, 0, -2, 0, -4,
    0, 0, 0, 0, -2, 0, 0, -4,
    -4, -2, -2, 0, 0, 5, 0, 0,
    0, 1, 1, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, -2, -2, -2, 0, -5, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, -2, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -5,
    0, -11, 1, -11, -7, 0, 0, 0,
    4, -14, -7, 2, -2, -5, 0, -5,
    -5, -12, -12, -10, -16, -2, -4, -18,
    0, -2, 0, 0, -2, 0, 0, 0,
    -2, -2, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -4,
    -4, 0, -4, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, -5, 0,
    0, -2, -5, 0, -5, 0, 0, 3,
    0, -2, -2, 0, 2, -25, -5, -25,
    -16, -1, -1, 0, -5, -18, -2, -2,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, 3, -31, -10, -1, -11, -7,
    -16, -2, 0, -5, -10, -11, 0, -5,
    -5, -8, -5, -8, 0, 0, 0, 0,
    2, -1, 0, -2, 6, -2, -4, 0,
    0, 0, 3, -2, -2, 2, 0, 0,
    0, -2, -4, -9, -9, -5, -12, -2,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, -6, -4, -4, 0, 0, 0,
    -8, -5, -2, -6, -10, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, -14,
    -2, -14, -7, -6, -6, 0, -3, -10,
    0, -2, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -19, -6, 0,
    -6, -10, -8, -2, -2, -6, -10, -7,
    -5, -8, -8, -5, -7, -5, 0, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, 2, 0, -7,
    -4, 0, 0, 0, -2, -2, -2, 0,
    -2, 2, 0, 0, -2, 0, -5, 0,
    -2, 0, 0, 0, -7, 0, -5, -5,
    -14, 0, -12, 0, 0, -34, 0, 0,
    0, -6, -26, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, -4, -2,
    -4, -18, -12, -28, -24, -4, -26, -4,
    -14, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -4, 0, -2, -5, -10, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, -8, -1, -8, -8, -3, -3, 0,
    0, -9, -2, -4, 0, 0, -2, 0,
    -2, 0, 0, -2, 0, -1, 0, -14,
    -3, 0, -3, -5, -5, 0, 0, -2,
    -5, -3, -2, -5, -5, -5, -5, -5,
    0, -6, 0, 0, 0, -5, -2, -10,
    2, -10, -5, 0, 0, 0, 5, -9,
    -6, 2, -2, -4, 0, -2, -5, -9,
    -7, -8, -10, 0, -2, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, -1, 0, -2, 6, -27, -1, -27,
    -13, 2, 2, 0, 0, -16, -2, 0,
    -2, 0, 2, 2, -5, -2, -2, -5,
    -6, 2, 2, -37, -1, -1, -5, 0,
    -5, 0, 0, -2, 0, -1, 4, 0,
    2, 0, 4, 0, 0, -10, 0, 0,
    0, 0, -3, 0, -4, 0, 0, 0,
    0, 0, 0, 2, 0, -5, -2, 0,
    0, -8, -8, -14, -12, 2, -12, 2,
    -5, 4, 0, 0, -2, 0, 0, 0,
    0, 0, -3, 0, -2, -3, -5, 0,
    -5, 0, 0, 0, 0, 0, 0, -2,
    0, -5, 0, -5, -4, 0, 0, 0,
    1, -5, -2, 0, -2, -2, -2, -2,
    -6, -7, -6, -2, -8, 0, 0, -10,
    0, -2, 0, -4, -2, 0, 0, 0,
    -2, 0, -2, -2, -5, -2, -5, 0,
    0, 8, 0, 0, -5, 0, 6, -21,
    -11, -21, -12, -1, -1, 0, -5, -17,
    0, -2, 0, 0, 0, 2, -2, 0,
    0, -1, 0, 2, 6, -21, -10, 0,
    -18, -2, -10, 0, 0, -8, -17, -13,
    0, -7, -10, -9, -10, -16, 0, 0,
    0, -3, -5, -2, 0, -21, -3, -21,
    -14, -3, -3, 0, 0, -19, -5, -7,
    -4, -2, -5, -2, -5, -4, -2, -2,
    0, -2, 0, -22, -10, -2, -8, -5,
    -10, -5, -2, -6, -11, -10, -5, -8,
    -6, -8, -8, -9, 0, 0, 0, -10,
    -13, 0, 0, -40, -19, -40, -23, -10,
    -10, 0, -12, -29, 0, -12, 0, 0,
    -5, 0, -2, 0, 0, 0, 0, -2,
    3, -41, -21, -1, -19, -13, -22, -7,
    -5, -15, -19, -22, -8, -16, -10, -18,
    -16, -16, 0, 0, 0, -8, -10, 0,
    0, -32, -17, -32, -23, -10, -10, 0,
    -12, -27, 0, -10, 0, -2, -5, 0,
    -2, 0, 0, 0, 0, -2, 3, -34,
    -19, -1, -19, -10, -22, -5, -5, -13,
    -18, -19, -7, -14, -11, -14, -11, -16,
    0, 0, 0, 0, -5, 0, -2, 0,
    -11, 0, 0, -2, -2, 0, 0, 0,
    0, -8, 0, 0, 0, -2, -2, 0,
    0, 0, 0, 2, 0, 0, -1, 0,
    -8, 0, 0, 0, -4, 0, -8, -2,
    -8, -10, -17, 0, -12, 0, 0, 0,
    0, -10, -13, 0, 0, -27, -21, -27,
    -23, -16, -16, 0, -12, -23, 0, -13,
    0, 0, -10, 0, 0, 0, 0, 0,
    0, 0, 3, -29, -20, -1, -24, -16,
    -25, -10, -6, -17, -23, -21, -16, -21,
    -18, -16, -18, -22, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, -2, 0, 0,
    0, -2, -5, -2, -2, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, -3, -6, 0,
    -6, 0, 0, -21, 0, 0, 0, 0,
    0, 4, -5, 4, 0, 0, 0, 0,
    0, 0, 0, -8, 18, 0, 0, -16,
    -14, -25, -22, 0, -21, 0, -20, 0,
    0, 0, 0, 0, 1, 0, 22, 0,
    0, 0, -2, -1, -5, 0, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 8, 0, -5, -21, -26, -39,
    -36, 0, -29, 0, 0, 0, -4, 0,
    -13, 0, 0, 0, 0, 0, -6, -5,
    -13, -12, -19, 0, -15, 0, 0, -16,
    0, 0, 0, 0, -12, 0, -2, 0,
    0, 0, 0, -1, 0, 2, -2, -3,
    -5, 0, 0, -14, -13, -25, -23, 0,
    -21, 0, -11, 0, 0, 0, 0, 0,
    -2, 0, 0, -1, -5, 0, 0, 0,
    -5, 0, -2, 0, 0, -10, 0, 0,
    0, -3, -6, -6, 2, -6, -5, -1,
    -1, -1, 0, -10, -6, 0, -6, -2,
    -2, -16, -10, -19, -17, -5, -19, 0,
    -5, -13, 0, -2, 0, 0, 0, 0,
    0, 0, -2, 0, -2, -2, 0, -5,
    -2, 0, 0, -1, 0, 0, 0, 0,
    -1, -1, 0, -1, 0, 0, 0, 0,
    0, -2, -3, 0, -5, 0, 0, -10,
    -8, -18, -16, -5, -16, 0, -1, -6,
    0, -2, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -3, 0, -1, -5, 0,
    -1, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, 0, 0, -6, -6, -10,
    -9, 0, -11, 0, -1, 0, -2, 0,
    0, 0, -2, 0, 0, 0, -4, 0,
    0, -2, -2, 0, -2, 0, 0, -5,
    0, 0, 0, -4, -5, -7, 0, -7,
    -2, 0, 0, -1, 0, -5, -2, 0,
    -5, 0, 0, -12, -8, -17, -15, -5,
    -18, 0, -5, -10, -2, -2, 0, -2,
    0, 0, 0, 0, -2, 0, 0, 0,
    -2, 0, -2, 0, 10, 20, 0, 0,
    0, 23, 14, -9, -3, -9, -4, 0,
    0, 9, 0, -2, 4, 0, 6, 10,
    6, 14, 10, 14, 14, 12, 14, 10,
    25, -6, 0, -2, -4, 0, -1, 6,
    6, 0, 0, 0, 0, -2, 2, 0,
    2, 0, 0, 0, 0, -5, 0, 0,
    0, 0, -1, 0, 3, 0, 0, -1,
    0, 0, -2, 0, 12, -1, 0, -6,
    -8, -10, -10, 0, -17, 0, -4, 0,
    -5, -3, -2, 0, -2, 0, 5, 0,
    0, 0, 0, 0, 1, 0, 2, 0,
    0, -14, 0, 0, 0, -3, -17, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    -1, -1, -5, 0, 0, -15, -8, -22,
    -20, 0, -18, 0, -13, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -1, -1, -6, 0, -6, 0, -1, -2,
    0, -1, 0, 0, -1, 0, 0, 0,
    0, 0, 0, -2, 0, 0, -1, 0,
    0, 0, 0, -5, -5, -5, -5, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -4, 0, -4, 0, 0, -1, 0, -1,
    0, 0, -1, -4, 0, -4, -3, -3,
    -3, -1, 0, -3, -2, -1, 0, 0,
    0, 0, -6, -4, -4, -2, -5, 0,
    0, -8, 0, -1, 0, 0, -2, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -1, 0, -5, 0, 0, -1, -1, -1,
    0, 4, -1, -1, -5, 0, 0, -10,
    -10, -17, -14, 1, -18, 2, -1, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    -2, -2, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -1, 0, 2, -6, 8,
    0, 3, 2, 0, 8, -1, 0, -3,
    -6, 0, 8, -4, 0, -13, -11, -21,
    -17, -1, -21, 0, -3, -1, -2, -2,
    0, 0, 0, 0, 9, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 1,
    0, -3, 0, -6, 4, -22, -8, -22,
    -12, -2, -2, -1, -3, -14, -6, 0,
    -7, -4, 0, -1, -7, -12, -10, -14,
    -12, 0, 0, -17, -2, -2, -3, 0,
    -3, 0, 0, 0, 0, -2, 4, 0,
    2, 0, 2, 0, 0, -5, 0, 0,
    0, -3, -3, -6, 0, -6, 0, 0,
    0, 0, 0, -2, -5, 0, -6, -2,
    0, -8, -6, -18, -16, 0, -23, 0,
    -6, -10, 0, -2, 0, -1, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 4, 0, 0, -2, 0, 0, 0,
    -3, -10, -8, 0, -12, 2, 0, 3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, 0,
    0, -10, 0, 0, 0, -6, -9, 0,
    0, 0, 0, -2, -2, 0, 0, 0,
    0, -3, -7, 0, 0, -10, -10, -20,
    -18, 0, -19, 0, -5, 0, 0, 0,
    -2, 0, -2, 0, -2, 0, -3, 0,
    0, 0, -5, 0, -5, 0, 0, -1,
    0, -4, 0, -9, -1, -24, -3, -24,
    -10, -1, -1, -1, -2, -17, -6, 0,
    -10, -5, 0, -3, -6, -13, -11, -13,
    -14, -1, 0, -20, -4, -6, -5, 0,
    -5, 0, 0, 0, 0, -2, 2, 0,
    3, 0, 3, 0, 0, 0, 0, 0,
    0, -3, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 2, 0, -2, -5, 0,
    0, -7, -8, -18, -13, 0, -16, 0,
    0, 0, -3, 0, -5, 0, -2, 0,
    0, 0, -5, 0, 0, 0, 0, 1,
    0, 0, 0, 0, 0, 0, 0, -2,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -6, -16, -13, 0, -15, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 56,
    .right_class_cnt     = 46,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_SourceHanSerifSC_Regular_15 = {
#else
lv_font_t lv_font_SourceHanSerifSC_Regular_15 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_SOURCEHANSERIFSC_REGULAR_15*/

