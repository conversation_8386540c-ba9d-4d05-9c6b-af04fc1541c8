/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 18 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SOURCEHANSERIFSC_REGULAR_18
#define LV_FONT_SOURCEHANSERIFSC_REGULAR_18 1
#endif

#if LV_FONT_SOURCEHANSERIFSC_REGULAR_18

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xd, 0x72, 0xfa, 0x1f, 0x90, 0xf8, 0xd, 0x60,
    0xb4, 0xa, 0x30, 0x92, 0x8, 0x10, 0x50, 0x0,
    0x0, 0x10, 0x2f, 0xa1, 0xd8,

    /* U+0022 "\"" */
    0xd, 0x50, 0x99, 0xf, 0x80, 0xcc, 0xe, 0x70,
    0xbb, 0xc, 0x50, 0x99, 0xa, 0x30, 0x77, 0x8,
    0x10, 0x55, 0x1, 0x0, 0x0,

    /* U+0023 "#" */
    0x0, 0x4, 0x50, 0x3, 0x60, 0x0, 0x6, 0x30,
    0x5, 0x40, 0x0, 0x9, 0x10, 0x8, 0x20, 0x0,
    0xa, 0x0, 0x9, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xfe, 0x3, 0x4b, 0x33, 0x3b, 0x33, 0x0, 0x18,
    0x0, 0x9, 0x0, 0x0, 0x36, 0x0, 0x27, 0x0,
    0x0, 0x54, 0x0, 0x55, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf4, 0x13, 0xb3, 0x33, 0xa3, 0x30, 0x0,
    0xa0, 0x0, 0xa0, 0x0, 0x0, 0xa0, 0x0, 0xa0,
    0x0, 0x1, 0x80, 0x0, 0x90, 0x0,

    /* U+0024 "$" */
    0x0, 0x1, 0x30, 0x0, 0x0, 0x3, 0x60, 0x0,
    0x1, 0xab, 0xca, 0x50, 0xd, 0x32, 0x61, 0xf4,
    0x6b, 0x2, 0x60, 0xca, 0x8a, 0x2, 0x60, 0x34,
    0x6f, 0x12, 0x60, 0x0, 0x1e, 0xd7, 0x60, 0x0,
    0x3, 0xef, 0xd4, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x3, 0x68, 0xf6, 0x0, 0x3, 0x60, 0xac,
    0x52, 0x3, 0x60, 0x6d, 0xd9, 0x3, 0x60, 0x8a,
    0x6d, 0x3, 0x62, 0xe2, 0x5, 0xab, 0xca, 0x20,
    0x0, 0x3, 0x60, 0x0, 0x0, 0x3, 0x60, 0x0,

    /* U+0025 "%" */
    0x0, 0x98, 0x90, 0x0, 0x0, 0x0, 0x60, 0x0,
    0x9, 0x60, 0x78, 0x0, 0x0, 0x3, 0x70, 0x0,
    0xf, 0x20, 0x2e, 0x0, 0x0, 0xa, 0x0, 0x0,
    0x1f, 0x0, 0x1f, 0x0, 0x0, 0x82, 0x0, 0x0,
    0x2f, 0x0, 0xf, 0x10, 0x3, 0x70, 0x0, 0x0,
    0x1f, 0x0, 0x1f, 0x0, 0xa, 0x1, 0x88, 0x30,
    0xe, 0x20, 0x3d, 0x0, 0x83, 0xc, 0x30, 0xc1,
    0x7, 0x80, 0x96, 0x3, 0x80, 0x3d, 0x0, 0x87,
    0x0, 0x67, 0x60, 0xa, 0x0, 0x6b, 0x0, 0x6a,
    0x0, 0x0, 0x0, 0x73, 0x0, 0x7a, 0x0, 0x5c,
    0x0, 0x0, 0x2, 0x80, 0x0, 0x6b, 0x0, 0x6b,
    0x0, 0x0, 0xa, 0x0, 0x0, 0x4c, 0x0, 0x79,
    0x0, 0x0, 0x73, 0x0, 0x0, 0xd, 0x10, 0xc3,
    0x0, 0x2, 0x80, 0x0, 0x0, 0x3, 0x98, 0x60,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x6, 0xa9, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x0, 0x3f, 0x0, 0x0, 0x0, 0x0, 0xa7,
    0x0, 0xe, 0x30, 0x0, 0x0, 0x0, 0x99, 0x0,
    0x1f, 0x10, 0x0, 0x0, 0x0, 0x5f, 0x20, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xc8, 0x40, 0x5,
    0xab, 0x92, 0x0, 0x8, 0xeb, 0x0, 0x0, 0x1a,
    0x0, 0x1, 0xc3, 0x2f, 0x90, 0x0, 0x46, 0x0,
    0xb, 0x80, 0x5, 0xf7, 0x0, 0x81, 0x0, 0x1f,
    0x30, 0x0, 0x7f, 0x61, 0x80, 0x0, 0x3f, 0x30,
    0x0, 0x9, 0xfa, 0x10, 0x0, 0xf, 0x90, 0x0,
    0x0, 0xcf, 0x30, 0x0, 0x7, 0xf7, 0x0, 0x28,
    0x6c, 0xe2, 0x0, 0x0, 0x5c, 0xfe, 0xb3, 0x1,
    0xae, 0xa2,

    /* U+0027 "'" */
    0xd, 0x50, 0xf8, 0xe, 0x70, 0xc5, 0xa, 0x30,
    0x81, 0x1, 0x0,

    /* U+0028 "(" */
    0x0, 0x0, 0x20, 0x0, 0x2, 0x90, 0x0, 0xc,
    0x10, 0x0, 0x77, 0x0, 0x1, 0xe1, 0x0, 0x7,
    0xa0, 0x0, 0xc, 0x60, 0x0, 0x1f, 0x20, 0x0,
    0x2f, 0x0, 0x0, 0x4f, 0x0, 0x0, 0x5f, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0x2f, 0x10, 0x0, 0xe,
    0x30, 0x0, 0xa, 0x70, 0x0, 0x5, 0xc0, 0x0,
    0x0, 0xc3, 0x0, 0x0, 0x4a, 0x0, 0x0, 0x8,
    0x40, 0x0, 0x0, 0x90, 0x0, 0x0, 0x0,

    /* U+0029 ")" */
    0x20, 0x0, 0x2, 0x90, 0x0, 0x7, 0x60, 0x0,
    0xd, 0x10, 0x0, 0x79, 0x0, 0x1, 0xf0, 0x0,
    0xd, 0x50, 0x0, 0x99, 0x0, 0x7, 0xb0, 0x0,
    0x6d, 0x0, 0x5, 0xe0, 0x0, 0x6c, 0x0, 0x7,
    0xb0, 0x0, 0xa8, 0x0, 0xe, 0x30, 0x3, 0xd0,
    0x0, 0x95, 0x0, 0x1c, 0x0, 0xa, 0x20, 0x3,
    0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x3d, 0x0,
    0x0, 0x34, 0x1, 0xb0, 0x16, 0x6, 0xec, 0x48,
    0x8f, 0xd1, 0x0, 0x27, 0xc3, 0x10, 0x0, 0x2,
    0xb4, 0xa0, 0x0, 0x1, 0xd6, 0xc, 0x80, 0x0,
    0x3b, 0x0, 0x3b, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x16, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0x0, 0x0, 0x0, 0x0, 0x2a, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0x0, 0x0, 0x0, 0x0, 0x2a, 0x0,
    0x0, 0x3c, 0xcc, 0xde, 0xcc, 0xc9, 0x0, 0x0,
    0x2a, 0x0, 0x0, 0x0, 0x0, 0x2a, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x0,

    /* U+002C "," */
    0xc, 0xb0, 0xe, 0xf0, 0x2, 0xd0, 0x5, 0x70,
    0x68, 0x0, 0x10, 0x0,

    /* U+002D "-" */
    0x3f, 0xff, 0xf6,

    /* U+002E "." */
    0x2, 0x10, 0xf, 0xe0, 0xc, 0xa0,

    /* U+002F "/" */
    0x0, 0x0, 0xb, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x57, 0x0, 0x0, 0x9, 0x20, 0x0, 0x0,
    0xc0, 0x0, 0x0, 0x39, 0x0, 0x0, 0x7, 0x40,
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x1b, 0x0, 0x0,
    0x5, 0x60, 0x0, 0x0, 0xa2, 0x0, 0x0, 0xc,
    0x0, 0x0, 0x4, 0x80, 0x0, 0x0, 0x84, 0x0,
    0x0, 0xc, 0x0, 0x0, 0x2, 0xb0, 0x0, 0x0,
    0x66, 0x0, 0x0, 0xb, 0x10, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x8, 0xaa, 0x80, 0x0, 0x0, 0xb8, 0x0,
    0x8b, 0x0, 0x4, 0xe0, 0x0, 0xe, 0x40, 0xa,
    0xa0, 0x0, 0xa, 0xa0, 0xe, 0x70, 0x0, 0x7,
    0xe0, 0x1f, 0x60, 0x0, 0x6, 0xf1, 0x2f, 0x50,
    0x0, 0x5, 0xf2, 0x2f, 0x50, 0x0, 0x5, 0xf1,
    0x1f, 0x60, 0x0, 0x6, 0xf1, 0xe, 0x70, 0x0,
    0x7, 0xe0, 0xa, 0xa0, 0x0, 0xa, 0xa0, 0x4,
    0xe0, 0x0, 0xe, 0x30, 0x0, 0xb8, 0x0, 0x8b,
    0x0, 0x0, 0x8, 0x99, 0x80, 0x0,

    /* U+0031 "1" */
    0x3, 0x7b, 0x50, 0x6, 0x76, 0xf5, 0x0, 0x0,
    0x2f, 0x50, 0x0, 0x2, 0xf5, 0x0, 0x0, 0x2f,
    0x50, 0x0, 0x2, 0xf5, 0x0, 0x0, 0x2f, 0x50,
    0x0, 0x2, 0xf5, 0x0, 0x0, 0x2f, 0x50, 0x0,
    0x2, 0xf5, 0x0, 0x0, 0x2f, 0x50, 0x0, 0x2,
    0xf5, 0x0, 0x0, 0x2f, 0x50, 0x6, 0xac, 0xfd,
    0xa6,

    /* U+0032 "2" */
    0x3, 0x88, 0xbb, 0x30, 0x3, 0xf0, 0x0, 0x8f,
    0x10, 0xbb, 0x0, 0x0, 0xf7, 0x4, 0x20, 0x0,
    0xe, 0x90, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0x0, 0x5f, 0x20, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0x8, 0xb0, 0x0, 0x0, 0x4, 0xc0, 0x0,
    0x0, 0x1, 0xc1, 0x0, 0x0, 0x0, 0xb2, 0x0,
    0x0, 0x0, 0x93, 0x0, 0x0, 0x0, 0x79, 0x44,
    0x44, 0x44, 0x1d, 0xff, 0xff, 0xff, 0xf3,

    /* U+0033 "3" */
    0x0, 0x59, 0x9b, 0xa2, 0x0, 0x5f, 0x0, 0x9,
    0xe1, 0x8, 0x90, 0x0, 0x2f, 0x50, 0x0, 0x0,
    0x1, 0xf6, 0x0, 0x0, 0x0, 0x5f, 0x20, 0x0,
    0x0, 0x3d, 0x60, 0x0, 0x7, 0xce, 0x50, 0x0,
    0x0, 0x0, 0x1b, 0xb0, 0x0, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0x0, 0x0, 0xbc, 0x2, 0x0, 0x0,
    0xb, 0xc0, 0xf5, 0x0, 0x0, 0xe9, 0xa, 0xa0,
    0x0, 0x8e, 0x10, 0x7, 0x99, 0xb9, 0x10,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0, 0x7,
    0xf7, 0x0, 0x0, 0x0, 0x27, 0xe7, 0x0, 0x0,
    0x0, 0x90, 0xe7, 0x0, 0x0, 0x6, 0x30, 0xe7,
    0x0, 0x0, 0x18, 0x0, 0xe7, 0x0, 0x0, 0x91,
    0x0, 0xe7, 0x0, 0x4, 0x50, 0x0, 0xe7, 0x0,
    0x9, 0x0, 0x0, 0xe7, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xf9, 0x2, 0x22, 0x22, 0xe8, 0x21, 0x0,
    0x0, 0x0, 0xe7, 0x0, 0x0, 0x0, 0x0, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0xe7, 0x0,

    /* U+0035 "5" */
    0x0, 0xcf, 0xff, 0xff, 0xa0, 0x0, 0xb4, 0x44,
    0x44, 0x30, 0x0, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xc9, 0xbc, 0x81, 0x0, 0x0, 0x41,
    0x1, 0xbe, 0x10, 0x0, 0x0, 0x0, 0xe, 0xa0,
    0x0, 0x0, 0x0, 0xa, 0xe0, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x4, 0x0, 0x0, 0xa, 0xd0, 0x1f,
    0x50, 0x0, 0xe, 0x80, 0xa, 0xa0, 0x0, 0x9d,
    0x0, 0x0, 0x89, 0x9a, 0x80, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x45, 0x0, 0x0, 0x7, 0xa5,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x0, 0x7, 0xc0, 0x0, 0x0, 0x1, 0xf3,
    0x0, 0x0, 0x0, 0x8e, 0x29, 0xcb, 0x40, 0xc,
    0xe6, 0x0, 0x5f, 0x50, 0xe9, 0x0, 0x0, 0xbd,
    0xf, 0x90, 0x0, 0x7, 0xf0, 0xda, 0x0, 0x0,
    0x5f, 0x1b, 0xc0, 0x0, 0x7, 0xf0, 0x5f, 0x10,
    0x0, 0xab, 0x0, 0xca, 0x0, 0x3e, 0x20, 0x0,
    0x8a, 0x9a, 0x20, 0x0,

    /* U+0037 "7" */
    0xf, 0xff, 0xff, 0xff, 0xc0, 0x44, 0x44, 0x44,
    0x78, 0x0, 0x0, 0x0, 0x8, 0x30, 0x0, 0x0,
    0x0, 0xc0, 0x0, 0x0, 0x0, 0x67, 0x0, 0x0,
    0x0, 0xd, 0x20, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x0, 0x1f, 0x10,
    0x0, 0x0, 0x8, 0xb0, 0x0, 0x0, 0x0, 0xe5,
    0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0xc,
    0xa0, 0x0, 0x0, 0x3, 0xf4, 0x0, 0x0,

    /* U+0038 "8" */
    0x2, 0xa9, 0x9b, 0x30, 0x1, 0xe4, 0x0, 0x4e,
    0x10, 0x6d, 0x0, 0x0, 0xd6, 0x8, 0xc0, 0x0,
    0xd, 0x70, 0x6f, 0x20, 0x1, 0xf2, 0x0, 0xdd,
    0x20, 0x85, 0x0, 0x2, 0xcf, 0xc5, 0x0, 0x0,
    0x17, 0x5c, 0xf7, 0x0, 0x1c, 0x10, 0x8, 0xf6,
    0xa, 0x80, 0x0, 0xb, 0xe0, 0xe6, 0x0, 0x0,
    0x6f, 0xd, 0x80, 0x0, 0x7, 0xd0, 0x6e, 0x10,
    0x1, 0xd5, 0x0, 0x5b, 0x99, 0xa4, 0x0,

    /* U+0039 "9" */
    0x0, 0x1a, 0x99, 0x90, 0x0, 0x1, 0xe5, 0x0,
    0x7d, 0x0, 0xa, 0xc0, 0x0, 0xe, 0x60, 0xe,
    0x80, 0x0, 0xa, 0xc0, 0xf, 0x70, 0x0, 0x8,
    0xf0, 0xe, 0x90, 0x0, 0x6, 0xf1, 0xa, 0xe0,
    0x0, 0x6, 0xf0, 0x1, 0xea, 0x0, 0x3d, 0xd0,
    0x0, 0x19, 0xca, 0x6c, 0xa0, 0x0, 0x0, 0x0,
    0x1f, 0x40, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0x0, 0x5, 0xe2, 0x0, 0x0, 0x0, 0x6c, 0x10,
    0x0, 0x0, 0x3a, 0x80, 0x0, 0x0, 0x1, 0x60,
    0x0, 0x0, 0x0,

    /* U+003A ":" */
    0xc, 0xa0, 0xf, 0xe0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10,
    0xf, 0xe0, 0xc, 0xa0,

    /* U+003B ";" */
    0xc, 0xa0, 0xf, 0xe0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x60,
    0xf, 0xf0, 0x5, 0xe0, 0x2, 0x90, 0x2a, 0x10,
    0x50, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x8, 0x10, 0x0, 0x0, 0x5c,
    0x60, 0x0, 0x2, 0xb9, 0x10, 0x0, 0x8, 0xb2,
    0x0, 0x0, 0x5c, 0x50, 0x0, 0x0, 0xb, 0x80,
    0x0, 0x0, 0x0, 0x5, 0xc4, 0x0, 0x0, 0x0,
    0x0, 0x9b, 0x20, 0x0, 0x0, 0x0, 0x2b, 0x80,
    0x0, 0x0, 0x0, 0x5, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003D "=" */
    0x3c, 0xcc, 0xcc, 0xcc, 0xc9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0xcc, 0xcc, 0xcc,
    0xc9,

    /* U+003E ">" */
    0x63, 0x0, 0x0, 0x0, 0x2, 0xb9, 0x10, 0x0,
    0x0, 0x0, 0x5c, 0x60, 0x0, 0x0, 0x0, 0x7,
    0xb3, 0x0, 0x0, 0x0, 0x1, 0xa9, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x1a, 0xa1, 0x0,
    0x0, 0x7c, 0x30, 0x0, 0x4, 0xc7, 0x0, 0x0,
    0x8, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x17, 0x20, 0x0, 0x6f, 0xfa, 0x10, 0x3, 0x69,
    0xa0, 0x0, 0x0, 0x36, 0x0, 0x0, 0x1a, 0x0,
    0x0, 0x68, 0x0, 0x29, 0xe2, 0x5, 0xfb, 0x20,
    0xb, 0x70, 0x0, 0x9, 0x20, 0x0, 0x3, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0xc, 0x90, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x1, 0x69, 0x99, 0x99, 0x20, 0x0,
    0x0, 0x0, 0x5a, 0x30, 0x0, 0x1, 0x96, 0x0,
    0x0, 0x7, 0x80, 0x0, 0x0, 0x0, 0x7, 0x40,
    0x0, 0x49, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0x0, 0xd1, 0x0, 0x7, 0xcc, 0x67, 0x0, 0x54,
    0x6, 0x70, 0x0, 0x89, 0x0, 0xc7, 0x0, 0x36,
    0xc, 0x0, 0x4, 0xc0, 0x0, 0xd5, 0x0, 0x9,
    0xb, 0x0, 0xc, 0x50, 0x0, 0xf2, 0x0, 0x18,
    0x29, 0x0, 0x1f, 0x10, 0x3, 0xf0, 0x0, 0x36,
    0x39, 0x0, 0x3f, 0x0, 0x6, 0xc0, 0x0, 0x73,
    0x2b, 0x0, 0x4f, 0x0, 0x9, 0x90, 0x0, 0x90,
    0xd, 0x0, 0x1f, 0x82, 0x78, 0xb0, 0x19, 0x10,
    0xc, 0x20, 0x5, 0xb9, 0x20, 0x8a, 0x80, 0x0,
    0x3, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xb3, 0x0, 0x0, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x16, 0x99, 0x88, 0x30, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x4, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaa, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x75, 0x5f, 0x50, 0x0,
    0x0, 0x0, 0xc, 0x1, 0xfa, 0x0, 0x0, 0x0,
    0x2, 0xb0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0x66,
    0x0, 0x7f, 0x40, 0x0, 0x0, 0xb, 0x10, 0x2,
    0xf9, 0x0, 0x0, 0x1, 0xe9, 0x99, 0x9e, 0xe0,
    0x0, 0x0, 0x57, 0x0, 0x0, 0x7f, 0x30, 0x0,
    0xa, 0x20, 0x0, 0x1, 0xf8, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0xc, 0xd0, 0x6, 0xbd, 0x92, 0x0,
    0x39, 0xdf, 0xa6,

    /* U+0042 "B" */
    0x9, 0xbf, 0xca, 0xab, 0x92, 0x0, 0x0, 0x2f,
    0x70, 0x0, 0xae, 0x10, 0x0, 0x2f, 0x60, 0x0,
    0x1f, 0x60, 0x0, 0x2f, 0x60, 0x0, 0xf, 0x80,
    0x0, 0x2f, 0x60, 0x0, 0x4f, 0x30, 0x0, 0x2f,
    0x60, 0x3, 0xd6, 0x0, 0x0, 0x2f, 0xb9, 0xcd,
    0x50, 0x0, 0x0, 0x2f, 0x60, 0x1, 0x9d, 0x20,
    0x0, 0x2f, 0x60, 0x0, 0xb, 0xd0, 0x0, 0x2f,
    0x60, 0x0, 0x7, 0xf2, 0x0, 0x2f, 0x60, 0x0,
    0x6, 0xf2, 0x0, 0x2f, 0x60, 0x0, 0xa, 0xe0,
    0x0, 0x2f, 0x70, 0x0, 0x6f, 0x50, 0x9, 0xcf,
    0xc9, 0xaa, 0xa2, 0x0,

    /* U+0043 "C" */
    0x0, 0x3, 0x9b, 0xba, 0xa5, 0x0, 0x8, 0xe5,
    0x0, 0x0, 0xe5, 0x7, 0xf2, 0x0, 0x0, 0xb,
    0x52, 0xf8, 0x0, 0x0, 0x0, 0x85, 0x8f, 0x10,
    0x0, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xec, 0x0, 0x0, 0x0, 0x0, 0xe, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xbe, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x70, 0x0, 0x0, 0x6, 0x70, 0x8f, 0x10, 0x0,
    0x0, 0x97, 0x0, 0x9e, 0x40, 0x0, 0xc, 0x70,
    0x0, 0x3a, 0xba, 0x9a, 0x60,

    /* U+0044 "D" */
    0x9, 0xbf, 0xc9, 0xaa, 0x93, 0x0, 0x0, 0x2,
    0xf7, 0x0, 0x3, 0xe9, 0x0, 0x0, 0x2f, 0x70,
    0x0, 0x1, 0xf7, 0x0, 0x2, 0xf6, 0x0, 0x0,
    0x8, 0xf1, 0x0, 0x2f, 0x60, 0x0, 0x0, 0x2f,
    0x70, 0x2, 0xf6, 0x0, 0x0, 0x0, 0xf9, 0x0,
    0x2f, 0x60, 0x0, 0x0, 0xe, 0xc0, 0x2, 0xf6,
    0x0, 0x0, 0x0, 0xeb, 0x0, 0x2f, 0x60, 0x0,
    0x0, 0xf, 0x90, 0x2, 0xf6, 0x0, 0x0, 0x3,
    0xf6, 0x0, 0x2f, 0x60, 0x0, 0x0, 0x9e, 0x10,
    0x2, 0xf6, 0x0, 0x0, 0x2f, 0x60, 0x0, 0x2f,
    0x70, 0x0, 0x5e, 0x70, 0x0, 0x9c, 0xfc, 0xaa,
    0xa9, 0x20, 0x0,

    /* U+0045 "E" */
    0x9, 0xbf, 0xca, 0xaa, 0xad, 0x80, 0x2, 0xf7,
    0x0, 0x0, 0x88, 0x0, 0x2f, 0x70, 0x0, 0x5,
    0x90, 0x2, 0xf6, 0x0, 0x0, 0x27, 0x0, 0x2f,
    0x60, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x37,
    0x0, 0x0, 0x2f, 0x60, 0x5, 0x70, 0x0, 0x2,
    0xfc, 0x99, 0xc7, 0x0, 0x0, 0x2f, 0x60, 0x4,
    0x70, 0x0, 0x2, 0xf6, 0x0, 0x14, 0x0, 0x0,
    0x2f, 0x60, 0x0, 0x0, 0x30, 0x2, 0xf6, 0x0,
    0x0, 0xd, 0x0, 0x2f, 0x70, 0x0, 0x2, 0xe0,
    0x9c, 0xfc, 0xaa, 0xaa, 0xbd,

    /* U+0046 "F" */
    0x9, 0xbf, 0xca, 0xaa, 0xad, 0x90, 0x2, 0xf7,
    0x0, 0x0, 0x79, 0x0, 0x2f, 0x70, 0x0, 0x4,
    0xa0, 0x2, 0xf6, 0x0, 0x0, 0x18, 0x0, 0x2f,
    0x60, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x28,
    0x0, 0x0, 0x2f, 0x60, 0x4, 0x80, 0x0, 0x2,
    0xfc, 0x99, 0xc8, 0x0, 0x0, 0x2f, 0x60, 0x4,
    0x80, 0x0, 0x2, 0xf6, 0x0, 0x15, 0x0, 0x0,
    0x2f, 0x60, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x9b, 0xfd, 0x94, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3, 0xad, 0xba, 0xb6, 0x0, 0x0, 0x8,
    0xe5, 0x0, 0x0, 0xc8, 0x0, 0x7, 0xf2, 0x0,
    0x0, 0x8, 0x80, 0x2, 0xf8, 0x0, 0x0, 0x0,
    0x59, 0x0, 0x8f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xc0, 0x0,
    0x0, 0x8a, 0xbb, 0x91, 0xbe, 0x0, 0x0, 0x0,
    0xc, 0xc0, 0x9, 0xf1, 0x0, 0x0, 0x0, 0xcc,
    0x0, 0x2f, 0x80, 0x0, 0x0, 0xc, 0xc0, 0x0,
    0x8f, 0x20, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x8e,
    0x40, 0x0, 0xc, 0xb0, 0x0, 0x0, 0x3a, 0xdb,
    0x9a, 0x60, 0x0,

    /* U+0048 "H" */
    0x9, 0xbf, 0xd9, 0x20, 0x8, 0xbf, 0xda, 0x30,
    0x2, 0xf7, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0,
    0x2f, 0x70, 0x0, 0x0, 0x1f, 0x80, 0x0, 0x2,
    0xf6, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x0, 0xf, 0x80, 0x0, 0x2, 0xf6,
    0x0, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x2f, 0xc9,
    0x99, 0x99, 0xaf, 0x80, 0x0, 0x2, 0xf6, 0x0,
    0x0, 0x0, 0xf8, 0x0, 0x0, 0x2f, 0x60, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x2, 0xf6, 0x0, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x2f, 0x60, 0x0, 0x0,
    0xf, 0x80, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x1,
    0xf8, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x1f,
    0x80, 0x0, 0xac, 0xfd, 0xa2, 0x0, 0x9c, 0xfe,
    0xa4,

    /* U+0049 "I" */
    0xa, 0xcf, 0xda, 0x30, 0x2, 0xf7, 0x0, 0x0,
    0x2f, 0x70, 0x0, 0x2, 0xf7, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0x2, 0xf6, 0x0, 0x0, 0x2f, 0x60, 0x0,
    0x2, 0xf6, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x2,
    0xf7, 0x0, 0x0, 0x2f, 0x70, 0x0, 0xac, 0xfd,
    0xa3,

    /* U+004A "J" */
    0x0, 0x9b, 0xfe, 0xa5, 0x0, 0x0, 0xeb, 0x0,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0xdb, 0x0,
    0x0, 0x0, 0xdb, 0x0, 0x0, 0x0, 0xdb, 0x0,
    0x0, 0x0, 0xda, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0xda, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0xda, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x2, 0xf1, 0x0, 0x1e, 0xb9, 0x60, 0x0,
    0x6, 0x83, 0x0, 0x0,

    /* U+004B "K" */
    0xa, 0xcf, 0xda, 0x20, 0x8b, 0xfb, 0x60, 0x0,
    0x2f, 0x60, 0x0, 0x7, 0x60, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x39, 0x0, 0x0, 0x0, 0x2f, 0x60,
    0x1, 0xb0, 0x0, 0x0, 0x0, 0x2f, 0x60, 0xa,
    0x20, 0x0, 0x0, 0x0, 0x2f, 0x60, 0x79, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x54, 0xdf, 0x10, 0x0,
    0x0, 0x0, 0x2f, 0x79, 0x1f, 0x90, 0x0, 0x0,
    0x0, 0x2f, 0xd1, 0x8, 0xf2, 0x0, 0x0, 0x0,
    0x2f, 0x60, 0x1, 0xfa, 0x0, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x8f, 0x20, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0xe, 0xb0, 0x0, 0x0, 0x2f, 0x60, 0x0,
    0x7, 0xf3, 0x0, 0xa, 0xcf, 0xda, 0x20, 0x7b,
    0xfd, 0xa1,

    /* U+004C "L" */
    0xa, 0xcf, 0xda, 0x30, 0x0, 0x0, 0x2, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0,
    0x0, 0x2, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x60, 0x0,
    0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x60, 0x0, 0x2, 0x60, 0x2, 0xf7, 0x0,
    0x0, 0x68, 0x0, 0x2f, 0x70, 0x0, 0x9, 0x80,
    0x9c, 0xfc, 0xaa, 0xaa, 0xe7,

    /* U+004D "M" */
    0x2a, 0xdf, 0x50, 0x0, 0x0, 0x0, 0x2f, 0xea,
    0x50, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0x0, 0x0, 0x6a, 0xf2, 0x0, 0x0, 0x0, 0xad,
    0xb0, 0x0, 0x6, 0x4f, 0x80, 0x0, 0x0, 0x37,
    0xdb, 0x0, 0x0, 0x73, 0xbe, 0x0, 0x0, 0x9,
    0x1d, 0xb0, 0x0, 0x7, 0x35, 0xf4, 0x0, 0x0,
    0xa0, 0xdb, 0x0, 0x0, 0x73, 0xe, 0xa0, 0x0,
    0x55, 0xd, 0xb0, 0x0, 0x7, 0x30, 0x8f, 0x10,
    0xa, 0x0, 0xdb, 0x0, 0x0, 0x73, 0x2, 0xf6,
    0x1, 0x90, 0xd, 0xb0, 0x0, 0x7, 0x30, 0xc,
    0xc0, 0x73, 0x0, 0xdb, 0x0, 0x0, 0x73, 0x0,
    0x6f, 0x3b, 0x0, 0xd, 0xb0, 0x0, 0x7, 0x30,
    0x0, 0xfc, 0x70, 0x0, 0xdb, 0x0, 0x0, 0x73,
    0x0, 0xa, 0xf1, 0x0, 0xd, 0xb0, 0x2, 0xad,
    0xca, 0x10, 0x4b, 0x0, 0x7b, 0xfe, 0xa6,

    /* U+004E "N" */
    0x29, 0xcf, 0x40, 0x0, 0x9, 0xbe, 0xb5, 0x0,
    0x7f, 0xd0, 0x0, 0x0, 0xa, 0x0, 0x0, 0x77,
    0xf8, 0x0, 0x0, 0xa, 0x0, 0x0, 0x73, 0xaf,
    0x20, 0x0, 0xa, 0x0, 0x0, 0x73, 0x1e, 0xc0,
    0x0, 0xa, 0x0, 0x0, 0x73, 0x5, 0xf6, 0x0,
    0xa, 0x0, 0x0, 0x73, 0x0, 0xbf, 0x10, 0xa,
    0x0, 0x0, 0x73, 0x0, 0x2f, 0xa0, 0xa, 0x0,
    0x0, 0x73, 0x0, 0x7, 0xf5, 0xa, 0x0, 0x0,
    0x73, 0x0, 0x0, 0xce, 0xa, 0x0, 0x0, 0x73,
    0x0, 0x0, 0x2f, 0x9a, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x8, 0xfc, 0x0, 0x0, 0x73, 0x0, 0x0,
    0x0, 0xdd, 0x0, 0x1a, 0xdc, 0xa4, 0x0, 0x0,
    0x3d, 0x0,

    /* U+004F "O" */
    0x0, 0x6, 0xaa, 0xaa, 0x40, 0x0, 0x0, 0xab,
    0x0, 0x2, 0xd8, 0x0, 0x9, 0xd0, 0x0, 0x0,
    0x2f, 0x60, 0x3f, 0x60, 0x0, 0x0, 0x9, 0xe1,
    0x9f, 0x0, 0x0, 0x0, 0x4, 0xf6, 0xbe, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0xec, 0x0, 0x0, 0x0,
    0x0, 0xfb, 0xec, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0xbe, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x9f, 0x0,
    0x0, 0x0, 0x3, 0xf6, 0x3f, 0x60, 0x0, 0x0,
    0x9, 0xe1, 0xa, 0xd0, 0x0, 0x0, 0x1f, 0x60,
    0x1, 0xbb, 0x0, 0x2, 0xd8, 0x0, 0x0, 0x6,
    0xba, 0xaa, 0x40, 0x0,

    /* U+0050 "P" */
    0x9, 0xcf, 0xca, 0xab, 0x80, 0x0, 0x2, 0xf7,
    0x0, 0xa, 0xd0, 0x0, 0x2f, 0x70, 0x0, 0x1f,
    0x80, 0x2, 0xf6, 0x0, 0x0, 0xcb, 0x0, 0x2f,
    0x60, 0x0, 0xb, 0xd0, 0x2, 0xf6, 0x0, 0x0,
    0xda, 0x0, 0x2f, 0x60, 0x0, 0x3f, 0x40, 0x2,
    0xf6, 0x0, 0x4d, 0x80, 0x0, 0x2f, 0xca, 0xa8,
    0x20, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x60, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x8b, 0xfd, 0x94, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x6, 0xaa, 0xaa, 0x40, 0x0, 0x0, 0xab,
    0x0, 0x2, 0xe8, 0x0, 0x9, 0xd0, 0x0, 0x0,
    0x2f, 0x60, 0x2f, 0x60, 0x0, 0x0, 0xa, 0xe1,
    0x9f, 0x0, 0x0, 0x0, 0x4, 0xf6, 0xbe, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0xec, 0x0, 0x0, 0x0,
    0x0, 0xfb, 0xec, 0x0, 0x0, 0x0, 0x0, 0xfb,
    0xcd, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x9f, 0x0,
    0x0, 0x0, 0x3, 0xf6, 0x3f, 0x50, 0x0, 0x0,
    0x9, 0xf1, 0xb, 0xc0, 0x0, 0x0, 0x1f, 0x70,
    0x1, 0xca, 0x0, 0x1, 0xd9, 0x0, 0x0, 0x7,
    0xb9, 0xaa, 0x50, 0x0, 0x0, 0x0, 0xd, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf8, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x29, 0xca, 0x60,

    /* U+0052 "R" */
    0x9, 0xcf, 0xc9, 0xab, 0xa2, 0x0, 0x0, 0x2,
    0xf7, 0x0, 0x6, 0xf3, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0xc, 0xb0, 0x0, 0x2, 0xf6, 0x0, 0x0,
    0x9e, 0x0, 0x0, 0x2f, 0x60, 0x0, 0xa, 0xd0,
    0x0, 0x2, 0xf6, 0x0, 0x0, 0xe8, 0x0, 0x0,
    0x2f, 0x60, 0x1, 0xbb, 0x0, 0x0, 0x2, 0xfc,
    0x9b, 0xe5, 0x0, 0x0, 0x0, 0x2f, 0x60, 0x6,
    0xf2, 0x0, 0x0, 0x2, 0xf6, 0x0, 0xd, 0xa0,
    0x0, 0x0, 0x2f, 0x60, 0x0, 0x8f, 0x0, 0x0,
    0x2, 0xf6, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x2f,
    0x70, 0x0, 0xe, 0x90, 0x0, 0x9b, 0xfd, 0x93,
    0x0, 0x6d, 0xa5,

    /* U+0053 "S" */
    0x0, 0x19, 0xbb, 0xc9, 0x10, 0x1, 0xe5, 0x0,
    0x9, 0xb0, 0x8, 0xc0, 0x0, 0x5, 0xa0, 0xa,
    0xb0, 0x0, 0x2, 0x60, 0x8, 0xf2, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x3e,
    0xfe, 0x60, 0x0, 0x0, 0x0, 0x6d, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x0,
    0x6, 0xf3, 0xa, 0x0, 0x0, 0x2, 0xf4, 0xf,
    0x0, 0x0, 0x4, 0xf1, 0xf, 0x40, 0x0, 0x1d,
    0x70, 0x2, 0x99, 0xaa, 0xb4, 0x0,

    /* U+0054 "T" */
    0x7e, 0xaa, 0xaf, 0xea, 0xaa, 0xe5, 0x88, 0x0,
    0xe, 0xb0, 0x0, 0xa6, 0x85, 0x0, 0xe, 0xb0,
    0x0, 0x76, 0x62, 0x0, 0xe, 0xb0, 0x0, 0x34,
    0x0, 0x0, 0xd, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xb0, 0x0, 0x0, 0x0, 0x9,
    0xbf, 0xea, 0x70, 0x0,

    /* U+0055 "U" */
    0x1a, 0xdf, 0xb9, 0x10, 0x9, 0xbe, 0xa5, 0x0,
    0x7f, 0x20, 0x0, 0x0, 0xb, 0x0, 0x0, 0x7f,
    0x20, 0x0, 0x0, 0xb, 0x0, 0x0, 0x7f, 0x20,
    0x0, 0x0, 0xb, 0x0, 0x0, 0x7f, 0x20, 0x0,
    0x0, 0xb, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0xa,
    0x0, 0x0, 0x6f, 0x20, 0x0, 0x0, 0xa, 0x0,
    0x0, 0x6f, 0x20, 0x0, 0x0, 0xb, 0x0, 0x0,
    0x4f, 0x40, 0x0, 0x0, 0xa, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x0, 0x28, 0x0, 0x0, 0xd, 0xd0,
    0x0, 0x0, 0x92, 0x0, 0x0, 0x4, 0xfb, 0x30,
    0x29, 0x90, 0x0, 0x0, 0x0, 0x3b, 0xef, 0xc6,
    0x0, 0x0,

    /* U+0056 "V" */
    0x9c, 0xfd, 0xa3, 0x0, 0x6b, 0xfb, 0x60, 0x1f,
    0x80, 0x0, 0x0, 0xb, 0x0, 0x0, 0xce, 0x0,
    0x0, 0x3, 0x80, 0x0, 0x7, 0xf3, 0x0, 0x0,
    0x82, 0x0, 0x0, 0x1f, 0x80, 0x0, 0xb, 0x0,
    0x0, 0x0, 0xcd, 0x0, 0x2, 0x80, 0x0, 0x0,
    0x7, 0xf2, 0x0, 0x73, 0x0, 0x0, 0x0, 0x1f,
    0x70, 0xb, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x1,
    0x90, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x64, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x6a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcb, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x7b, 0xfd, 0xa2, 0x5, 0xaf, 0xea, 0x40, 0x5b,
    0xfb, 0x80, 0xf, 0xa0, 0x0, 0x0, 0xeb, 0x0,
    0x0, 0xc, 0x0, 0x0, 0xbe, 0x0, 0x0, 0x3e,
    0xf0, 0x0, 0x2, 0x90, 0x0, 0x7, 0xf2, 0x0,
    0x8, 0x5f, 0x40, 0x0, 0x65, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0xa0, 0xe8, 0x0, 0xa, 0x10, 0x0,
    0x0, 0xea, 0x0, 0x18, 0xa, 0xd0, 0x0, 0xb0,
    0x0, 0x0, 0xa, 0xe0, 0x5, 0x40, 0x5f, 0x10,
    0x28, 0x0, 0x0, 0x0, 0x6f, 0x20, 0x90, 0x1,
    0xf6, 0x6, 0x40, 0x0, 0x0, 0x1, 0xf6, 0xa,
    0x0, 0xc, 0xa0, 0xa0, 0x0, 0x0, 0x0, 0xd,
    0xa3, 0x70, 0x0, 0x8e, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x9e, 0x72, 0x0, 0x3, 0xf5, 0x80, 0x0,
    0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0xe, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x0,
    0xaf, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc5, 0x0,
    0x0, 0x6, 0xb0, 0x0, 0x0,

    /* U+0058 "X" */
    0x4a, 0xff, 0xa6, 0x3, 0xae, 0xca, 0x0, 0x8,
    0xf3, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x1e, 0xb0,
    0x0, 0x73, 0x0, 0x0, 0x0, 0x7f, 0x40, 0x19,
    0x0, 0x0, 0x0, 0x0, 0xdd, 0x8, 0x10, 0x0,
    0x0, 0x0, 0x5, 0xf9, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x37, 0xaf,
    0x10, 0x0, 0x0, 0x0, 0xa, 0x2, 0xf9, 0x0,
    0x0, 0x0, 0x6, 0x50, 0x8, 0xf2, 0x0, 0x0,
    0x1, 0xa0, 0x0, 0x1e, 0xb0, 0x0, 0x0, 0x93,
    0x0, 0x0, 0x6f, 0x40, 0x7, 0xbf, 0xb6, 0x0,
    0x8b, 0xfe, 0xa2,

    /* U+0059 "Y" */
    0x5b, 0xfe, 0xa5, 0x2, 0xad, 0xda, 0x0, 0xd,
    0xe0, 0x0, 0x0, 0x92, 0x0, 0x0, 0x5f, 0x50,
    0x0, 0x1a, 0x0, 0x0, 0x0, 0xed, 0x0, 0x8,
    0x30, 0x0, 0x0, 0x6, 0xf4, 0x0, 0xa0, 0x0,
    0x0, 0x0, 0xe, 0xb0, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x2a, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6a, 0xef,
    0xba, 0x10, 0x0,

    /* U+005A "Z" */
    0x1f, 0xaa, 0xaa, 0xaa, 0xef, 0x2, 0xe0, 0x0,
    0x0, 0x3f, 0x80, 0x3c, 0x0, 0x0, 0xd, 0xd0,
    0x2, 0x60, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x0,
    0xd, 0xd0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x0,
    0x0, 0x0, 0x2, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xbe, 0x10, 0x0, 0x5, 0x40, 0x5f, 0x60, 0x0,
    0x0, 0xa5, 0x1e, 0xc0, 0x0, 0x0, 0xd, 0x47,
    0xfc, 0xaa, 0xaa, 0xaa, 0xf3,

    /* U+005B "[" */
    0x1f, 0xdb, 0x40, 0xf4, 0x0, 0xf, 0x30, 0x0,
    0xf3, 0x0, 0xf, 0x30, 0x0, 0xf3, 0x0, 0xf,
    0x30, 0x0, 0xf3, 0x0, 0xf, 0x30, 0x0, 0xf3,
    0x0, 0xf, 0x30, 0x0, 0xf3, 0x0, 0xf, 0x30,
    0x0, 0xf3, 0x0, 0xf, 0x30, 0x0, 0xf3, 0x0,
    0xf, 0x40, 0x1, 0xfd, 0xa4,

    /* U+005C "\\" */
    0xb1, 0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0x2a,
    0x0, 0x0, 0x0, 0xc0, 0x0, 0x0, 0x8, 0x40,
    0x0, 0x0, 0x48, 0x0, 0x0, 0x0, 0xc0, 0x0,
    0x0, 0xa, 0x20, 0x0, 0x0, 0x66, 0x0, 0x0,
    0x1, 0xb0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x0,
    0x84, 0x0, 0x0, 0x3, 0x90, 0x0, 0x0, 0xc,
    0x0, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x5, 0x70,
    0x0, 0x0, 0x1b, 0x0, 0x0, 0x0, 0xc0,

    /* U+005D "]" */
    0x2a, 0xcf, 0x30, 0x0, 0xf3, 0x0, 0xf, 0x30,
    0x0, 0xf3, 0x0, 0xf, 0x30, 0x0, 0xf3, 0x0,
    0xf, 0x30, 0x0, 0xf2, 0x0, 0xf, 0x20, 0x0,
    0xf2, 0x0, 0xf, 0x20, 0x0, 0xf3, 0x0, 0xf,
    0x30, 0x0, 0xf3, 0x0, 0xf, 0x30, 0x0, 0xf3,
    0x0, 0xf, 0x32, 0xac, 0xf3,

    /* U+005E "^" */
    0x0, 0x7, 0xd0, 0x0, 0x0, 0x1c, 0x77, 0x0,
    0x0, 0x94, 0xc, 0x20, 0x3, 0xa0, 0x3, 0xb0,
    0xc, 0x10, 0x0, 0x94, 0x26, 0x0, 0x0, 0x18,

    /* U+005F "_" */
    0x1e, 0xee, 0xee, 0xee, 0xee, 0xe3,

    /* U+0060 "`" */
    0x3d, 0x10, 0x0, 0xcb, 0x0, 0x0, 0xb5, 0x0,
    0x0, 0x80,

    /* U+0061 "a" */
    0x0, 0x59, 0x9b, 0xa1, 0x0, 0x6, 0xf0, 0x0,
    0xca, 0x0, 0xa, 0xa0, 0x0, 0x8e, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x2, 0x78, 0xbf,
    0x0, 0x1, 0xb8, 0x10, 0x7f, 0x0, 0xc, 0x90,
    0x0, 0x7f, 0x0, 0xf, 0x70, 0x0, 0x8f, 0x0,
    0xd, 0xc1, 0x6, 0x9f, 0x10, 0x3, 0xde, 0xb3,
    0xb, 0xd5,

    /* U+0062 "b" */
    0x3, 0x67, 0x0, 0x0, 0x0, 0x2, 0x7d, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbb,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xbb, 0x2a, 0xdd, 0x70, 0x0, 0xb,
    0xe7, 0x0, 0x4f, 0x90, 0x0, 0xbc, 0x0, 0x0,
    0x7f, 0x20, 0xb, 0xc0, 0x0, 0x2, 0xf6, 0x0,
    0xbc, 0x0, 0x0, 0xf, 0x90, 0xb, 0xc0, 0x0,
    0x0, 0xf8, 0x0, 0xbc, 0x0, 0x0, 0x2f, 0x60,
    0xb, 0xc0, 0x0, 0x8, 0xf1, 0x0, 0xbd, 0x70,
    0x5, 0xf8, 0x3, 0xdf, 0x92, 0xbe, 0xc6, 0x0,

    /* U+0063 "c" */
    0x0, 0x7, 0xba, 0xa6, 0x0, 0xc, 0xa0, 0x0,
    0xe6, 0x7, 0xe0, 0x0, 0x9, 0xc0, 0xda, 0x0,
    0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0x1, 0xf8,
    0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0x1, 0x1, 0xed, 0x40, 0x16,
    0x60, 0x1, 0x9d, 0xfd, 0x60,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x25, 0x81, 0x0, 0x0, 0x0,
    0x7, 0xaf, 0x30, 0x0, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x20, 0x0, 0x0,
    0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x19, 0xed, 0x84, 0xf2, 0x0, 0x1d,
    0xb1, 0x2, 0xbf, 0x20, 0x8, 0xf1, 0x0, 0x4,
    0xf2, 0x0, 0xea, 0x0, 0x0, 0x4f, 0x20, 0xf,
    0x80, 0x0, 0x4, 0xf2, 0x1, 0xf7, 0x0, 0x0,
    0x4f, 0x20, 0xf, 0x90, 0x0, 0x4, 0xf2, 0x0,
    0xad, 0x0, 0x0, 0x4f, 0x20, 0x2, 0xe8, 0x0,
    0xa, 0xf2, 0x0, 0x2, 0xbc, 0x97, 0x4f, 0xb9,

    /* U+0065 "e" */
    0x0, 0x8, 0xa9, 0xb4, 0x0, 0x0, 0xd7, 0x0,
    0x1e, 0x50, 0x8, 0xd0, 0x0, 0x8, 0xc0, 0xe,
    0x90, 0x0, 0x7, 0xf0, 0xf, 0xc8, 0x88, 0x9c,
    0xb0, 0x1f, 0x80, 0x0, 0x0, 0x0, 0xe, 0xb0,
    0x0, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x0, 0x10,
    0x1, 0xed, 0x30, 0x15, 0x70, 0x0, 0x19, 0xdf,
    0xd6, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x6a, 0xfa, 0x0, 0x0, 0x88, 0x6,
    0xa0, 0x0, 0x2f, 0x10, 0x0, 0x0, 0x7, 0xd0,
    0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x3, 0xae,
    0xfb, 0xb6, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0xb, 0xc0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x0, 0xb, 0xc0, 0x0, 0x0, 0x0, 0xbc, 0x0,
    0x0, 0x0, 0xb, 0xc0, 0x0, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x0, 0xb, 0xd0, 0x0, 0x0, 0x3a,
    0xef, 0xa6, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x4b, 0x9a, 0x85, 0xa9, 0x3, 0xf2, 0x0,
    0xad, 0x43, 0xa, 0xb0, 0x0, 0x4f, 0x10, 0xc,
    0x90, 0x0, 0x2f, 0x20, 0x9, 0xb0, 0x0, 0x5f,
    0x0, 0x2, 0xe4, 0x1, 0xc8, 0x0, 0x0, 0x88,
    0x99, 0x50, 0x0, 0x5, 0x70, 0x0, 0x0, 0x0,
    0x8, 0xd6, 0x55, 0x53, 0x0, 0x1, 0xcf, 0xff,
    0xff, 0xc0, 0x5, 0x70, 0x0, 0x4, 0xf7, 0xe,
    0x0, 0x0, 0x0, 0xb8, 0x3f, 0x0, 0x0, 0x0,
    0xe4, 0xe, 0x60, 0x0, 0x19, 0x80, 0x2, 0xab,
    0xaa, 0xa4, 0x0,

    /* U+0068 "h" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbb, 0x6, 0xdf,
    0xb1, 0x0, 0x0, 0xbc, 0x95, 0x14, 0xf8, 0x0,
    0x0, 0xbe, 0x10, 0x0, 0xbc, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x9e, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x9e, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x9e, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x9e, 0x0, 0x29, 0xee, 0x91, 0x9, 0xdf, 0x92,

    /* U+0069 "i" */
    0x0, 0xca, 0x0, 0x0, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x3c,
    0xfd, 0x0, 0x0, 0xbd, 0x0, 0x0, 0xac, 0x0,
    0x0, 0xac, 0x0, 0x0, 0xac, 0x0, 0x0, 0xac,
    0x0, 0x0, 0xac, 0x0, 0x0, 0xac, 0x0, 0x0,
    0xbd, 0x0, 0x19, 0xef, 0xa1,

    /* U+006A "j" */
    0x0, 0x0, 0xd9, 0x0, 0x0, 0xeb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x0,
    0x3c, 0xfc, 0x0, 0x0, 0xcb, 0x0, 0x0, 0xcb,
    0x0, 0x0, 0xcb, 0x0, 0x0, 0xcb, 0x0, 0x0,
    0xbb, 0x0, 0x0, 0xbb, 0x0, 0x0, 0xcb, 0x0,
    0x0, 0xcb, 0x0, 0x0, 0xca, 0x0, 0x0, 0xc9,
    0x0, 0x0, 0xd8, 0x0, 0x0, 0xf4, 0x18, 0x44,
    0xc0, 0x2d, 0xe8, 0x0,

    /* U+006B "k" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbe, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0x0, 0xad, 0xda, 0x30, 0xb,
    0xc0, 0x1, 0xa1, 0x0, 0x0, 0xbc, 0x1, 0xa1,
    0x0, 0x0, 0xb, 0xc0, 0xc5, 0x0, 0x0, 0x0,
    0xbc, 0x9d, 0xd0, 0x0, 0x0, 0xb, 0xe2, 0x3f,
    0x60, 0x0, 0x0, 0xbc, 0x0, 0xbe, 0x0, 0x0,
    0xb, 0xc0, 0x2, 0xf7, 0x0, 0x0, 0xbc, 0x0,
    0x9, 0xf1, 0x2, 0xae, 0xf9, 0x20, 0x1f, 0xc8,

    /* U+006C "l" */
    0x0, 0x0, 0x0, 0x2b, 0xed, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0xbc, 0x0, 0x0, 0xbc, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0xbc, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0xbc, 0x0, 0x39, 0xee, 0x93,

    /* U+006D "m" */
    0x16, 0xb9, 0x7, 0xef, 0x90, 0x1a, 0xee, 0x70,
    0x1, 0x5d, 0xb8, 0x51, 0x6f, 0x6a, 0x41, 0x8f,
    0x20, 0x0, 0xbe, 0x10, 0x0, 0xec, 0x0, 0x0,
    0xf6, 0x0, 0xb, 0xc0, 0x0, 0xd, 0x90, 0x0,
    0xe, 0x80, 0x0, 0xbc, 0x0, 0x0, 0xc9, 0x0,
    0x0, 0xe8, 0x0, 0xb, 0xc0, 0x0, 0xd, 0xa0,
    0x0, 0xe, 0x80, 0x0, 0xbc, 0x0, 0x0, 0xda,
    0x0, 0x0, 0xe8, 0x0, 0xb, 0xc0, 0x0, 0xd,
    0xa0, 0x0, 0xf, 0x80, 0x0, 0xbc, 0x0, 0x0,
    0xda, 0x0, 0x0, 0xf8, 0x2, 0x9e, 0xe9, 0x13,
    0x9f, 0xe9, 0x3, 0x9f, 0xd8,

    /* U+006E "n" */
    0x16, 0xb9, 0x6, 0xdf, 0xa0, 0x0, 0x15, 0xdb,
    0x96, 0x14, 0xf7, 0x0, 0x0, 0xbe, 0x10, 0x0,
    0xbb, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x9d, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x9d, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x9d, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x9d, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x9e, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x9e, 0x0, 0x29, 0xee,
    0x91, 0x19, 0xef, 0x92,

    /* U+006F "o" */
    0x0, 0x8, 0xa9, 0xa5, 0x0, 0x0, 0xd8, 0x0,
    0xc, 0x90, 0x8, 0xe0, 0x0, 0x3, 0xf4, 0xe,
    0xa0, 0x0, 0x0, 0xf9, 0xf, 0x80, 0x0, 0x0,
    0xdc, 0xf, 0x80, 0x0, 0x0, 0xdc, 0xe, 0x90,
    0x0, 0x0, 0xf9, 0x9, 0xe0, 0x0, 0x3, 0xf4,
    0x0, 0xd7, 0x0, 0xc, 0x90, 0x0, 0x8, 0xa9,
    0xa6, 0x0,

    /* U+0070 "p" */
    0x16, 0xb9, 0x1a, 0xdd, 0x70, 0x1, 0x5d, 0xd7,
    0x0, 0x4e, 0x90, 0x0, 0xbc, 0x0, 0x0, 0x7f,
    0x20, 0xb, 0xc0, 0x0, 0x2, 0xf6, 0x0, 0xbc,
    0x0, 0x0, 0xf, 0x80, 0xb, 0xc0, 0x0, 0x0,
    0xf8, 0x0, 0xbc, 0x0, 0x0, 0x2f, 0x60, 0xb,
    0xc0, 0x0, 0x8, 0xf1, 0x0, 0xbe, 0x60, 0x5,
    0xf7, 0x0, 0xb, 0xc3, 0xce, 0xc6, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0x0, 0x0, 0xb, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xc0, 0x0, 0x0, 0x0, 0x2a, 0xef, 0xa5,
    0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x19, 0xdd, 0x90, 0x53, 0x0, 0x1d, 0xb1,
    0x2, 0x9f, 0x30, 0x8, 0xf1, 0x0, 0x4, 0xf3,
    0x0, 0xea, 0x0, 0x0, 0x4f, 0x20, 0xf, 0x80,
    0x0, 0x4, 0xf2, 0x1, 0xf8, 0x0, 0x0, 0x4f,
    0x20, 0xf, 0x90, 0x0, 0x4, 0xf2, 0x0, 0xae,
    0x0, 0x0, 0x4f, 0x20, 0x2, 0xfa, 0x10, 0x3a,
    0xf2, 0x0, 0x2, 0xbe, 0xc7, 0x3f, 0x20, 0x0,
    0x0, 0x0, 0x3, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x30, 0x0, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x30, 0x0, 0x0, 0x0,
    0x9c, 0xfc, 0x70,

    /* U+0072 "r" */
    0x16, 0xb9, 0x8, 0xec, 0x1, 0x5d, 0xb8, 0x68,
    0xc0, 0x0, 0xbd, 0x70, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0xb,
    0xc0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0xb, 0xc0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x2, 0x9e, 0xfa, 0x40, 0x0,

    /* U+0073 "s" */
    0x0, 0x7a, 0xab, 0x80, 0x8, 0xa0, 0x0, 0xc3,
    0xd, 0x80, 0x0, 0x72, 0xb, 0xe3, 0x0, 0x0,
    0x2, 0xdf, 0xd6, 0x0, 0x0, 0x6, 0xcf, 0xd1,
    0x0, 0x0, 0x4, 0xf8, 0xc, 0x0, 0x0, 0xb9,
    0xf, 0x0, 0x1, 0xe4, 0x7, 0xba, 0x9a, 0x40,

    /* U+0074 "t" */
    0x0, 0x79, 0x0, 0x0, 0xa, 0x80, 0x0, 0x0,
    0xd8, 0x0, 0x7, 0xaf, 0xdb, 0xb1, 0x0, 0xf8,
    0x0, 0x0, 0xf, 0x80, 0x0, 0x0, 0xf8, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0xf, 0x80, 0x0, 0x0, 0xf8, 0x0, 0x0, 0xd,
    0xc0, 0x11, 0x0, 0x4d, 0xda, 0x10,

    /* U+0075 "u" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0x90,
    0x2, 0xaf, 0xa0, 0x0, 0xe8, 0x0, 0x0, 0xda,
    0x0, 0xe, 0x80, 0x0, 0xd, 0x90, 0x0, 0xf8,
    0x0, 0x0, 0xd9, 0x0, 0xf, 0x80, 0x0, 0xd,
    0x90, 0x0, 0xf8, 0x0, 0x0, 0xd9, 0x0, 0xf,
    0x80, 0x0, 0xd, 0x90, 0x0, 0xda, 0x0, 0x2,
    0xf9, 0x0, 0x9, 0xf4, 0x16, 0x8d, 0x90, 0x0,
    0x1b, 0xfe, 0x70, 0xcf, 0xc0,

    /* U+0076 "v" */
    0x1a, 0xfe, 0xa2, 0x7, 0xbd, 0x70, 0xa, 0xf0,
    0x0, 0x6, 0x40, 0x0, 0x4f, 0x50, 0x0, 0xa0,
    0x0, 0x0, 0xeb, 0x0, 0x18, 0x0, 0x0, 0x8,
    0xf1, 0x6, 0x30, 0x0, 0x0, 0x2f, 0x60, 0x90,
    0x0, 0x0, 0x0, 0xcb, 0x17, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x20, 0x0, 0x0, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xa7, 0x0, 0x0,

    /* U+0077 "w" */
    0xa, 0xfe, 0x92, 0x9, 0xed, 0x90, 0x5a, 0xe9,
    0x0, 0xae, 0x0, 0x0, 0xeb, 0x0, 0x3, 0x70,
    0x0, 0x5f, 0x20, 0x4, 0x9f, 0x10, 0x7, 0x20,
    0x0, 0xf, 0x70, 0x8, 0xe, 0x60, 0xa, 0x0,
    0x0, 0xa, 0xc0, 0x9, 0x9, 0xb0, 0x18, 0x0,
    0x0, 0x5, 0xf1, 0x45, 0x4, 0xf1, 0x53, 0x0,
    0x0, 0x0, 0xf6, 0x80, 0x0, 0xe5, 0x80, 0x0,
    0x0, 0x0, 0xab, 0x80, 0x0, 0xab, 0x80, 0x0,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x4f, 0x40, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0xe, 0x0, 0x0,

    /* U+0078 "x" */
    0x6c, 0xfc, 0x50, 0x6c, 0xd7, 0x0, 0xeb, 0x0,
    0xa, 0x10, 0x0, 0x4f, 0x50, 0x64, 0x0, 0x0,
    0xa, 0xe3, 0x80, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x8,
    0x2c, 0xe0, 0x0, 0x0, 0x37, 0x2, 0xf9, 0x0,
    0x0, 0xb0, 0x0, 0x8f, 0x30, 0x7c, 0xc6, 0x4,
    0xaf, 0xe9,

    /* U+0079 "y" */
    0x19, 0xfe, 0x92, 0x6, 0xae, 0x80, 0xa, 0xe0,
    0x0, 0x4, 0x60, 0x0, 0x4f, 0x40, 0x0, 0x81,
    0x0, 0x0, 0xea, 0x0, 0x9, 0x0, 0x0, 0x8,
    0xf0, 0x3, 0x50, 0x0, 0x0, 0x2f, 0x50, 0x80,
    0x0, 0x0, 0x0, 0xbb, 0x8, 0x0, 0x0, 0x0,
    0x5, 0xf5, 0x40, 0x0, 0x0, 0x0, 0xf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x99, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x40, 0x0, 0x0, 0x0, 0x0, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x36, 0x0, 0x0, 0x0,
    0xa8, 0x2a, 0x0, 0x0, 0x0, 0xb, 0xfa, 0x10,
    0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x1f, 0x99, 0x99, 0xdf, 0x11, 0xd0, 0x0, 0x1f,
    0x70, 0x29, 0x0, 0xb, 0xd0, 0x0, 0x0, 0x5,
    0xf3, 0x0, 0x0, 0x1, 0xe9, 0x0, 0x0, 0x0,
    0xae, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x1, 0x10,
    0x1e, 0xa0, 0x0, 0x75, 0x9, 0xe1, 0x0, 0xb,
    0x42, 0xfc, 0x99, 0x99, 0xf3,

    /* U+007B "{" */
    0x0, 0x9, 0xca, 0x10, 0x8, 0xb0, 0x0, 0x0,
    0xb7, 0x0, 0x0, 0x9, 0x90, 0x0, 0x0, 0x5b,
    0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x1, 0xd0, 0x0, 0x18, 0xa2, 0x0, 0x0,
    0x5a, 0x40, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0,
    0xe0, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x6, 0xb0,
    0x0, 0x0, 0x98, 0x0, 0x0, 0xb, 0x70, 0x0,
    0x0, 0x7c, 0x10, 0x0, 0x0, 0x6a, 0x91,

    /* U+007C "|" */
    0x42, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94,
    0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94,
    0x94, 0x94, 0x94, 0x94, 0x94,

    /* U+007D "}" */
    0x4a, 0xc6, 0x0, 0x0, 0x1e, 0x40, 0x0, 0xb,
    0x70, 0x0, 0xd, 0x50, 0x0, 0xf, 0x10, 0x0,
    0x2d, 0x0, 0x0, 0x5a, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x5, 0x97, 0x0, 0x7, 0x94, 0x0, 0x3b,
    0x0, 0x0, 0x5a, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0xf, 0x20, 0x0, 0xd, 0x50, 0x0, 0xb, 0x70,
    0x0, 0x2e, 0x30, 0x49, 0xa3, 0x0,

    /* U+007E "~" */
    0x1b, 0xc8, 0x0, 0x4, 0x4a, 0x40, 0x6c, 0x20,
    0xb2, 0x90, 0x0, 0x3b, 0xc6, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xba, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0xdf, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x1, 0x6a,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x7c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xb7, 0x3f,
    0xf2, 0x0, 0x0, 0x5f, 0xff, 0xfe, 0x95, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x5, 0xff, 0x73, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0,
    0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x20, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0x69,
    0x8f, 0xf2, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0x20, 0x3, 0x58, 0xfe, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf2, 0x2d, 0xff, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0xfd, 0xb, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x4, 0xbd, 0xc8, 0x10,
    0xaf, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x51, 0x6, 0x88, 0x88, 0x88, 0x88, 0x88, 0x30,
    0x15, 0xf7, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0x8f, 0xfd, 0xcf, 0xf3, 0x33, 0x33, 0x33,
    0x6f, 0xec, 0xdf, 0xf2, 0xc, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0x70, 0x2f, 0xf2, 0xc, 0xe0, 0x0,
    0x0, 0x0, 0x3f, 0x80, 0x2f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf4, 0x2d,
    0xf9, 0x99, 0x99, 0x99, 0xbf, 0x92, 0x4f, 0xf2,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x2f,
    0xfb, 0xaf, 0xf1, 0x11, 0x11, 0x11, 0x5f, 0xda,
    0xbf, 0xf9, 0x8e, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0xc8, 0x9f, 0xf2, 0xc, 0xe0, 0x0, 0x0, 0x0,
    0x3f, 0x70, 0x2f, 0xf6, 0x4d, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0xa4, 0x6f, 0xfe, 0xef, 0xfb, 0xbb,
    0xbb, 0xbb, 0xcf, 0xfe, 0xef, 0xc2, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x2c,

    /* U+F00B "" */
    0x58, 0x88, 0x70, 0x28, 0x88, 0x88, 0x88, 0x88,
    0x85, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xd1, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xe1, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x58, 0x88, 0x60, 0x27, 0x88, 0x88,
    0x88, 0x88, 0x85, 0x47, 0x77, 0x50, 0x17, 0x77,
    0x77, 0x77, 0x77, 0x74, 0xff, 0xff, 0xf3, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xf1, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0x90, 0x9, 0xd1, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf9, 0x0, 0x9f, 0xfd,
    0x10, 0x0, 0x7, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xd1, 0x0, 0x7f, 0xff, 0xf9, 0x0, 0x0,
    0x2e, 0xff, 0xfd, 0x17, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xef, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x2d, 0xb0, 0x0, 0x0, 0x6, 0xe6, 0xd, 0xff,
    0xc0, 0x0, 0x6, 0xff, 0xf3, 0xcf, 0xff, 0xc0,
    0x6, 0xff, 0xff, 0x31, 0xdf, 0xff, 0xc7, 0xff,
    0xff, 0x50, 0x1, 0xdf, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x6, 0xff, 0xff, 0xdf,
    0xff, 0xc0, 0x6, 0xff, 0xff, 0x51, 0xdf, 0xff,
    0xc0, 0xff, 0xff, 0x50, 0x1, 0xdf, 0xff, 0x58,
    0xff, 0x50, 0x0, 0x1, 0xdf, 0xd0, 0x5, 0x30,
    0x0, 0x0, 0x1, 0x61, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0xcd, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xe3, 0x3, 0xff,
    0xa0, 0xb, 0xc1, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0x3f, 0xfa, 0x4, 0xff, 0xd1, 0x0, 0x4, 0xff,
    0xf6, 0x3, 0xff, 0xa0, 0x1e, 0xff, 0xa0, 0x0,
    0xdf, 0xf7, 0x0, 0x3f, 0xfa, 0x0, 0x2e, 0xff,
    0x40, 0x3f, 0xfc, 0x0, 0x3, 0xff, 0xa0, 0x0,
    0x6f, 0xfa, 0x8, 0xff, 0x60, 0x0, 0x3f, 0xfa,
    0x0, 0x0, 0xef, 0xf0, 0xaf, 0xf2, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0xb, 0xff, 0x1b, 0xff, 0x10,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xbf, 0xf1, 0x9f,
    0xf3, 0x0, 0x0, 0x24, 0x0, 0x0, 0xd, 0xff,
    0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf8, 0x0, 0x9f, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x9f, 0xfe, 0x10, 0x0, 0xcf, 0xfe,
    0x71, 0x0, 0x4, 0xcf, 0xff, 0x50, 0x0, 0x1,
    0xdf, 0xff, 0xfe, 0xdf, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xce, 0xfd, 0xa5,
    0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x4, 0x66, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x78, 0x17, 0xff, 0xff,
    0xff, 0x71, 0x87, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x3f, 0xff,
    0xff, 0xfe, 0x88, 0xef, 0xff, 0xff, 0xf3, 0x8,
    0xff, 0xff, 0xd0, 0x0, 0xd, 0xff, 0xff, 0x80,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0xbf, 0xff, 0x30, 0x0, 0x3, 0xff,
    0xfb, 0x0, 0x1, 0xcf, 0xff, 0x80, 0x0, 0x8,
    0xff, 0xfc, 0x10, 0x3e, 0xff, 0xff, 0xf6, 0x0,
    0x6f, 0xff, 0xff, 0xe3, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0x8e, 0xff, 0xff, 0xff, 0xe8, 0xfd, 0x0, 0x0,
    0x11, 0x1, 0x9f, 0xff, 0xf9, 0x10, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xee, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0x92, 0x0, 0x6b,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xe4, 0xa, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xf6, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0x31, 0xcf, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x25, 0x70,
    0xaf, 0xff, 0xf2, 0x0, 0x0, 0x1, 0xbf, 0xfa,
    0x8, 0xff, 0xb0, 0x7f, 0xff, 0x40, 0x0, 0x2,
    0xdf, 0xf8, 0xa, 0xff, 0xff, 0xd2, 0x5f, 0xff,
    0x50, 0x4, 0xff, 0xf5, 0x1d, 0xff, 0xff, 0xff,
    0xe4, 0x2e, 0xff, 0x70, 0xdf, 0xe3, 0x3e, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x1c, 0xff, 0x13, 0xb1,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0x60, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd,
    0x88, 0xbf, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x70, 0x3, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf7, 0x0, 0x3f, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x70,
    0x3, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf5, 0x0, 0x2f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x9, 0xaa, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xee, 0xef, 0xff, 0xfe, 0xee, 0xc0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x24, 0x44, 0x44, 0x7,
    0xff, 0x70, 0x44, 0x44, 0x42, 0xff, 0xff, 0xff,
    0xc1, 0x66, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x66, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x66, 0xc4,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x0, 0x4, 0x44, 0x44, 0x44, 0x44, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x6, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x50, 0x0, 0xbf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x10, 0x6f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xfa, 0xe, 0xff, 0xcc, 0xcc, 0x20, 0x0, 0x0,
    0xbc, 0xcc, 0xef, 0xf2, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0x88, 0x8e, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x15, 0x66, 0x40, 0x0, 0x5,
    0xcb, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0x92,
    0x7, 0xff, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x66, 0xff, 0x0, 0x8f, 0xff, 0xa4, 0x12,
    0x5b, 0xff, 0xfd, 0xff, 0x4, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xd, 0xff, 0x30,
    0x0, 0x0, 0x45, 0x46, 0xff, 0xff, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0x8f,
    0xf1, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0x2, 0x10, 0x0, 0x0, 0x0, 0x12, 0x22, 0x22,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x67, 0x77, 0x77, 0x75, 0x0, 0x0,
    0x0, 0x6, 0x73, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x3f, 0xf6, 0xff, 0xff, 0xee, 0xfd,
    0x0, 0x0, 0x0, 0xcf, 0xf1, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xa0, 0xff, 0xff,
    0xfb, 0x20, 0x0, 0x2, 0xbf, 0xfe, 0x10, 0xff,
    0x8d, 0xff, 0xfc, 0xa9, 0xcf, 0xff, 0xe2, 0x0,
    0xff, 0x61, 0x9f, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0xff, 0x70, 0x1, 0x7c, 0xee, 0xd9, 0x30,
    0x0, 0x0, 0x56, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0, 0x3,
    0xef, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xdf,
    0xff, 0xf0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x0,
    0x0, 0xbf, 0xf0, 0x0, 0x0, 0x0, 0xbe, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xf0, 0x7, 0x10, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x3f, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xd, 0xf3, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0x70, 0x8b, 0xbb,
    0xdf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x40, 0x0, 0x0, 0x2d, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0x0, 0x0, 0x40, 0x1c,
    0xf4, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x5f, 0xb0, 0x1e, 0xe1, 0x0, 0x0, 0x3, 0xef,
    0xff, 0x0, 0x0, 0xaf, 0xa0, 0x6f, 0x70, 0xdf,
    0xff, 0xff, 0xff, 0xf0, 0x7, 0x10, 0xbf, 0x30,
    0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0x3, 0xfd,
    0x3, 0xf9, 0xa, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x7, 0xf5, 0xe, 0xc0, 0x8f, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x4f, 0x70, 0xdd, 0x7,
    0xf3, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1d, 0xf3,
    0xf, 0xb0, 0x9f, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x3, 0xf7, 0x7, 0xf6, 0xc, 0xf0, 0x7b, 0xbb,
    0xdf, 0xff, 0xf0, 0x0, 0x3, 0xfe, 0x12, 0xfa,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x3, 0xff,
    0x40, 0xaf, 0x30, 0x0, 0x0, 0x0, 0xbf, 0xf0,
    0x0, 0x3c, 0x30, 0x6f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0x6f, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x90, 0x0, 0x0,

    /* U+F03E "" */
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xa3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xd5, 0x6e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0x50, 0x7,
    0xff, 0xff, 0xf5, 0x9, 0xff, 0xff, 0xff, 0xfb,
    0xbf, 0xff, 0xff, 0x50, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xcb, 0xff, 0xf5, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xfc, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xc0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x74, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x47, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F043 "" */
    0x0, 0x0, 0x6, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0x2c, 0xf8, 0x4f, 0xff,
    0xff, 0xff, 0xf0, 0x7f, 0xf2, 0x5c, 0xff, 0xff,
    0xfb, 0x0, 0xef, 0xe5, 0x8, 0xff, 0xff, 0x30,
    0x2, 0xef, 0xff, 0xff, 0xff, 0x50, 0x0, 0x1,
    0xaf, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x3,
    0x31, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x6b, 0x90, 0x0, 0x0, 0x3, 0xa2, 0x9f, 0xe0,
    0x0, 0x0, 0x4f, 0xf9, 0x9f, 0xe0, 0x0, 0x5,
    0xff, 0xfa, 0x9f, 0xe0, 0x0, 0x6f, 0xff, 0xfa,
    0x9f, 0xe0, 0x7, 0xff, 0xff, 0xfa, 0x9f, 0xe0,
    0x8f, 0xff, 0xff, 0xfa, 0x9f, 0xe9, 0xff, 0xff,
    0xff, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x9f, 0xfe,
    0xff, 0xff, 0xff, 0xfa, 0x9f, 0xe1, 0xdf, 0xff,
    0xff, 0xfa, 0x9f, 0xe0, 0x1c, 0xff, 0xff, 0xfa,
    0x9f, 0xe0, 0x0, 0xbf, 0xff, 0xfa, 0x9f, 0xe0,
    0x0, 0xa, 0xff, 0xfa, 0x9f, 0xe0, 0x0, 0x0,
    0x9f, 0xfa, 0x9f, 0xe0, 0x0, 0x0, 0x8, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x3a, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x3a, 0xbb, 0xb9, 0x10, 0x3, 0xab, 0xbb, 0x91,
    0xef, 0xff, 0xff, 0xa0, 0xe, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xfe, 0x40, 0x7, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x91,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x4a, 0x20, 0x0, 0x0, 0xa, 0xb4, 0xbf, 0xe3,
    0x0, 0x0, 0xf, 0xf8, 0xcf, 0xff, 0x40, 0x0,
    0xf, 0xf8, 0xcf, 0xff, 0xf5, 0x0, 0xf, 0xf8,
    0xcf, 0xff, 0xff, 0x60, 0xf, 0xf8, 0xcf, 0xff,
    0xff, 0xf7, 0xf, 0xf8, 0xcf, 0xff, 0xff, 0xff,
    0x9f, 0xf8, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xff, 0xff, 0xdf, 0xf8, 0xcf, 0xff, 0xff, 0xfc,
    0x1f, 0xf8, 0xcf, 0xff, 0xff, 0xb0, 0xf, 0xf8,
    0xcf, 0xff, 0xfa, 0x0, 0xf, 0xf8, 0xcf, 0xff,
    0x80, 0x0, 0xf, 0xf8, 0xbf, 0xf7, 0x0, 0x0,
    0xf, 0xf8, 0x7f, 0x60, 0x0, 0x0, 0xf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2, 0xca, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x34, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x42, 0x0, 0x3, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x10, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x20, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0,
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff, 0x50,
    0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f,
    0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0x20, 0x0, 0x0, 0x0, 0x3c, 0x60,

    /* U+F054 "" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc1,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x20, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0,
    0x0, 0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff,
    0x50, 0x0, 0x0, 0x9f, 0xff, 0x60, 0x0, 0x0,
    0x9f, 0xff, 0x60, 0x0, 0x0, 0xe, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x3d, 0x50, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x39, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x36, 0x77, 0x77, 0xef, 0xfc, 0x77, 0x77, 0x61,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xae, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x61,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x3, 0x68, 0x87, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xff, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xfd, 0x63, 0x25, 0xbf, 0xff, 0x70, 0x0, 0x0,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xa0, 0x0, 0x6, 0xff, 0xfd, 0x0, 0x8, 0xfc,
    0x20, 0x9f, 0xff, 0xa0, 0x2, 0xff, 0xff, 0x50,
    0x0, 0x8f, 0xfe, 0x12, 0xff, 0xff, 0x60, 0xcf,
    0xff, 0xf2, 0x16, 0x7f, 0xff, 0xf5, 0xe, 0xff,
    0xfe, 0x1e, 0xff, 0xff, 0x12, 0xff, 0xff, 0xff,
    0x70, 0xdf, 0xff, 0xf2, 0x6f, 0xff, 0xf3, 0xe,
    0xff, 0xff, 0xf3, 0xf, 0xff, 0xfb, 0x0, 0xbf,
    0xff, 0x90, 0x4f, 0xff, 0xf8, 0x5, 0xff, 0xfe,
    0x10, 0x1, 0xdf, 0xff, 0x40, 0x28, 0x84, 0x1,
    0xef, 0xff, 0x30, 0x0, 0x1, 0xbf, 0xff, 0x60,
    0x0, 0x4, 0xef, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x6e, 0xff, 0xfb, 0xbe, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x71,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x4a, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x60, 0x0, 0x15, 0x78, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xa6, 0xdf, 0xff,
    0xff, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xe8, 0x32, 0x5b, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x10,
    0x6, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xf8, 0x7f, 0xd3, 0x9, 0xff, 0xfb, 0x0,
    0x0, 0x1e, 0x70, 0x1, 0xbf, 0xfe, 0xff, 0xf2,
    0x1f, 0xff, 0xf7, 0x0, 0x9, 0xff, 0xa0, 0x0,
    0x8f, 0xff, 0xff, 0x70, 0xdf, 0xff, 0xf1, 0x0,
    0xcf, 0xff, 0xd1, 0x0, 0x5f, 0xff, 0xf9, 0xc,
    0xff, 0xff, 0x30, 0x5, 0xff, 0xff, 0x60, 0x0,
    0x2d, 0xff, 0xb0, 0xef, 0xff, 0xb0, 0x0, 0xa,
    0xff, 0xfc, 0x0, 0x0, 0xa, 0xff, 0xef, 0xff,
    0xe1, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x10, 0x0, 0x3, 0xef, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xcb, 0x80,
    0x1, 0xbf, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xdf, 0xfe, 0x70, 0x0, 0x8f, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xcd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x33,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf9, 0x0, 0x6f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfa, 0x0, 0x6f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb,
    0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfd, 0x0, 0x9f,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xcb, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff,
    0xfc, 0x0, 0x2, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x60, 0xa, 0xff, 0xff,
    0xff, 0xfe, 0x42, 0xcf, 0xff, 0xff, 0xff, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x23,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf9, 0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0x1,
    0x29, 0xff, 0x90, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xfe, 0x20,
    0x5, 0xff, 0xff, 0xff, 0xfd, 0x9a, 0xad, 0xff,
    0xd0, 0x5f, 0xff, 0xbd, 0xff, 0xe2, 0x0, 0x0,
    0xcf, 0x44, 0xff, 0xf9, 0x8, 0xfe, 0x20, 0x0,
    0x0, 0x4, 0x4f, 0xff, 0x90, 0x4, 0xd2, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xa1, 0x91, 0x6,
    0xf6, 0x0, 0x0, 0x2, 0xef, 0xfb, 0xc, 0xfc,
    0x8, 0xff, 0x60, 0xef, 0xff, 0xff, 0xb0, 0x1d,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xfc, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xfe, 0xdd, 0xdd, 0xb0,
    0x0, 0x0, 0x2d, 0xde, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfa, 0xcf, 0xfe, 0x30, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xc, 0xff, 0xe3, 0x0,
    0x6, 0xff, 0xf9, 0x0, 0x0, 0xcf, 0xfe, 0x30,
    0x5f, 0xff, 0x90, 0x0, 0x0, 0xc, 0xff, 0xe2,
    0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6,
    0x1b, 0x80, 0x0, 0x0, 0x0, 0x0, 0xb, 0x90,

    /* U+F078 "" */
    0x3, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0x20,
    0x6f, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2,
    0xaf, 0xfe, 0x30, 0x0, 0x0, 0x5, 0xff, 0xf6,
    0x1c, 0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0x90,
    0x1, 0xcf, 0xfe, 0x30, 0x5, 0xff, 0xf9, 0x0,
    0x0, 0x1c, 0xff, 0xe3, 0x5f, 0xff, 0x90, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0x90, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x26, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xa0,
    0x0, 0x44, 0x44, 0x44, 0x44, 0x42, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xfa, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xa0, 0xaf, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0xe, 0xfd, 0xef, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0xb, 0xe2, 0xdf, 0x67,
    0xf5, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x4,
    0x15, 0xfe, 0x3, 0x20, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x6f, 0xd6, 0xfe, 0x4f, 0xf1,
    0x0, 0x0, 0xdf, 0x94, 0x44, 0x44, 0x41, 0x3f,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x24, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x4f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0,

    /* U+F07B "" */
    0x17, 0x88, 0x88, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x44,
    0x44, 0x44, 0x30, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x47, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x11, 0x2f, 0xff, 0xf7, 0x11, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x24, 0x44, 0x42, 0x1f,
    0xff, 0xf6, 0x24, 0x44, 0x42, 0xff, 0xff, 0xfc,
    0x8, 0xbb, 0xa2, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x55, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x66, 0xc4,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb8,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x90,
    0x0, 0x0, 0x4, 0xa5, 0x0, 0x0, 0xaf, 0xff,
    0xd0, 0x0, 0x0, 0x7d, 0xff, 0xf4, 0x2, 0xcf,
    0xff, 0xe2, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xe9,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xfc, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xfd, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x35, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xfa, 0x0, 0x0, 0x1, 0x9c, 0xa1,
    0xaf, 0xfe, 0xff, 0x60, 0x0, 0x2e, 0xff, 0xf9,
    0xef, 0x60, 0xaf, 0xb0, 0x3, 0xef, 0xff, 0xb0,
    0xef, 0x92, 0xcf, 0x90, 0x3e, 0xff, 0xfa, 0x0,
    0x7f, 0xff, 0xff, 0xe6, 0xff, 0xff, 0xa0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x36, 0xef, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0xaf, 0xfe, 0xff, 0xc2, 0xdf, 0xff, 0xd1, 0x0,
    0xef, 0x60, 0xaf, 0xa0, 0x1c, 0xff, 0xfe, 0x20,
    0xef, 0x92, 0xcf, 0xa0, 0x0, 0xcf, 0xff, 0xe2,
    0x7f, 0xff, 0xff, 0x40, 0x0, 0xb, 0xff, 0xf9,
    0x8, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x58, 0x50,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf1, 0x68, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x7f, 0x80,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x7f, 0xf8,
    0x36, 0x62, 0xaf, 0xff, 0xff, 0xf1, 0x36, 0x66,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xf6, 0x22, 0x22,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf7, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x20, 0x0, 0x0,

    /* U+F0C7 "" */
    0x5, 0x66, 0x66, 0x66, 0x66, 0x64, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xef, 0xf9, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf6,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf9,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xc5, 0x7f, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfe, 0x0, 0x5, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfa, 0x0, 0x1, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfd, 0x0, 0x4, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0x93, 0x4d, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,

    /* U+F0C9 "" */
    0x79, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x95,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x30, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xa1, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x3d, 0xfe, 0x42,
    0xdf, 0xff, 0xff, 0xff, 0xfd, 0x26, 0xff, 0xff,
    0xf9, 0x19, 0xff, 0xff, 0xff, 0x91, 0xbf, 0xff,
    0xff, 0xff, 0xd3, 0x5f, 0xff, 0xe5, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0x99, 0x17, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x77, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F0E7 "" */
    0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xa5, 0x55, 0x40, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x1, 0x22, 0x23, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x8d, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x8b, 0xbc, 0xfa, 0xfd, 0xbb, 0x90, 0x0, 0x0,
    0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x95, 0xff, 0xff, 0xf1, 0x79, 0x0,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf1, 0x7f, 0xb0,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf1, 0x7f, 0xfa,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf2, 0x2, 0x21,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xdd, 0xdc,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x7a, 0xaa, 0x58, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x8, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xcf, 0xfa, 0x40, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f,
    0xf4, 0x4, 0xd0, 0x2f, 0x0, 0xf3, 0x3, 0xf0,
    0xf, 0xf4, 0xff, 0x40, 0x5d, 0x2, 0xf0, 0xf,
    0x40, 0x4f, 0x0, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0x22, 0xb7, 0x29, 0xa2, 0x4f, 0x42, 0xcf,
    0xff, 0x4f, 0xff, 0xf0, 0xa, 0x60, 0x79, 0x2,
    0xf2, 0xb, 0xff, 0xf4, 0xff, 0xff, 0xdd, 0xfe,
    0xdf, 0xfd, 0xef, 0xed, 0xff, 0xff, 0x4f, 0xf8,
    0x48, 0xe4, 0x44, 0x44, 0x44, 0x47, 0xf4, 0x5f,
    0xf4, 0xff, 0x40, 0x4d, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0xff, 0x4f, 0xf7, 0x48, 0xe4, 0x44,
    0x44, 0x44, 0x47, 0xf4, 0x4f, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8b, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x17, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x29,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x56,
    0x66, 0x66, 0x7f, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x58, 0x88, 0x88, 0x87, 0x6, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfe, 0xf, 0x90, 0x0, 0xff, 0xff,
    0xff, 0xfe, 0xf, 0xf9, 0x0, 0xff, 0xff, 0xff,
    0xfe, 0xf, 0xff, 0x90, 0xff, 0xff, 0xff, 0xfe,
    0xf, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0x32,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x14, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x36,
    0x89, 0xa9, 0x74, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc6,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x6, 0xff,
    0xff, 0xfc, 0x75, 0x43, 0x46, 0x9e, 0xff, 0xff,
    0xb1, 0x8, 0xff, 0xfe, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xff, 0xe2, 0xcf, 0xfa, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0x40, 0xb6, 0x0, 0x0, 0x59, 0xde, 0xfe, 0xc7,
    0x20, 0x0, 0x1b, 0x50, 0x0, 0x0, 0x5, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xfe, 0xde, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x40,
    0x0, 0x2, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x1, 0xb6, 0x0, 0x0, 0x0, 0x0, 0x1b, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0x5f, 0xf4, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x3f, 0xf5, 0xff, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xef,
    0x5f, 0xf4, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xf5, 0xff, 0x45, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F241 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x4d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x46, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x30, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F242 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4c,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x4c, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x46, 0x88, 0x88,
    0x88, 0x80, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F243 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x49,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0x9f, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x49, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0x9f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x44, 0x88, 0x87,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F244 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x9f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xef, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x2e, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xa1, 0x0, 0xcf, 0xff, 0xd4,
    0x9f, 0x55, 0x55, 0x55, 0x55, 0x55, 0x7f, 0xf7,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xaf, 0xff, 0xa0, 0x0,
    0xb, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xb2, 0x0,
    0x9c, 0x90, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0,
    0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0x2, 0xbb, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xf9, 0x9f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xbd, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x1, 0x7b, 0xdd, 0xb8, 0x20, 0x0, 0x0,
    0x5f, 0xff, 0xdf, 0xff, 0xf6, 0x0, 0x4, 0xff,
    0xff, 0x68, 0xff, 0xff, 0x40, 0xe, 0xff, 0xff,
    0x60, 0x8f, 0xff, 0xd0, 0x4f, 0xff, 0xff, 0x60,
    0x9, 0xff, 0xf3, 0x8f, 0xf6, 0xbf, 0x61, 0xc0,
    0x9f, 0xf7, 0xbf, 0xf6, 0xb, 0x60, 0xe2, 0x5f,
    0xf9, 0xdf, 0xff, 0x50, 0x20, 0x33, 0xff, 0xfb,
    0xef, 0xff, 0xf5, 0x0, 0x2e, 0xff, 0xfc, 0xef,
    0xff, 0xfc, 0x0, 0x7f, 0xff, 0xfc, 0xdf, 0xff,
    0xd1, 0x0, 0x9, 0xff, 0xfc, 0xcf, 0xfc, 0x13,
    0x50, 0x90, 0xaf, 0xfb, 0xaf, 0xf2, 0x4f, 0x60,
    0xf3, 0x2f, 0xf9, 0x6f, 0xfd, 0xff, 0x70, 0x52,
    0xef, 0xf6, 0x1f, 0xff, 0xff, 0x70, 0x2e, 0xff,
    0xf1, 0x9, 0xff, 0xff, 0x72, 0xef, 0xff, 0x90,
    0x0, 0xbf, 0xff, 0xae, 0xff, 0xfd, 0x10, 0x0,
    0x5, 0xcf, 0xff, 0xfd, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x5, 0x88, 0x88, 0x30, 0x0, 0x0,
    0x56, 0x66, 0x7f, 0xff, 0xff, 0xe6, 0x66, 0x63,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd8,
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0x1f, 0xf6, 0xaf, 0xc4, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0x1f, 0xf6, 0xaf, 0xc4, 0xff, 0xa0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x31, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x44, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x44, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0x44, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0x41, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x57, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x72, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0xbf, 0xff, 0xff, 0xf8,
    0xaf, 0xff, 0xa8, 0xff, 0xff, 0xf8, 0x0, 0xbf,
    0xff, 0xff, 0xfa, 0x0, 0xaf, 0xa0, 0xa, 0xff,
    0xff, 0x80, 0xbf, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x60, 0x3, 0xff, 0xff, 0xf8, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x9f, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xff, 0xb0, 0x3, 0xe3, 0x0,
    0xbf, 0xff, 0xf8, 0x0, 0x4f, 0xff, 0xff, 0xfe,
    0x13, 0xff, 0xf3, 0x1e, 0xff, 0xff, 0x80, 0x0,
    0x4f, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90,

    /* U+F7C2 "" */
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xcf,
    0x47, 0xf4, 0xd8, 0x4f, 0xf5, 0xc, 0xff, 0x3,
    0xe0, 0xc5, 0xe, 0xf5, 0xcf, 0xff, 0x3, 0xe0,
    0xc5, 0xe, 0xf5, 0xff, 0xff, 0x24, 0xe2, 0xc6,
    0x2e, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x1, 0x34,
    0x44, 0x44, 0x44, 0x42, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x10, 0x0, 0x3e, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0, 0x4f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10,
    0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x3e, 0xff, 0xfe, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xca, 0x0, 0x2e, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 74, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 89, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21, .adv_w = 108, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 42, .adv_w = 165, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 112, .adv_w = 158, .box_w = 8, .box_h = 18, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 184, .adv_w = 266, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 304, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 402, .adv_w = 56, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 413, .adv_w = 105, .box_w = 6, .box_h = 21, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 476, .adv_w = 105, .box_w = 5, .box_h = 21, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 529, .adv_w = 138, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 565, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 620, .adv_w = 94, .box_w = 4, .box_h = 6, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 632, .adv_w = 100, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 635, .adv_w = 94, .box_w = 4, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 641, .adv_w = 101, .box_w = 7, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 704, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 774, .adv_w = 136, .box_w = 7, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 823, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 886, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 949, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1019, .adv_w = 161, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1089, .adv_w = 161, .box_w = 9, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1157, .adv_w = 158, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1220, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1283, .adv_w = 162, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1358, .adv_w = 94, .box_w = 4, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1378, .adv_w = 94, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1404, .adv_w = 167, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1454, .adv_w = 167, .box_w = 10, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1479, .adv_w = 167, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1529, .adv_w = 124, .box_w = 6, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1574, .adv_w = 263, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1710, .adv_w = 207, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1801, .adv_w = 193, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1885, .adv_w = 199, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1962, .adv_w = 222, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2053, .adv_w = 188, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2130, .adv_w = 181, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2207, .adv_w = 213, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2298, .adv_w = 246, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2403, .adv_w = 117, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2452, .adv_w = 116, .box_w = 8, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2520, .adv_w = 211, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2618, .adv_w = 180, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2695, .adv_w = 281, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2814, .adv_w = 229, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2912, .adv_w = 221, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2996, .adv_w = 185, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3073, .adv_w = 221, .box_w = 12, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3181, .adv_w = 206, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3272, .adv_w = 163, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3342, .adv_w = 190, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3426, .adv_w = 230, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3524, .adv_w = 206, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3615, .adv_w = 303, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3748, .adv_w = 202, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3839, .adv_w = 198, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3930, .adv_w = 174, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4007, .adv_w = 99, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4052, .adv_w = 101, .box_w = 7, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4115, .adv_w = 99, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4160, .adv_w = 167, .box_w = 8, .box_h = 6, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 4184, .adv_w = 162, .box_w = 12, .box_h = 1, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4190, .adv_w = 125, .box_w = 5, .box_h = 4, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 4200, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4250, .adv_w = 184, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4338, .adv_w = 155, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4383, .adv_w = 181, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4471, .adv_w = 158, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4521, .adv_w = 112, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4589, .adv_w = 163, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4664, .adv_w = 191, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4760, .adv_w = 96, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4805, .adv_w = 90, .box_w = 6, .box_h = 20, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 4865, .adv_w = 175, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4953, .adv_w = 96, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5001, .adv_w = 281, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5086, .adv_w = 190, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5146, .adv_w = 172, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5196, .adv_w = 184, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5279, .adv_w = 175, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5362, .adv_w = 133, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5407, .adv_w = 136, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5447, .adv_w = 106, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5493, .adv_w = 188, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5554, .adv_w = 158, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5609, .adv_w = 241, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5689, .adv_w = 162, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5739, .adv_w = 159, .box_w = 11, .box_h = 15, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 5822, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5867, .adv_w = 108, .box_w = 7, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5930, .adv_w = 91, .box_w = 2, .box_h = 21, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 5951, .adv_w = 108, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6005, .adv_w = 167, .box_w = 9, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 6019, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6200, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6326, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6479, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6605, .adv_w = 198, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6690, .adv_w = 288, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6861, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7032, .adv_w = 324, .box_w = 21, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7211, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7382, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7529, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7700, .adv_w = 144, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7768, .adv_w = 216, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7873, .adv_w = 324, .box_w = 21, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8062, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8188, .adv_w = 198, .box_w = 13, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8312, .adv_w = 252, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8414, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8566, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8702, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8838, .adv_w = 252, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8940, .adv_w = 252, .box_w = 18, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9093, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9181, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9269, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9405, .adv_w = 252, .box_w = 16, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 9437, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9584, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9803, .adv_w = 324, .box_w = 22, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10012, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10165, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 10245, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 10325, .adv_w = 360, .box_w = 24, .box_h = 15, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 10505, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10631, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10802, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10983, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11119, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11271, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11407, .adv_w = 252, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11527, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11653, .adv_w = 180, .box_w = 13, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11777, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11929, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12081, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12228, .adv_w = 288, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12428, .adv_w = 216, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12561, .adv_w = 360, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12768, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12906, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13044, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13182, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13320, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13458, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13631, .adv_w = 252, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13764, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13916, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14097, .adv_w = 360, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14258, .adv_w = 216, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14391, .adv_w = 290, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 2, 0, 0, 3,
    1, 4, 5, 6, 0, 7, 8, 7,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 10, 10, 0, 0, 0,
    11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 20, 21, 22, 23, 20, 24,
    25, 26, 25, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 4, 36, 5, 0,
    37, 0, 38, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 41, 45, 45,
    39, 39, 49, 50, 51, 52, 53, 54,
    54, 55, 54, 56, 4, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 10,
    11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, 0, 0, 0,
    14, 15, 16, 17, 18, 17, 17, 17,
    18, 17, 17, 19, 17, 17, 20, 20,
    18, 17, 18, 17, 21, 22, 23, 24,
    25, 26, 27, 28, 5, 29, 6, 0,
    30, 0, 31, 32, 33, 33, 33, 34,
    35, 32, 36, 37, 32, 32, 38, 38,
    33, 39, 33, 38, 40, 41, 42, 43,
    43, 44, 45, 46, 5, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 0, -33,
    0, -33, -25, 0, 0, 0, 0, -38,
    0, -8, -3, 0, -3, 9, -1, 0,
    0, 0, 0, 0, 0, 0, -9, 0,
    -12, -1, -16, 0, 0, -2, -5, -9,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 0, 0, 0, 0, -12, 0, 4,
    0, 0, 0, 0, 0, -6, -6, 0,
    -1, 0, 0, 0, 0, 0, 0, -1,
    -2, 0, 0, 0, 0, 0, 3, 0,
    1, 0, 1, 0, 0, 0, 0, 0,
    0, -6, 0, -2, -7, -2, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, -17, -9, -20, -18, 0, -18, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, -6, -16, 4,
    0, 0, -12, 0, 0, -12, -12, 0,
    0, -9, 0, -6, 10, 0, -3, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, -1, 0, 0, 31, -5,
    0, -6, -3, -7, -6, -3, 5, -3,
    0, 0, 0, 0, 0, -16, 0, -12,
    0, -12, 0, 0, 0, 0, 0, -9,
    0, 0, 0, 0, 0, -6, -6, -15,
    -12, -6, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -31, 0, -31,
    0, 0, 0, 0, 0, -35, 0, -3,
    -6, 0, 0, 7, 0, 0, 0, -2,
    0, 0, 0, 0, -12, 0, -8, -2,
    -13, 0, 0, -2, -3, -7, 0, 0,
    0, 0, 0, -4, 0, -25, 0, 0,
    -16, -14, -41, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, -17, 0, 0,
    0, -25, -19, -41, -37, 0, -37, 0,
    -41, 0, -6, 0, -9, 0, -6, 0,
    2, 0, -9, -5, -12, -13, -28, 0,
    -12, -6, 0, 0, 0, 0, 0, -12,
    0, -15, 0, -15, -9, 0, 0, 0,
    0, -9, 0, 3, -6, -3, 0, -13,
    -3, -24, -22, -12, -22, -3, -12, 0,
    0, -1, 3, -2, 1, 0, 0, 0,
    0, 0, 0, 0, -2, -6, -2, 0,
    0, 0, -6, 0, 0, 0, 0, -43,
    -19, -43, -24, 0, 0, 0, 0, -22,
    0, 0, 0, 0, 0, 1, 0, 4,
    4, 0, 0, 0, 0, 0, -10, 0,
    -9, 0, -10, 0, 0, -5, -5, -6,
    0, -4, 0, -3, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -3, -9, -9, -2,
    -16, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, -35, 0, -35, -22, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -5, 0, 0, 0, 0,
    0, -2, 0, 1, 0, 0, 0, 0,
    0, -1, -1, -1, -1, 0, 0, -25,
    0, -4, 0, -1, -3, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 1, 0,
    0, -35, 0, 0, -6, -9, -34, 0,
    -10, 0, 0, 0, 0, 0, 0, 3,
    -3, -11, -4, -2, 0, -18, -21, -38,
    -31, 0, -28, 0, -19, 0, -1, 0,
    -5, 0, -1, 0, -2, 0, -9, 0,
    -6, -6, -16, 0, -14, 0, 0, -6,
    0, 0, 0, -5, -3, -8, 0, -8,
    -5, 0, 0, 0, 0, -9, -8, -2,
    -3, -5, 0, -6, -9, -16, -12, -9,
    -16, -2, -5, -8, 0, -3, 0, -5,
    0, 0, 0, 0, -3, 0, 0, -5,
    -5, -3, -2, 0, 0, 6, 0, 0,
    0, 1, 1, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, -3, -3, -3, 0, -6, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -3, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -6,
    0, -13, 1, -13, -9, 0, 0, 0,
    5, -16, -8, 3, -3, -6, 0, -5,
    -6, -14, -15, -12, -19, -2, -5, -22,
    0, -3, 0, 0, -3, 0, 0, 0,
    -3, -3, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -4,
    -4, 0, -4, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, -5, 0,
    0, -3, -5, 0, -6, 0, 0, 4,
    0, -2, -3, 0, 3, -30, -6, -30,
    -19, -1, -1, 0, -6, -22, -3, -3,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 4, -37, -12, -1, -13, -9,
    -19, -2, 0, -6, -12, -14, 0, -6,
    -6, -9, -6, -9, 0, 0, 0, 0,
    3, -1, 0, -2, 7, -2, -5, 0,
    0, 0, 4, -3, -2, 3, 0, 0,
    0, -2, -4, -11, -11, -5, -14, -2,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, -7, -5, -5, 0, 0, 0,
    -10, -6, -3, -7, -12, 0, -9, 0,
    0, 0, 0, 0, 0, 0, 0, -16,
    -3, -16, -9, -7, -7, 0, -4, -12,
    0, -3, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -23, -7, 0,
    -7, -12, -9, -2, -2, -8, -12, -9,
    -5, -9, -9, -6, -9, -6, 0, 0,
    0, 0, 0, 0, 0, 0, -9, 0,
    0, 0, 0, 0, 0, 3, 0, -9,
    -5, 0, 0, 0, -3, -2, -2, 0,
    -3, 2, 0, 0, -2, 0, -6, 0,
    -3, 0, 0, 0, -9, 0, -6, -6,
    -17, 0, -15, 0, 0, -41, 0, 0,
    0, -8, -31, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -5, -5, -2,
    -5, -22, -14, -34, -29, -5, -31, -5,
    -17, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -5, 0, -2, -6, -12, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, -9, -1, -9, -10, -4, -4, 0,
    0, -11, -2, -5, 0, 0, -2, 0,
    -3, 0, 0, -3, 0, -1, 0, -17,
    -4, 0, -3, -6, -6, 0, 0, -3,
    -6, -3, -3, -6, -6, -5, -6, -6,
    0, -8, 0, 0, 0, -6, -3, -12,
    3, -12, -6, 0, 0, 0, 6, -11,
    -7, 3, -2, -5, 0, -2, -5, -11,
    -8, -9, -12, 0, -2, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, -2, 0, -3, 7, -32, -1, -32,
    -16, 2, 2, 0, 0, -19, -3, 0,
    -3, 0, 2, 3, -6, -3, -3, -6,
    -7, 3, 3, -45, -1, -1, -6, 0,
    -6, 0, 0, -2, 0, -1, 5, 0,
    2, 0, 5, 0, 0, -12, 0, 0,
    0, 0, -4, 0, -5, 0, 0, 0,
    0, 0, 0, 3, 0, -6, -3, 0,
    0, -9, -10, -17, -15, 2, -15, 2,
    -6, 5, 0, 0, -3, 0, 0, 0,
    0, 0, -4, 0, -3, -4, -6, 0,
    -6, 0, 0, 0, 0, 0, 0, -3,
    0, -6, 0, -6, -5, 0, 0, 0,
    1, -5, -2, 0, -2, -2, -2, -3,
    -8, -8, -8, -3, -9, 0, 0, -12,
    0, -2, 0, -5, -2, 0, 0, 0,
    -3, 0, -3, -3, -5, -2, -5, 0,
    0, 9, 0, 0, -6, 0, 7, -25,
    -13, -25, -14, -1, -1, 0, -6, -20,
    0, -2, 0, 0, 0, 3, -3, 0,
    0, -1, 0, 2, 8, -25, -12, 0,
    -21, -3, -12, 0, 0, -9, -21, -16,
    0, -9, -12, -11, -12, -19, 0, 0,
    0, -4, -6, -3, 0, -25, -4, -25,
    -17, -4, -4, 0, 0, -23, -6, -8,
    -4, -3, -5, -3, -6, -5, -2, -3,
    0, -3, 0, -26, -12, -2, -9, -6,
    -12, -5, -2, -8, -13, -12, -6, -9,
    -7, -10, -9, -11, 0, 0, 0, -12,
    -16, 0, 0, -48, -23, -48, -27, -12,
    -12, 0, -15, -35, 0, -14, 0, 0,
    -6, 0, -3, 0, 0, 0, 0, -3,
    4, -49, -25, -1, -23, -16, -26, -9,
    -6, -18, -22, -26, -9, -19, -12, -22,
    -19, -20, 0, 0, 0, -10, -12, 0,
    0, -38, -20, -38, -28, -12, -12, 0,
    -15, -33, 0, -12, 0, -2, -6, 0,
    -3, 0, 0, 0, 0, -3, 4, -41,
    -23, -1, -23, -12, -26, -6, -6, -16,
    -22, -22, -8, -17, -13, -17, -13, -20,
    0, 0, 0, 0, -6, 0, -2, 0,
    -13, 0, 0, -2, -2, 0, 0, 0,
    0, -9, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 2, 0, 0, -1, 0,
    -10, 0, 0, 0, -5, 0, -10, -3,
    -9, -12, -20, 0, -15, 0, 0, 0,
    0, -12, -16, 0, 0, -33, -25, -33,
    -28, -19, -19, 0, -15, -28, 0, -16,
    0, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 4, -34, -24, -1, -29, -19,
    -30, -12, -8, -21, -28, -26, -19, -26,
    -22, -20, -22, -26, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, -2, 0, 0,
    0, -3, -6, -3, -3, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -4, -8, 0,
    -8, 0, 0, -25, 0, 0, 0, 0,
    0, 5, -6, 5, 0, 0, 0, 0,
    0, 0, 0, -9, 22, 0, 0, -19,
    -16, -30, -26, 0, -25, 0, -24, 0,
    0, 0, 0, 0, 1, 0, 27, 0,
    0, 0, -3, -1, -6, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 10, 0, -6, -25, -31, -47,
    -44, 0, -34, 0, 0, 0, -5, 0,
    -16, 0, 0, 0, 0, 0, -7, -5,
    -16, -15, -23, 0, -18, 0, 0, -19,
    0, 0, 0, 0, -14, 0, -3, 0,
    0, 0, 0, -1, 0, 2, -2, -4,
    -5, 0, 0, -17, -16, -30, -28, 0,
    -25, 0, -13, 0, 0, 0, 0, 0,
    -2, 0, 0, -1, -5, 0, 0, 0,
    -5, 0, -3, 0, 0, -12, 0, 0,
    0, -4, -8, -8, 3, -8, -5, -1,
    -1, -1, 0, -12, -7, 0, -7, -2,
    -2, -19, -12, -23, -20, -6, -23, 0,
    -6, -16, 0, -2, 0, 0, 0, 0,
    0, 0, -2, 0, -2, -2, 0, -5,
    -3, 0, 0, -1, 0, 0, 0, 0,
    -1, -1, 0, -1, 0, 0, 0, 0,
    0, -3, -4, 0, -6, 0, 0, -12,
    -9, -21, -19, -6, -19, 0, -1, -8,
    0, -3, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, -4, 0, -1, -6, 0,
    -1, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, 0, 0, -7, -7, -12,
    -11, 0, -13, 0, -1, 0, -2, 0,
    0, 0, -2, 0, 0, 0, -5, 0,
    0, -3, -3, 0, -3, 0, 0, -6,
    0, 0, 0, -5, -6, -8, 0, -8,
    -2, 0, 0, -1, 0, -5, -3, 0,
    -6, 0, 0, -15, -9, -20, -18, -6,
    -22, 0, -6, -12, -2, -3, 0, -2,
    0, 0, 0, 0, -2, 0, 0, 0,
    -2, 0, -2, 0, 12, 24, 0, 0,
    0, 28, 16, -11, -4, -11, -5, 0,
    0, 11, 0, -2, 5, 0, 7, 12,
    7, 17, 12, 17, 17, 15, 17, 12,
    30, -7, 0, -3, -5, 0, -1, 7,
    7, 0, 0, 0, 0, -3, 3, 0,
    3, 0, 0, 0, 0, -6, 0, 0,
    0, 0, -1, 0, 4, 0, 0, -1,
    0, 0, -2, 0, 15, -1, 0, -8,
    -10, -12, -12, 0, -21, 0, -5, 0,
    -5, -4, -2, 0, -2, 0, 6, 0,
    0, 0, 0, 0, 1, 0, 3, 0,
    0, -17, 0, 0, 0, -4, -21, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    -1, -1, -6, 0, 0, -18, -10, -26,
    -24, 0, -22, 0, -16, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -1, -1, -8, 0, -8, 0, -1, -2,
    0, -1, 0, 0, -1, 0, 0, 0,
    0, 0, 0, -2, 0, 0, -1, 0,
    0, 0, 0, -6, -6, -6, -6, 0,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -4, 0, -4, 0, 0, -1, 0, -1,
    0, 0, -1, -5, 0, -5, -4, -4,
    -4, -1, 0, -4, -2, -1, 0, 0,
    0, 0, -7, -5, -5, -3, -6, 0,
    0, -10, 0, -1, 0, 0, -3, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -1, 0, -6, 0, 0, -1, -1, -1,
    0, 5, -1, -1, -6, 0, 0, -12,
    -12, -21, -17, 1, -22, 2, -1, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    -3, -2, 0, 0, 0, 0, 0, 0,
    0, -7, 0, -1, 0, 3, -7, 9,
    0, 4, 3, 0, 10, -1, 0, -4,
    -7, 0, 10, -5, 0, -16, -13, -25,
    -21, -1, -25, 0, -4, -1, -2, -2,
    0, 0, 0, 0, 11, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 1,
    0, -3, 0, -7, 5, -27, -9, -27,
    -15, -2, -2, -1, -4, -17, -7, 0,
    -9, -5, 0, -1, -9, -15, -12, -17,
    -15, 0, 0, -20, -3, -3, -4, 0,
    -4, 0, 0, 0, 0, -2, 5, 0,
    3, 0, 3, 0, 0, -6, 0, 0,
    0, -4, -4, -7, 0, -7, 0, 0,
    0, 0, 0, -3, -6, 0, -8, -3,
    0, -10, -7, -22, -19, 0, -27, 0,
    -7, -12, 0, -3, 0, -1, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 5, 0, 0, -3, 0, 0, 0,
    -4, -12, -9, 0, -14, 2, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, 0,
    0, -12, 0, 0, 0, -7, -11, 0,
    0, 0, 0, -3, -3, 0, 0, 0,
    0, -4, -8, 0, 0, -12, -12, -24,
    -22, 0, -22, 0, -6, 0, 0, 0,
    -2, 0, -3, 0, -2, 0, -3, 0,
    0, 0, -6, 0, -6, 0, 0, -1,
    0, -5, 0, -10, -1, -29, -4, -29,
    -12, -1, -1, -1, -2, -20, -8, 0,
    -12, -6, 0, -4, -7, -16, -13, -16,
    -17, -2, 0, -24, -4, -7, -5, 0,
    -5, 0, 0, 0, 0, -3, 2, 0,
    3, 0, 3, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 2, 0, -2, -5, 0,
    0, -9, -9, -22, -16, 0, -20, 0,
    0, 0, -3, 0, -5, 0, -2, 0,
    0, 0, -5, 0, 0, 0, 0, 1,
    0, 0, 0, 0, 0, 0, 0, -3,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    -8, -19, -16, 0, -18, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 56,
    .right_class_cnt     = 46,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_SourceHanSerifSC_Regular_18 = {
#else
lv_font_t lv_font_SourceHanSerifSC_Regular_18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 18,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_SOURCEHANSERIFSC_REGULAR_18*/

