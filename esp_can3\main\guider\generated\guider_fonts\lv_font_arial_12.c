/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_ARIAL_12
#define LV_FONT_ARIAL_12 1
#endif

#if LV_FONT_ARIAL_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf5, 0xf5, 0xe4, 0xd3, 0xc2, 0xb1, 0x30, 0xe4,

    /* U+0022 "\"" */
    0x7c, 0x8b, 0x6b, 0x7a, 0x37, 0x47,

    /* U+0023 "#" */
    0x0, 0xb2, 0xd, 0x0, 0xd, 0x3, 0xa0, 0xbe,
    0xed, 0xee, 0x60, 0x67, 0xa, 0x30, 0x9, 0x40,
    0xd0, 0xb, 0xfd, 0xdf, 0xd6, 0xd, 0x5, 0x90,
    0x4, 0x90, 0x85, 0x0,

    /* U+0024 "$" */
    0x1, 0x7c, 0x40, 0x1, 0xe8, 0xcc, 0x70, 0x5b,
    0x9, 0x27, 0x3, 0xe2, 0x90, 0x0, 0x5, 0xde,
    0x81, 0x0, 0x0, 0xb9, 0xc0, 0x34, 0x9, 0xf,
    0x5, 0xd1, 0x94, 0xe0, 0x8, 0xee, 0xc3, 0x0,
    0x0, 0x90, 0x0,

    /* U+0025 "%" */
    0xa, 0xba, 0x0, 0x1b, 0x0, 0x3b, 0x9, 0x40,
    0xa3, 0x0, 0x3b, 0xa, 0x43, 0x90, 0x0, 0x8,
    0xb8, 0xc, 0x10, 0x0, 0x0, 0x0, 0x67, 0x2b,
    0xb3, 0x0, 0x1, 0xc0, 0xa4, 0x1d, 0x0, 0x9,
    0x40, 0xa3, 0xd, 0x0, 0x2a, 0x0, 0x3c, 0xb5,

    /* U+0026 "&" */
    0x0, 0xad, 0xd2, 0x0, 0x5, 0xc0, 0x7a, 0x0,
    0x2, 0xe2, 0xb6, 0x0, 0x1, 0xcf, 0x70, 0x0,
    0x1d, 0x77, 0xd2, 0xa0, 0x6b, 0x0, 0x9f, 0xb0,
    0x4e, 0x10, 0x6f, 0xc1, 0x6, 0xde, 0xb2, 0xa7,
    0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x7b, 0x6b, 0x37,

    /* U+0028 "(" */
    0x0, 0x84, 0x3, 0xa0, 0xb, 0x40, 0x1f, 0x0,
    0x3e, 0x0, 0x3d, 0x0, 0x1f, 0x0, 0xb, 0x40,
    0x3, 0xb0, 0x0, 0x94,

    /* U+0029 ")" */
    0xb, 0x0, 0x6, 0x80, 0x0, 0xe1, 0x0, 0xb5,
    0x0, 0x98, 0x0, 0x98, 0x0, 0xb5, 0x0, 0xe0,
    0x6, 0x80, 0xb, 0x0,

    /* U+002A "*" */
    0x1, 0xa0, 0x5, 0xbd, 0xb1, 0xb, 0xa5, 0x0,
    0x30, 0x30,

    /* U+002B "+" */
    0x0, 0x5, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0xf, 0x0, 0x4, 0xff, 0xff, 0xf5, 0x0, 0xf,
    0x0, 0x0, 0x0, 0xf0, 0x0,

    /* U+002C "," */
    0x20, 0xf4, 0x72, 0x50,

    /* U+002D "-" */
    0xaf, 0xfa,

    /* U+002E "." */
    0x20, 0xe4,

    /* U+002F "/" */
    0x0, 0xa2, 0x0, 0xc0, 0x4, 0x80, 0x9, 0x30,
    0xd, 0x0, 0x39, 0x0, 0x84, 0x0, 0xc0, 0x0,

    /* U+0030 "0" */
    0x4, 0xdd, 0xa0, 0x1, 0xe2, 0x8, 0x90, 0x5b,
    0x0, 0x1f, 0x7, 0x90, 0x0, 0xf1, 0x79, 0x0,
    0xf, 0x5, 0xb0, 0x1, 0xf0, 0x1e, 0x20, 0x8a,
    0x0, 0x4d, 0xdb, 0x10,

    /* U+0031 "1" */
    0x0, 0x97, 0x2b, 0xf7, 0x85, 0x97, 0x0, 0x97,
    0x0, 0x97, 0x0, 0x97, 0x0, 0x97, 0x0, 0x97,

    /* U+0032 "2" */
    0x6, 0xdd, 0xc3, 0x3, 0xe1, 0x5, 0xd0, 0x24,
    0x0, 0x2f, 0x0, 0x0, 0xa, 0x90, 0x0, 0xb,
    0xa0, 0x0, 0x2d, 0x80, 0x0, 0x1e, 0x50, 0x0,
    0x8, 0xff, 0xff, 0xf0,

    /* U+0033 "3" */
    0x6, 0xdd, 0xb1, 0x3, 0xe1, 0x8, 0x90, 0x0,
    0x0, 0xa8, 0x0, 0x4, 0xde, 0x20, 0x0, 0x0,
    0x6d, 0x2, 0x40, 0x0, 0xf1, 0x3e, 0x10, 0x6d,
    0x0, 0x6d, 0xdb, 0x20,

    /* U+0034 "4" */
    0x0, 0x5, 0xe0, 0x0, 0x2, 0xdf, 0x0, 0x0,
    0xc4, 0xf0, 0x0, 0xa5, 0x2f, 0x0, 0x69, 0x2,
    0xf0, 0xc, 0xee, 0xef, 0xe1, 0x0, 0x2, 0xf0,
    0x0, 0x0, 0x2f, 0x0,

    /* U+0035 "5" */
    0x9, 0xff, 0xfc, 0x0, 0xd3, 0x0, 0x0, 0xf,
    0xbe, 0xb1, 0x2, 0xb2, 0x8, 0xc0, 0x0, 0x0,
    0xf, 0x13, 0x50, 0x0, 0xf1, 0x3e, 0x10, 0x7b,
    0x0, 0x6d, 0xdb, 0x10,

    /* U+0036 "6" */
    0x3, 0xcd, 0xc2, 0x1, 0xe3, 0x6, 0xc0, 0x5b,
    0x0, 0x2, 0x8, 0xaa, 0xdb, 0x20, 0x8e, 0x30,
    0x6d, 0x6, 0xb0, 0x0, 0xf1, 0x2e, 0x20, 0x5d,
    0x0, 0x4c, 0xdc, 0x20,

    /* U+0037 "7" */
    0x6f, 0xff, 0xff, 0x10, 0x0, 0xa, 0x80, 0x0,
    0x6, 0xc0, 0x0, 0x0, 0xe3, 0x0, 0x0, 0x6b,
    0x0, 0x0, 0xc, 0x50, 0x0, 0x0, 0xf2, 0x0,
    0x0, 0x2f, 0x0, 0x0,

    /* U+0038 "8" */
    0x5, 0xdd, 0xb1, 0x1, 0xf1, 0x7, 0xa0, 0x1f,
    0x20, 0x8a, 0x0, 0x6f, 0xee, 0x20, 0x3d, 0x20,
    0x6d, 0x7, 0xa0, 0x0, 0xf1, 0x4e, 0x10, 0x5d,
    0x0, 0x6d, 0xdc, 0x20,

    /* U+0039 "9" */
    0x6, 0xdd, 0xa1, 0x3, 0xe1, 0x6, 0xb0, 0x7a,
    0x0, 0x1f, 0x4, 0xe2, 0x7, 0xf1, 0x6, 0xdd,
    0x6f, 0x10, 0x20, 0x2, 0xf0, 0x2e, 0x0, 0x99,
    0x0, 0x6d, 0xd9, 0x0,

    /* U+003A ":" */
    0xe4, 0x20, 0x0, 0x0, 0x20, 0xe4,

    /* U+003B ";" */
    0xf4, 0x20, 0x0, 0x0, 0x20, 0xf4, 0x72, 0x50,

    /* U+003C "<" */
    0x0, 0x0, 0x6, 0x30, 0x3, 0x9e, 0x91, 0x2c,
    0xb5, 0x0, 0x2, 0xcb, 0x50, 0x0, 0x0, 0x39,
    0xe9, 0x10, 0x0, 0x0, 0x63,

    /* U+003D "=" */
    0x4e, 0xee, 0xee, 0x50, 0x0, 0x0, 0x0, 0x4e,
    0xee, 0xee, 0x40,

    /* U+003E ">" */
    0x36, 0x0, 0x0, 0x1, 0x9e, 0x93, 0x0, 0x0,
    0x5, 0xbc, 0x20, 0x0, 0x5b, 0xc2, 0x19, 0xe9,
    0x30, 0x3, 0x60, 0x0, 0x0,

    /* U+003F "?" */
    0x6, 0xdd, 0xc3, 0x3, 0xe1, 0x5, 0xe0, 0x24,
    0x0, 0x1f, 0x0, 0x0, 0x1c, 0x70, 0x0, 0xd,
    0x60, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x4, 0xe0, 0x0,

    /* U+0040 "@" */
    0x0, 0x5, 0xbc, 0xcd, 0xa2, 0x0, 0x0, 0xaa,
    0x20, 0x0, 0x4c, 0x50, 0x7, 0x80, 0x7d, 0xc6,
    0xd1, 0xd1, 0xd, 0x7, 0xb0, 0xd, 0x90, 0x85,
    0x39, 0xe, 0x30, 0xb, 0x60, 0x76, 0x58, 0x1f,
    0x0, 0xe, 0x20, 0xc3, 0x2b, 0xe, 0x40, 0x9f,
    0x8, 0x90, 0xc, 0x44, 0xed, 0x6e, 0xe8, 0x1,
    0x2, 0xd7, 0x10, 0x0, 0x5, 0xd3, 0x0, 0x7,
    0xcd, 0xdd, 0xc8, 0x10,

    /* U+0041 "A" */
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0x5, 0xbe,
    0x20, 0x0, 0x0, 0xc, 0x69, 0x90, 0x0, 0x0,
    0x2f, 0x13, 0xf1, 0x0, 0x0, 0x9a, 0x0, 0xd7,
    0x0, 0x0, 0xfe, 0xee, 0xee, 0x0, 0x6, 0xd0,
    0x0, 0x1f, 0x50, 0xc, 0x60, 0x0, 0x8, 0xc0,

    /* U+0042 "B" */
    0x2f, 0xff, 0xfc, 0x30, 0x2f, 0x0, 0x7, 0xd0,
    0x2f, 0x0, 0x7, 0xc0, 0x2f, 0xff, 0xff, 0x40,
    0x2f, 0x0, 0x5, 0xf1, 0x2f, 0x0, 0x0, 0xe5,
    0x2f, 0x0, 0x4, 0xf1, 0x2f, 0xff, 0xed, 0x50,

    /* U+0043 "C" */
    0x0, 0x7d, 0xee, 0x80, 0x0, 0x9c, 0x20, 0x1b,
    0x90, 0x2f, 0x10, 0x0, 0x16, 0x5, 0xc0, 0x0,
    0x0, 0x0, 0x5d, 0x0, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x1, 0xc0, 0xa, 0xb2, 0x1, 0xba, 0x0,
    0x8, 0xde, 0xe8, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xfc, 0x50, 0x1, 0xf1, 0x0, 0x3e,
    0x50, 0x1f, 0x10, 0x0, 0x6d, 0x1, 0xf1, 0x0,
    0x2, 0xf0, 0x1f, 0x10, 0x0, 0x2f, 0x1, 0xf1,
    0x0, 0x6, 0xc0, 0x1f, 0x10, 0x3, 0xe5, 0x1,
    0xff, 0xfe, 0xc5, 0x0,

    /* U+0045 "E" */
    0xf, 0xff, 0xff, 0xf2, 0xf, 0x10, 0x0, 0x0,
    0xf, 0x10, 0x0, 0x0, 0xf, 0xff, 0xff, 0xc0,
    0xf, 0x10, 0x0, 0x0, 0xf, 0x10, 0x0, 0x0,
    0xf, 0x10, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf5,

    /* U+0046 "F" */
    0xf, 0xff, 0xff, 0xc0, 0xf2, 0x0, 0x0, 0xf,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xf2, 0xf, 0x20,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0xf, 0x20, 0x0,
    0x0, 0xf2, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x6c, 0xee, 0xb3, 0x0, 0x8d, 0x30, 0x6,
    0xf1, 0x1f, 0x20, 0x0, 0x6, 0x24, 0xd0, 0x0,
    0x0, 0x0, 0x4d, 0x0, 0xf, 0xff, 0x81, 0xf2,
    0x0, 0x0, 0x89, 0x8, 0xd4, 0x0, 0x4c, 0x80,
    0x5, 0xcf, 0xfc, 0x60,

    /* U+0048 "H" */
    0xf, 0x10, 0x0, 0x7b, 0xf, 0x10, 0x0, 0x7b,
    0xf, 0x10, 0x0, 0x7b, 0xf, 0xff, 0xff, 0xfb,
    0xf, 0x10, 0x0, 0x7b, 0xf, 0x10, 0x0, 0x7b,
    0xf, 0x10, 0x0, 0x7b, 0xf, 0x10, 0x0, 0x7b,

    /* U+0049 "I" */
    0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4,

    /* U+004A "J" */
    0x0, 0x1, 0xf1, 0x0, 0x1, 0xf1, 0x0, 0x1,
    0xf1, 0x0, 0x1, 0xf1, 0x0, 0x1, 0xf1, 0x43,
    0x1, 0xf0, 0x8a, 0x5, 0xe0, 0x1b, 0xfd, 0x40,

    /* U+004B "K" */
    0x2f, 0x0, 0x4, 0xe4, 0x2f, 0x0, 0x5e, 0x30,
    0x2f, 0x6, 0xd2, 0x0, 0x2f, 0x7f, 0x70, 0x0,
    0x2f, 0xc4, 0xf3, 0x0, 0x2f, 0x0, 0x6e, 0x10,
    0x2f, 0x0, 0xa, 0xc0, 0x2f, 0x0, 0x0, 0xd9,

    /* U+004C "L" */
    0x2f, 0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x2f,
    0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0x2, 0xf0, 0x0, 0x0, 0x2f, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf3,

    /* U+004D "M" */
    0x1f, 0xc0, 0x0, 0xa, 0xf1, 0x1f, 0xe2, 0x0,
    0xd, 0xf1, 0x1f, 0x88, 0x0, 0x58, 0xf1, 0x1f,
    0x2d, 0x0, 0xb2, 0xf1, 0x1f, 0xc, 0x31, 0xc0,
    0xf1, 0x1f, 0x7, 0x97, 0x60, 0xf1, 0x1f, 0x1,
    0xed, 0x10, 0xf1, 0x1f, 0x0, 0xba, 0x0, 0xf1,

    /* U+004E "N" */
    0x1f, 0x60, 0x0, 0x6b, 0x1f, 0xf2, 0x0, 0x6b,
    0x1f, 0x7d, 0x0, 0x6b, 0x1f, 0xb, 0x90, 0x6b,
    0x1f, 0x1, 0xe5, 0x6b, 0x1f, 0x0, 0x4e, 0x8b,
    0x1f, 0x0, 0x9, 0xfb, 0x1f, 0x0, 0x0, 0xdb,

    /* U+004F "O" */
    0x0, 0x7d, 0xfe, 0x91, 0x0, 0x9c, 0x30, 0x8,
    0xe1, 0x2f, 0x10, 0x0, 0xb, 0x85, 0xd0, 0x0,
    0x0, 0x6b, 0x5d, 0x0, 0x0, 0x7, 0xb2, 0xf1,
    0x0, 0x0, 0xb8, 0x9, 0xc3, 0x1, 0x8e, 0x10,
    0x6, 0xde, 0xe9, 0x10,

    /* U+0050 "P" */
    0x1f, 0xff, 0xfe, 0x70, 0x1f, 0x10, 0x3, 0xe4,
    0x1f, 0x10, 0x0, 0xb7, 0x1f, 0x10, 0x3, 0xf3,
    0x1f, 0xff, 0xfd, 0x60, 0x1f, 0x10, 0x0, 0x0,
    0x1f, 0x10, 0x0, 0x0, 0x1f, 0x10, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x7d, 0xee, 0x91, 0x0, 0xac, 0x20, 0x19,
    0xd0, 0x3f, 0x10, 0x0, 0xc, 0x76, 0xc0, 0x0,
    0x0, 0x7a, 0x6c, 0x0, 0x0, 0x8, 0xa3, 0xf1,
    0x0, 0x20, 0xc7, 0xa, 0xc2, 0x2d, 0xcd, 0x0,
    0x7, 0xde, 0xe9, 0xd6, 0x0, 0x0, 0x0, 0x1,
    0x40,

    /* U+0052 "R" */
    0x1f, 0xee, 0xee, 0xc2, 0x1, 0xf1, 0x0, 0x9,
    0xb0, 0x1f, 0x10, 0x0, 0xaa, 0x1, 0xff, 0xff,
    0xe9, 0x10, 0x1f, 0x10, 0x4e, 0x40, 0x1, 0xf1,
    0x0, 0x6e, 0x10, 0x1f, 0x10, 0x0, 0xc9, 0x1,
    0xf1, 0x0, 0x3, 0xf3,

    /* U+0053 "S" */
    0x3, 0xcf, 0xeb, 0x20, 0xe, 0x50, 0x7, 0xd0,
    0xf, 0x20, 0x0, 0x60, 0x6, 0xec, 0x84, 0x0,
    0x0, 0x3, 0x7c, 0xc0, 0x48, 0x0, 0x0, 0xd5,
    0x2f, 0x61, 0x4, 0xf2, 0x4, 0xcf, 0xfc, 0x40,

    /* U+0054 "T" */
    0xbf, 0xff, 0xff, 0xf1, 0x0, 0xe, 0x40, 0x0,
    0x0, 0xe, 0x40, 0x0, 0x0, 0xe, 0x40, 0x0,
    0x0, 0xe, 0x40, 0x0, 0x0, 0xe, 0x40, 0x0,
    0x0, 0xe, 0x40, 0x0, 0x0, 0xe, 0x40, 0x0,

    /* U+0055 "U" */
    0x1f, 0x10, 0x0, 0x7b, 0x1f, 0x10, 0x0, 0x7b,
    0x1f, 0x10, 0x0, 0x7b, 0x1f, 0x10, 0x0, 0x7b,
    0xf, 0x10, 0x0, 0x7b, 0xf, 0x20, 0x0, 0x89,
    0xb, 0xa1, 0x3, 0xe5, 0x1, 0xae, 0xfd, 0x60,

    /* U+0056 "V" */
    0xb7, 0x0, 0x0, 0x7b, 0x5d, 0x0, 0x0, 0xd4,
    0xe, 0x30, 0x3, 0xd0, 0x8, 0xa0, 0xa, 0x60,
    0x1, 0xf0, 0x1e, 0x0, 0x0, 0xa6, 0x79, 0x0,
    0x0, 0x4c, 0xc2, 0x0, 0x0, 0xd, 0xc0, 0x0,

    /* U+0057 "W" */
    0xb6, 0x0, 0x2f, 0x80, 0x1, 0xf0, 0x6b, 0x0,
    0x7b, 0xd0, 0x5, 0xb0, 0x2e, 0x0, 0xc3, 0xe2,
    0x9, 0x70, 0xd, 0x31, 0xe0, 0x97, 0xd, 0x20,
    0x9, 0x75, 0xa0, 0x4b, 0x2d, 0x0, 0x4, 0xaa,
    0x50, 0xe, 0x69, 0x0, 0x0, 0xde, 0x0, 0xb,
    0xd4, 0x0, 0x0, 0xbb, 0x0, 0x6, 0xf0, 0x0,

    /* U+0058 "X" */
    0x3f, 0x30, 0x1, 0xd4, 0x6, 0xd1, 0xb, 0x70,
    0x0, 0xaa, 0x8a, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0x3e, 0xf3, 0x0, 0x1, 0xe5, 0x6e, 0x10,
    0xb, 0x90, 0x9, 0xc0, 0x8c, 0x0, 0x0, 0xd8,

    /* U+0059 "Y" */
    0x9c, 0x0, 0x0, 0xc8, 0xd, 0x70, 0x7, 0xc0,
    0x3, 0xf2, 0x3e, 0x10, 0x0, 0x7c, 0xd4, 0x0,
    0x0, 0xc, 0x90, 0x0, 0x0, 0xa, 0x70, 0x0,
    0x0, 0xa, 0x70, 0x0, 0x0, 0xa, 0x70, 0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x1d, 0x60,
    0x0, 0x0, 0xd8, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0x9c, 0x0, 0x0, 0x7, 0xd1, 0x0, 0x0,
    0x5e, 0x20, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xf0,

    /* U+005B "[" */
    0x3f, 0xd1, 0x3e, 0x0, 0x3e, 0x0, 0x3e, 0x0,
    0x3e, 0x0, 0x3e, 0x0, 0x3e, 0x0, 0x3e, 0x0,
    0x3e, 0x0, 0x3f, 0xd1,

    /* U+005C "\\" */
    0xc0, 0x0, 0x84, 0x0, 0x39, 0x0, 0xd, 0x0,
    0x9, 0x30, 0x4, 0x80, 0x0, 0xc0, 0x0, 0xa2,

    /* U+005D "]" */
    0xae, 0x90, 0x89, 0x8, 0x90, 0x89, 0x8, 0x90,
    0x89, 0x8, 0x90, 0x89, 0x8, 0x9a, 0xe9,

    /* U+005E "^" */
    0x0, 0x52, 0x0, 0x1, 0xeb, 0x0, 0x8, 0x7d,
    0x20, 0xe, 0x16, 0x90, 0x79, 0x0, 0xe1,

    /* U+005F "_" */
    0x2b, 0xbb, 0xbb, 0xb9,

    /* U+0060 "`" */
    0x28, 0x0, 0x87,

    /* U+0061 "a" */
    0x8, 0xcd, 0xd4, 0x2, 0xb0, 0x6, 0xc0, 0x1,
    0x68, 0xcd, 0x3, 0xe7, 0x45, 0xd0, 0x8a, 0x0,
    0x9d, 0x1, 0xcc, 0xc8, 0xf0,

    /* U+0062 "b" */
    0x3d, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x3e,
    0xad, 0xc2, 0x3, 0xf4, 0x5, 0xd0, 0x3d, 0x0,
    0xf, 0x13, 0xc0, 0x0, 0xf1, 0x3f, 0x20, 0x4c,
    0x3, 0xda, 0xbb, 0x20,

    /* U+0063 "c" */
    0x6, 0xdd, 0xb1, 0x3e, 0x10, 0x79, 0x79, 0x0,
    0x0, 0x79, 0x0, 0x2, 0x3e, 0x10, 0x7a, 0x6,
    0xdd, 0xb1,

    /* U+0064 "d" */
    0x0, 0x0, 0x3d, 0x0, 0x0, 0x3d, 0x7, 0xdc,
    0x9d, 0x4d, 0x10, 0xad, 0x88, 0x0, 0x3d, 0x89,
    0x0, 0x3d, 0x3e, 0x10, 0x9d, 0x6, 0xdc, 0x9d,

    /* U+0065 "e" */
    0x5, 0xcc, 0xb2, 0x3, 0xd0, 0x3, 0xe0, 0x7e,
    0xdd, 0xde, 0x17, 0xa0, 0x0, 0x10, 0x3f, 0x20,
    0x5e, 0x0, 0x6d, 0xdc, 0x30,

    /* U+0066 "f" */
    0x8, 0xf9, 0xf, 0x20, 0xbf, 0xc3, 0xf, 0x10,
    0xf, 0x10, 0xf, 0x10, 0xf, 0x10, 0xf, 0x10,

    /* U+0067 "g" */
    0x7, 0xdc, 0x8e, 0x4d, 0x10, 0x9e, 0x88, 0x0,
    0x2e, 0x88, 0x0, 0x2e, 0x3d, 0x10, 0x9d, 0x6,
    0xdd, 0xad, 0x37, 0x0, 0x7a, 0xa, 0xdd, 0xb1,

    /* U+0068 "h" */
    0x3d, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x3e, 0xad,
    0xd4, 0x3f, 0x40, 0x6c, 0x3d, 0x0, 0x3d, 0x3d,
    0x0, 0x3d, 0x3d, 0x0, 0x3d, 0x3d, 0x0, 0x3d,

    /* U+0069 "i" */
    0x3d, 0x2, 0x3d, 0x3d, 0x3d, 0x3d, 0x3d, 0x3d,

    /* U+006A "j" */
    0x3, 0xd0, 0x2, 0x3, 0xd0, 0x3d, 0x3, 0xd0,
    0x3d, 0x3, 0xd0, 0x3d, 0x4, 0xc6, 0xe6,

    /* U+006B "k" */
    0x3d, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x3d, 0x3,
    0xe3, 0x3d, 0x3d, 0x30, 0x3f, 0xf9, 0x0, 0x3f,
    0x3e, 0x30, 0x3d, 0x5, 0xd0, 0x3d, 0x0, 0xa9,

    /* U+006C "l" */
    0x3d, 0x3d, 0x3d, 0x3d, 0x3d, 0x3d, 0x3d, 0x3d,

    /* U+006D "m" */
    0x3c, 0xab, 0xc5, 0xac, 0x90, 0x3f, 0x20, 0xac,
    0x0, 0xe2, 0x3d, 0x0, 0x89, 0x0, 0xd3, 0x3d,
    0x0, 0x88, 0x0, 0xd3, 0x3d, 0x0, 0x88, 0x0,
    0xd3, 0x3d, 0x0, 0x88, 0x0, 0xd3,

    /* U+006E "n" */
    0x3c, 0xab, 0xd3, 0x3f, 0x20, 0x5c, 0x3d, 0x0,
    0x3d, 0x3d, 0x0, 0x3d, 0x3d, 0x0, 0x3d, 0x3d,
    0x0, 0x3d,

    /* U+006F "o" */
    0x6, 0xdd, 0xb2, 0x3, 0xe1, 0x5, 0xd0, 0x88,
    0x0, 0xe, 0x28, 0x80, 0x0, 0xe2, 0x3e, 0x10,
    0x5e, 0x0, 0x6d, 0xdc, 0x20,

    /* U+0070 "p" */
    0x3d, 0xaa, 0xc3, 0x3, 0xf2, 0x3, 0xd0, 0x3c,
    0x0, 0xf, 0x23, 0xd0, 0x0, 0xf2, 0x3f, 0x30,
    0x6d, 0x3, 0xeb, 0xdc, 0x20, 0x3d, 0x0, 0x0,
    0x3, 0xd0, 0x0, 0x0,

    /* U+0071 "q" */
    0x7, 0xdc, 0x8d, 0x4d, 0x0, 0x9d, 0x88, 0x0,
    0x3d, 0x79, 0x0, 0x3d, 0x3e, 0x10, 0x9d, 0x5,
    0xdc, 0xad, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x3d,

    /* U+0072 "r" */
    0x3c, 0xcd, 0x3, 0xf2, 0x0, 0x3e, 0x0, 0x3,
    0xd0, 0x0, 0x3d, 0x0, 0x3, 0xd0, 0x0,

    /* U+0073 "s" */
    0xa, 0xcc, 0x90, 0x6a, 0x0, 0xa1, 0x2e, 0xb7,
    0x20, 0x0, 0x48, 0xe4, 0x69, 0x0, 0xa7, 0xa,
    0xdc, 0xa0,

    /* U+0074 "t" */
    0x0, 0x0, 0xb, 0x0, 0xf, 0x0, 0x9f, 0xc1,
    0xf, 0x0, 0xf, 0x0, 0xf, 0x0, 0xf, 0x10,
    0xa, 0xf2,

    /* U+0075 "u" */
    0x3d, 0x0, 0x3d, 0x3d, 0x0, 0x3d, 0x3d, 0x0,
    0x3d, 0x3d, 0x0, 0x4d, 0x2f, 0x10, 0xad, 0x8,
    0xec, 0x7d,

    /* U+0076 "v" */
    0xa7, 0x0, 0x6a, 0x4d, 0x0, 0xc4, 0xd, 0x32,
    0xd0, 0x7, 0x98, 0x70, 0x1, 0xee, 0x10, 0x0,
    0xab, 0x0,

    /* U+0077 "w" */
    0xd4, 0x5, 0xf0, 0x9, 0x67, 0x80, 0xae, 0x40,
    0xe1, 0x2d, 0xd, 0x78, 0x3c, 0x0, 0xd4, 0xb2,
    0xd8, 0x60, 0x8, 0xd7, 0xe, 0xe1, 0x0, 0x3f,
    0x20, 0x9c, 0x0,

    /* U+0078 "x" */
    0x5d, 0x0, 0xd5, 0xa, 0x99, 0x90, 0x0, 0xdd,
    0x0, 0x2, 0xee, 0x20, 0xc, 0x66, 0xc0, 0x8b,
    0x0, 0xb8,

    /* U+0079 "y" */
    0x98, 0x0, 0x5a, 0x3e, 0x0, 0xc4, 0xc, 0x42,
    0xd0, 0x5, 0xb8, 0x70, 0x0, 0xed, 0x10, 0x0,
    0x8a, 0x0, 0x0, 0xc3, 0x0, 0x4e, 0x80, 0x0,

    /* U+007A "z" */
    0x6c, 0xcc, 0xf8, 0x0, 0x6, 0xc0, 0x0, 0x5d,
    0x10, 0x4, 0xe2, 0x0, 0x3e, 0x30, 0x0, 0xbe,
    0xdd, 0xda,

    /* U+007B "{" */
    0x2, 0xda, 0x6, 0x90, 0x7, 0x80, 0xb, 0x50,
    0xab, 0x0, 0xc, 0x40, 0x7, 0x70, 0x7, 0x80,
    0x6, 0xa0, 0x1, 0xda,

    /* U+007C "|" */
    0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0,
    0xe0, 0xe0,

    /* U+007D "}" */
    0xad, 0x10, 0x9, 0x60, 0x8, 0x70, 0x5, 0xb0,
    0x0, 0xba, 0x3, 0xc0, 0x8, 0x70, 0x8, 0x70,
    0x9, 0x60, 0xad, 0x10,

    /* U+007E "~" */
    0x2c, 0xd8, 0x11, 0x56, 0x43, 0xaf, 0xe3, 0x0,
    0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xd0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F043 "" */
    0x0, 0x2, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0x2f, 0xf7, 0x0, 0x0, 0xa, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xfc, 0xe,
    0xff, 0xff, 0xff, 0xf2, 0xf9, 0xcf, 0xff, 0xff,
    0x3d, 0xc5, 0xff, 0xff, 0xf1, 0x6f, 0xa3, 0xbf,
    0xfa, 0x0, 0x8f, 0xff, 0xfb, 0x0, 0x0, 0x26,
    0x74, 0x0, 0x0,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x26, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfd,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x99,
    0x99, 0x99, 0x99, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x12, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9a, 0xaa, 0xaa, 0xaa,
    0xaa, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0xc3, 0xbf, 0xff, 0xff, 0xfb, 0x3c,
    0xff, 0x57, 0xff, 0xff, 0x75, 0xff, 0xff, 0xf9,
    0x3d, 0xd3, 0x9f, 0xff, 0xff, 0xff, 0xd5, 0x5d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x1, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x1, 0xef, 0xfa, 0x5d,
    0x10, 0x0, 0x1, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x1, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x1, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 53, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 53, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8, .adv_w = 68, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 14, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 42, .adv_w = 107, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 77, .adv_w = 171, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 117, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 153, .adv_w = 37, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 156, .adv_w = 64, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 176, .adv_w = 64, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 196, .adv_w = 75, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 206, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 227, .adv_w = 53, .box_w = 2, .box_h = 4, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 231, .adv_w = 64, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 233, .adv_w = 53, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 235, .adv_w = 53, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 251, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 279, .adv_w = 107, .box_w = 4, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 295, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 323, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 351, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 379, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 407, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 435, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 463, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 491, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 519, .adv_w = 53, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 525, .adv_w = 53, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 533, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 554, .adv_w = 112, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 565, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 586, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 614, .adv_w = 195, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 674, .adv_w = 128, .box_w = 10, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 714, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 746, .adv_w = 139, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 782, .adv_w = 139, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 818, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 850, .adv_w = 117, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 878, .adv_w = 149, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 914, .adv_w = 139, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 946, .adv_w = 53, .box_w = 2, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 954, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 978, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1010, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1038, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1078, .adv_w = 139, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1110, .adv_w = 149, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1146, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1178, .adv_w = 149, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1219, .adv_w = 139, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1255, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1287, .adv_w = 117, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1319, .adv_w = 139, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1351, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1383, .adv_w = 181, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1431, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1463, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1495, .adv_w = 117, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1527, .adv_w = 53, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1547, .adv_w = 53, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1563, .adv_w = 53, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1578, .adv_w = 90, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1593, .adv_w = 107, .box_w = 8, .box_h = 1, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1597, .adv_w = 64, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 1600, .adv_w = 107, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1621, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1649, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1667, .adv_w = 107, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1691, .adv_w = 107, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1712, .adv_w = 53, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1728, .adv_w = 107, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1752, .adv_w = 107, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1776, .adv_w = 43, .box_w = 2, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1784, .adv_w = 43, .box_w = 3, .box_h = 10, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1799, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1823, .adv_w = 43, .box_w = 2, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1831, .adv_w = 160, .box_w = 10, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1861, .adv_w = 107, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1879, .adv_w = 107, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1900, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1928, .adv_w = 107, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1952, .adv_w = 64, .box_w = 5, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1967, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1985, .adv_w = 53, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2003, .adv_w = 107, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2021, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2039, .adv_w = 139, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2066, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2084, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2108, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2126, .adv_w = 64, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2146, .adv_w = 50, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2156, .adv_w = 64, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2176, .adv_w = 112, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2187, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2265, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2319, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2385, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2439, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2480, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2558, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2636, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2713, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2791, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2854, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2932, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2962, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3007, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3098, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3152, .adv_w = 132, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3211, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3259, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3331, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3392, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3453, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3501, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 3567, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3606, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3645, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3706, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3723, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3786, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3890, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3988, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4054, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4093, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4132, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4212, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4266, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4344, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4429, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4490, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4562, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4623, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4684, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4738, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4797, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4869, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4941, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5004, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5095, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5154, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5244, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5312, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5380, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5448, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5516, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5584, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5672, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5737, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5809, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5894, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5962, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6021, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_arial_12 = {
#else
lv_font_t lv_font_arial_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 12,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_ARIAL_12*/

