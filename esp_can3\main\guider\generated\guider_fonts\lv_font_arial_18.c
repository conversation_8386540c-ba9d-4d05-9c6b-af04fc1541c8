/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 18 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_ARIAL_18
#define LV_FONT_ARIAL_18 1
#endif

#if LV_FONT_ARIAL_18

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x7f, 0x87, 0xf8, 0x7f, 0x86, 0xf7, 0x5f, 0x64,
    0xf5, 0x3f, 0x42, 0xf3, 0x1f, 0x20, 0xf1, 0x2,
    0x0, 0x0, 0x5e, 0x66, 0xf7,

    /* U+0022 "\"" */
    0x2f, 0xa4, 0xf8, 0x2f, 0xa4, 0xf8, 0x1f, 0x93,
    0xf7, 0xf, 0x60, 0xf5, 0xa, 0x30, 0xb2,

    /* U+0023 "#" */
    0x0, 0x7, 0xd0, 0x6, 0xe0, 0x0, 0xa, 0xa0,
    0x9, 0xb0, 0x0, 0xd, 0x70, 0xc, 0x80, 0x0,
    0x1f, 0x40, 0xf, 0x50, 0xdf, 0xff, 0xff, 0xff,
    0xfc, 0x46, 0xae, 0x66, 0x9f, 0x64, 0x0, 0xab,
    0x0, 0x8c, 0x0, 0x0, 0xc8, 0x0, 0xb9, 0x0,
    0x11, 0xf6, 0x11, 0xe7, 0x11, 0xdf, 0xff, 0xff,
    0xff, 0xfc, 0x39, 0xe4, 0x48, 0xf4, 0x43, 0x9,
    0xb0, 0x7, 0xd0, 0x0, 0xc, 0x80, 0xb, 0x90,
    0x0, 0xf, 0x50, 0xe, 0x60, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x97, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xe5, 0x0, 0x8, 0xf9, 0xa9, 0xcf, 0x40, 0xe,
    0xb0, 0x86, 0x1f, 0xa0, 0xf, 0x80, 0x86, 0x4,
    0x20, 0xf, 0xc0, 0x86, 0x0, 0x0, 0x8, 0xfb,
    0xa6, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xb3, 0x0,
    0x0, 0x1, 0xad, 0xff, 0x40, 0x0, 0x0, 0x86,
    0x2e, 0xd0, 0x2, 0x10, 0x86, 0x8, 0xf1, 0x4f,
    0x60, 0x86, 0x8, 0xf1, 0x1f, 0xc0, 0x86, 0xd,
    0xe0, 0x7, 0xfc, 0xba, 0xcf, 0x50, 0x0, 0x6d,
    0xff, 0xd5, 0x0, 0x0, 0x0, 0x86, 0x0, 0x0,
    0x0, 0x0, 0x43, 0x0, 0x0,

    /* U+0025 "%" */
    0xa, 0xfd, 0x40, 0x0, 0x6, 0xd0, 0x0, 0x7e,
    0x47, 0xf1, 0x0, 0xd, 0x60, 0x0, 0xd8, 0x0,
    0xe6, 0x0, 0x6d, 0x0, 0x0, 0xe7, 0x0, 0xd8,
    0x0, 0xe6, 0x0, 0x0, 0xd8, 0x0, 0xe7, 0x6,
    0xd0, 0x0, 0x0, 0x8d, 0x15, 0xf2, 0xe, 0x60,
    0x0, 0x0, 0x1b, 0xff, 0x60, 0x6d, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0xe5, 0x5, 0xde, 0x80,
    0x0, 0x0, 0x6, 0xd0, 0x2f, 0x74, 0xe6, 0x0,
    0x0, 0xe, 0x50, 0x8e, 0x0, 0x9c, 0x0, 0x0,
    0x6d, 0x0, 0x9c, 0x0, 0x8d, 0x0, 0x0, 0xe5,
    0x0, 0x7d, 0x0, 0x9c, 0x0, 0x6, 0xd0, 0x0,
    0x2f, 0x64, 0xe6, 0x0, 0xe, 0x50, 0x0, 0x5,
    0xde, 0x80,

    /* U+0026 "&" */
    0x0, 0x3, 0xcf, 0xd4, 0x0, 0x0, 0x0, 0x2f,
    0xc6, 0xcf, 0x30, 0x0, 0x0, 0x8f, 0x10, 0x2f,
    0x80, 0x0, 0x0, 0x8f, 0x30, 0x2f, 0x70, 0x0,
    0x0, 0x3f, 0xc2, 0xcf, 0x10, 0x0, 0x0, 0x8,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x2c, 0xff, 0x60,
    0x0, 0x0, 0x3, 0xfe, 0x5e, 0xf2, 0x7, 0x30,
    0xd, 0xe1, 0x4, 0xfd, 0x5f, 0x70, 0x2f, 0x90,
    0x0, 0x7f, 0xff, 0x20, 0x2f, 0x90, 0x0, 0xb,
    0xfb, 0x0, 0xe, 0xf1, 0x0, 0x1d, 0xff, 0x40,
    0x4, 0xfe, 0x89, 0xff, 0x6f, 0xf4, 0x0, 0x4c,
    0xfe, 0xa2, 0x3, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0027 "'" */
    0x3f, 0x93, 0xf9, 0x2f, 0x80, 0xf6, 0xa, 0x20,

    /* U+0028 "(" */
    0x0, 0x2e, 0x10, 0xb, 0x80, 0x4, 0xf1, 0x0,
    0xbb, 0x0, 0x2f, 0x60, 0x6, 0xf1, 0x0, 0xae,
    0x0, 0xc, 0xc0, 0x0, 0xdb, 0x0, 0xd, 0xc0,
    0x0, 0xcd, 0x0, 0xa, 0xe0, 0x0, 0x6f, 0x20,
    0x1, 0xf6, 0x0, 0xb, 0xb0, 0x0, 0x4f, 0x10,
    0x0, 0xb9, 0x0, 0x2, 0xf1,

    /* U+0029 ")" */
    0xa8, 0x0, 0x2, 0xf2, 0x0, 0xa, 0xb0, 0x0,
    0x4f, 0x20, 0x0, 0xf9, 0x0, 0xa, 0xd0, 0x0,
    0x7f, 0x10, 0x5, 0xf3, 0x0, 0x4f, 0x40, 0x5,
    0xf4, 0x0, 0x6f, 0x30, 0x8, 0xf1, 0x0, 0xbd,
    0x0, 0xe, 0x80, 0x4, 0xf2, 0x0, 0xab, 0x0,
    0x2f, 0x20, 0xa, 0x80, 0x0,

    /* U+002A "*" */
    0x0, 0x2f, 0x0, 0x1, 0x61, 0xf0, 0x60, 0x4e,
    0xef, 0xee, 0x30, 0xa, 0xf9, 0x0, 0x5, 0xf4,
    0xf5, 0x0, 0x35, 0x6, 0x20,

    /* U+002B "+" */
    0x0, 0x7, 0xe0, 0x0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0x88, 0x88,
    0xcf, 0x88, 0x84, 0x0, 0x7, 0xf0, 0x0, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x0, 0x7, 0xf0, 0x0,
    0x0,

    /* U+002C "," */
    0x5e, 0x56, 0xf6, 0x9, 0x51, 0xd1, 0x24, 0x0,

    /* U+002D "-" */
    0x4a, 0xaa, 0xa4, 0x6f, 0xff, 0xf7,

    /* U+002E "." */
    0x5e, 0x65, 0xf7,

    /* U+002F "/" */
    0x0, 0x6, 0xd0, 0x0, 0xa9, 0x0, 0xe, 0x50,
    0x3, 0xf1, 0x0, 0x7c, 0x0, 0xb, 0x80, 0x0,
    0xf4, 0x0, 0x4f, 0x0, 0x8, 0xb0, 0x0, 0xc7,
    0x0, 0x1f, 0x30, 0x5, 0xe0, 0x0, 0x9a, 0x0,
    0xd, 0x60, 0x0,

    /* U+0030 "0" */
    0x0, 0x2a, 0xee, 0xa1, 0x0, 0x1, 0xee, 0x77,
    0xfd, 0x0, 0x8, 0xf2, 0x0, 0x4f, 0x60, 0xd,
    0xb0, 0x0, 0xd, 0xc0, 0x1f, 0x80, 0x0, 0x9,
    0xf0, 0x2f, 0x60, 0x0, 0x8, 0xf1, 0x3f, 0x60,
    0x0, 0x7, 0xf2, 0x3f, 0x60, 0x0, 0x7, 0xf2,
    0x2f, 0x60, 0x0, 0x8, 0xf1, 0x1f, 0x80, 0x0,
    0x9, 0xf0, 0xd, 0xb0, 0x0, 0xd, 0xc0, 0x8,
    0xf2, 0x0, 0x4f, 0x60, 0x1, 0xee, 0x77, 0xec,
    0x0, 0x0, 0x2b, 0xfe, 0xa1, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x9b, 0x0, 0x6, 0xfb, 0x0, 0x7f,
    0xfb, 0xd, 0xfa, 0xeb, 0xe, 0x50, 0xeb, 0x0,
    0x0, 0xeb, 0x0, 0x0, 0xeb, 0x0, 0x0, 0xeb,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0xeb, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0xeb, 0x0, 0x0, 0xeb, 0x0,
    0x0, 0xeb,

    /* U+0032 "2" */
    0x0, 0x4c, 0xff, 0xc3, 0x0, 0x3, 0xfd, 0x77,
    0xdf, 0x40, 0xc, 0xe0, 0x0, 0x1e, 0xc0, 0xf,
    0x90, 0x0, 0xa, 0xf0, 0x1, 0x10, 0x0, 0xb,
    0xf0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0,
    0x0, 0xcf, 0x30, 0x0, 0x0, 0xb, 0xf6, 0x0,
    0x0, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x1c, 0xf7,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x9,
    0xf6, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x99, 0x99,
    0x90, 0x6f, 0xff, 0xff, 0xff, 0xf1,

    /* U+0033 "3" */
    0x0, 0x3b, 0xfe, 0x91, 0x0, 0x3, 0xfd, 0x68,
    0xfc, 0x0, 0xb, 0xe1, 0x0, 0x5f, 0x50, 0xb,
    0x80, 0x0, 0x1f, 0x80, 0x0, 0x0, 0x0, 0x4f,
    0x60, 0x0, 0x0, 0x14, 0xed, 0x0, 0x0, 0x0,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x44, 0xbf, 0x60,
    0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0,
    0x8, 0xf2, 0x1e, 0x70, 0x0, 0x9, 0xf1, 0xe,
    0xd0, 0x0, 0x1e, 0xd0, 0x4, 0xfd, 0x67, 0xef,
    0x30, 0x0, 0x4c, 0xff, 0xb2, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x7f, 0xf6, 0x0, 0x0,
    0x1, 0xfa, 0xf6, 0x0, 0x0, 0xa, 0xd3, 0xf6,
    0x0, 0x0, 0x4f, 0x33, 0xf6, 0x0, 0x0, 0xe9,
    0x3, 0xf6, 0x0, 0x8, 0xe1, 0x3, 0xf6, 0x0,
    0x2f, 0x60, 0x3, 0xf6, 0x0, 0xbf, 0x88, 0x89,
    0xfb, 0x81, 0xcf, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0x3, 0xf6,
    0x0, 0x0, 0x0, 0x3, 0xf6, 0x0,

    /* U+0035 "5" */
    0x0, 0xcf, 0xff, 0xff, 0xb0, 0x0, 0xfc, 0x99,
    0x99, 0x60, 0x2, 0xf6, 0x0, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x0, 0x7, 0xf0, 0x1, 0x0,
    0x0, 0xa, 0xfa, 0xff, 0xe6, 0x0, 0xd, 0xfb,
    0x67, 0xdf, 0x70, 0x7, 0x70, 0x0, 0xe, 0xf0,
    0x0, 0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x0,
    0x6, 0xf4, 0x1c, 0x70, 0x0, 0x9, 0xf2, 0xe,
    0xd0, 0x0, 0x1f, 0xc0, 0x5, 0xfd, 0x67, 0xef,
    0x20, 0x0, 0x5c, 0xff, 0xb2, 0x0,

    /* U+0036 "6" */
    0x0, 0x19, 0xef, 0xc4, 0x0, 0x0, 0xcf, 0x86,
    0xdf, 0x30, 0x7, 0xf4, 0x0, 0x1e, 0xb0, 0xd,
    0xb0, 0x0, 0x5, 0x60, 0x1f, 0x70, 0x1, 0x0,
    0x0, 0x3f, 0x58, 0xff, 0xe6, 0x0, 0x4f, 0xde,
    0x66, 0xdf, 0x50, 0x5f, 0xf1, 0x0, 0xe, 0xd0,
    0x4f, 0x90, 0x0, 0x9, 0xf1, 0x2f, 0x80, 0x0,
    0x7, 0xf2, 0xf, 0xb0, 0x0, 0x9, 0xf0, 0x9,
    0xf2, 0x0, 0x1f, 0xb0, 0x1, 0xee, 0x77, 0xdf,
    0x20, 0x0, 0x1a, 0xef, 0xb3, 0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xff, 0xf3, 0x19, 0x99, 0x99,
    0x9f, 0xe1, 0x0, 0x0, 0x0, 0x7f, 0x50, 0x0,
    0x0, 0x2, 0xfa, 0x0, 0x0, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0x2f, 0x90, 0x0, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0x0, 0x0, 0xfb, 0x0, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x9, 0xf1,
    0x0, 0x0, 0x0, 0xd, 0xc0, 0x0, 0x0, 0x0,
    0xf, 0x90, 0x0, 0x0, 0x0, 0x3f, 0x60, 0x0,
    0x0, 0x0, 0x4f, 0x50, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x3b, 0xff, 0xa2, 0x0, 0x1, 0xfe, 0x77,
    0xee, 0x10, 0x8, 0xf2, 0x0, 0x4f, 0x70, 0xb,
    0xe0, 0x0, 0xf, 0xa0, 0x9, 0xf1, 0x0, 0x1f,
    0x80, 0x1, 0xeb, 0x22, 0xcf, 0x20, 0x0, 0x6f,
    0xff, 0xf4, 0x0, 0x8, 0xfb, 0x55, 0xcf, 0x40,
    0x1f, 0xc0, 0x0, 0xd, 0xe0, 0x3f, 0x60, 0x0,
    0x7, 0xf2, 0x3f, 0x70, 0x0, 0x7, 0xf2, 0xe,
    0xd0, 0x0, 0xd, 0xe0, 0x5, 0xfd, 0x67, 0xdf,
    0x50, 0x0, 0x4c, 0xff, 0xc4, 0x0,

    /* U+0039 "9" */
    0x0, 0x3c, 0xfe, 0x91, 0x0, 0x3, 0xfe, 0x77,
    0xed, 0x0, 0xd, 0xf2, 0x0, 0x2f, 0x80, 0x1f,
    0x90, 0x0, 0xb, 0xe0, 0x3f, 0x60, 0x0, 0x9,
    0xf1, 0x3f, 0x70, 0x0, 0xa, 0xf2, 0xf, 0xc0,
    0x0, 0xe, 0xf3, 0x8, 0xfa, 0x34, 0xce, 0xf3,
    0x0, 0xaf, 0xff, 0xa7, 0xf1, 0x0, 0x2, 0x42,
    0x8, 0xf0, 0x5, 0x30, 0x0, 0xc, 0xc0, 0xc,
    0xd0, 0x0, 0x5f, 0x50, 0x5, 0xfb, 0x68, 0xfb,
    0x0, 0x0, 0x5d, 0xfd, 0x70, 0x0,

    /* U+003A ":" */
    0x6f, 0x65, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0x66, 0xf6,

    /* U+003B ";" */
    0x6f, 0x65, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0x56, 0xf6, 0x9,
    0x51, 0xd1, 0x24, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xc8, 0x0, 0x0, 0x7, 0xdf, 0xd4, 0x0,
    0x28, 0xff, 0xb4, 0x0, 0xa, 0xff, 0x92, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xe7, 0x10, 0x0, 0x0, 0x3, 0xaf, 0xf9, 0x20,
    0x0, 0x0, 0x2, 0x9f, 0xf6, 0x0, 0x0, 0x0,
    0x1, 0x76,

    /* U+003D "=" */
    0xff, 0xff, 0xff, 0xff, 0x88, 0x88, 0x88, 0x88,
    0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0x88, 0x88,
    0x88, 0x88, 0x84,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x81, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xa3, 0x0, 0x0, 0x0,
    0x18, 0xef, 0xc5, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xe4, 0x0, 0x0, 0x0, 0x2c, 0xf8, 0x0, 0x0,
    0x4b, 0xff, 0x81, 0x0, 0x6d, 0xfe, 0x70, 0x0,
    0xe, 0xfc, 0x50, 0x0, 0x0, 0xb, 0x30, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x4c, 0xff, 0xc4, 0x0, 0x3, 0xfe, 0x77,
    0xef, 0x40, 0xc, 0xe1, 0x0, 0x1e, 0xd0, 0x1f,
    0x90, 0x0, 0x9, 0xf1, 0x0, 0x10, 0x0, 0xa,
    0xf0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x4, 0xfd, 0x10, 0x0, 0x0, 0x1f, 0xd1, 0x0,
    0x0, 0x0, 0x9f, 0x20, 0x0, 0x0, 0x0, 0xcc,
    0x0, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdc, 0x0,
    0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xec, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x5e, 0xfa, 0x75, 0x7a, 0xff,
    0x50, 0x0, 0x0, 0x6, 0xfa, 0x10, 0x0, 0x0,
    0x9, 0xf6, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x20, 0x0, 0xcc, 0x0, 0x19,
    0xee, 0x63, 0xf4, 0xc, 0x90, 0x3, 0xf2, 0x1,
    0xde, 0x77, 0xfc, 0xf1, 0x6, 0xf0, 0x8, 0xc0,
    0xa, 0xf2, 0x0, 0x4f, 0xe0, 0x3, 0xf1, 0xc,
    0x80, 0x2f, 0x80, 0x0, 0xf, 0xb0, 0x2, 0xf2,
    0xe, 0x60, 0x7f, 0x20, 0x0, 0xf, 0x80, 0x4,
    0xf1, 0xf, 0x40, 0xaf, 0x0, 0x0, 0x3f, 0x50,
    0x9, 0xd0, 0xf, 0x60, 0x9f, 0x0, 0x0, 0x9f,
    0x10, 0x1f, 0x60, 0xd, 0x80, 0x6f, 0x60, 0x5,
    0xff, 0x1, 0xcd, 0x0, 0x8, 0xd0, 0xe, 0xf9,
    0xbf, 0xef, 0x9e, 0xe2, 0x0, 0x3, 0xf5, 0x2,
    0xcf, 0xc3, 0x5e, 0xe9, 0x10, 0x31, 0x0, 0xaf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0xd, 0xf6, 0x0, 0x0, 0x0, 0x2, 0xbf, 0x50,
    0x0, 0x0, 0x9f, 0xfa, 0x76, 0x68, 0xcf, 0xe4,
    0x0, 0x0, 0x0, 0x2, 0x8c, 0xef, 0xfe, 0xa5,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x5a, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x15,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0xfd,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0xbf, 0x30,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x1f, 0xd2, 0x22, 0x3f, 0xe0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0xcf,
    0x55, 0x55, 0x57, 0xfb, 0x0, 0x2, 0xfb, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x8, 0xf5, 0x0, 0x0,
    0x0, 0x8f, 0x70, 0xd, 0xf0, 0x0, 0x0, 0x0,
    0x1f, 0xd0,

    /* U+0042 "B" */
    0xbf, 0xff, 0xff, 0xd8, 0x0, 0xb, 0xf9, 0x99,
    0xad, 0xfb, 0x0, 0xbf, 0x0, 0x0, 0xa, 0xf3,
    0xb, 0xf0, 0x0, 0x0, 0x4f, 0x60, 0xbf, 0x0,
    0x0, 0x5, 0xf4, 0xb, 0xf0, 0x0, 0x3, 0xdd,
    0x0, 0xbf, 0xff, 0xff, 0xfe, 0x10, 0xb, 0xf9,
    0x99, 0x9b, 0xfd, 0x20, 0xbf, 0x0, 0x0, 0x4,
    0xfb, 0xb, 0xf0, 0x0, 0x0, 0xc, 0xf0, 0xbf,
    0x0, 0x0, 0x0, 0xcf, 0xb, 0xf0, 0x0, 0x0,
    0x4f, 0xc0, 0xbf, 0x99, 0x99, 0xbf, 0xf3, 0xb,
    0xff, 0xff, 0xfe, 0xb3, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x6c, 0xff, 0xc6, 0x0, 0x0, 0x2,
    0xdf, 0xc9, 0x9e, 0xfa, 0x0, 0x0, 0xdf, 0x50,
    0x0, 0xa, 0xf6, 0x0, 0x6f, 0x70, 0x0, 0x0,
    0xe, 0xd0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x31,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x8,
    0xb1, 0x6, 0xf7, 0x0, 0x0, 0x0, 0xee, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0xbf, 0x70, 0x0, 0x2e,
    0xfc, 0x9a, 0xef, 0xa0, 0x0, 0x0, 0x18, 0xdf,
    0xfc, 0x60, 0x0,

    /* U+0044 "D" */
    0x9f, 0xff, 0xff, 0xea, 0x20, 0x0, 0x9f, 0xa9,
    0x9a, 0xcf, 0xf4, 0x0, 0x9f, 0x10, 0x0, 0x3,
    0xfe, 0x0, 0x9f, 0x10, 0x0, 0x0, 0x6f, 0x70,
    0x9f, 0x10, 0x0, 0x0, 0xf, 0xc0, 0x9f, 0x10,
    0x0, 0x0, 0xc, 0xf0, 0x9f, 0x10, 0x0, 0x0,
    0xb, 0xf0, 0x9f, 0x10, 0x0, 0x0, 0xc, 0xf0,
    0x9f, 0x10, 0x0, 0x0, 0xd, 0xe0, 0x9f, 0x10,
    0x0, 0x0, 0xf, 0xb0, 0x9f, 0x10, 0x0, 0x0,
    0x6f, 0x70, 0x9f, 0x10, 0x0, 0x3, 0xfe, 0x0,
    0x9f, 0xa9, 0x9a, 0xcf, 0xf4, 0x0, 0x9f, 0xff,
    0xff, 0xea, 0x20, 0x0,

    /* U+0045 "E" */
    0x9f, 0xff, 0xff, 0xff, 0xfb, 0x9, 0xfa, 0x99,
    0x99, 0x99, 0x60, 0x9f, 0x20, 0x0, 0x0, 0x0,
    0x9, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x20,
    0x0, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xa9, 0x99, 0x99, 0x92, 0x9, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x9f, 0x20, 0x0, 0x0,
    0x0, 0x9, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x20, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xa9, 0x99, 0x99, 0x99, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xf0,

    /* U+0046 "F" */
    0x8f, 0xff, 0xff, 0xff, 0xf2, 0x8f, 0xa9, 0x99,
    0x99, 0x91, 0x8f, 0x30, 0x0, 0x0, 0x0, 0x8f,
    0x30, 0x0, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0,
    0x0, 0x8f, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xa9,
    0x99, 0x99, 0x20, 0x8f, 0xff, 0xff, 0xff, 0x30,
    0x8f, 0x30, 0x0, 0x0, 0x0, 0x8f, 0x30, 0x0,
    0x0, 0x0, 0x8f, 0x30, 0x0, 0x0, 0x0, 0x8f,
    0x30, 0x0, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0,
    0x0, 0x8f, 0x30, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x5b, 0xef, 0xea, 0x30, 0x0, 0x1,
    0xbf, 0xe9, 0x8b, 0xff, 0x50, 0x0, 0xbf, 0x70,
    0x0, 0x2, 0xdf, 0x10, 0x4f, 0xa0, 0x0, 0x0,
    0x3, 0xf7, 0xa, 0xf2, 0x0, 0x0, 0x0, 0x6,
    0x10, 0xed, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0x9f, 0xff, 0xfe, 0xe, 0xd0, 0x0, 0x5,
    0x99, 0x9e, 0xe0, 0xaf, 0x20, 0x0, 0x0, 0x0,
    0xce, 0x4, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xe0,
    0xb, 0xf9, 0x0, 0x0, 0x8, 0xfd, 0x0, 0xb,
    0xff, 0xba, 0xbf, 0xfd, 0x30, 0x0, 0x4, 0xbe,
    0xfe, 0xb5, 0x0,

    /* U+0048 "H" */
    0x9f, 0x20, 0x0, 0x0, 0x2f, 0x89, 0xf2, 0x0,
    0x0, 0x2, 0xf8, 0x9f, 0x20, 0x0, 0x0, 0x2f,
    0x89, 0xf2, 0x0, 0x0, 0x2, 0xf8, 0x9f, 0x20,
    0x0, 0x0, 0x2f, 0x89, 0xf2, 0x0, 0x0, 0x2,
    0xf8, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x89, 0xfa,
    0x99, 0x99, 0x9a, 0xf8, 0x9f, 0x20, 0x0, 0x0,
    0x2f, 0x89, 0xf2, 0x0, 0x0, 0x2, 0xf8, 0x9f,
    0x20, 0x0, 0x0, 0x2f, 0x89, 0xf2, 0x0, 0x0,
    0x2, 0xf8, 0x9f, 0x20, 0x0, 0x0, 0x2f, 0x89,
    0xf2, 0x0, 0x0, 0x2, 0xf8,

    /* U+0049 "I" */
    0x5f, 0x65, 0xf6, 0x5f, 0x65, 0xf6, 0x5f, 0x65,
    0xf6, 0x5f, 0x65, 0xf6, 0x5f, 0x65, 0xf6, 0x5f,
    0x65, 0xf6, 0x5f, 0x65, 0xf6,

    /* U+004A "J" */
    0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0x1, 0xf9, 0x14, 0x0, 0x1, 0xf9,
    0x6f, 0x20, 0x2, 0xf8, 0x4f, 0x70, 0x7, 0xf5,
    0xd, 0xfb, 0xbf, 0xe0, 0x2, 0xbf, 0xfb, 0x20,

    /* U+004B "K" */
    0xbf, 0x0, 0x0, 0x2, 0xef, 0x4b, 0xf0, 0x0,
    0x1, 0xdf, 0x50, 0xbf, 0x0, 0x1, 0xdf, 0x50,
    0xb, 0xf0, 0x0, 0xcf, 0x60, 0x0, 0xbf, 0x0,
    0xbf, 0x70, 0x0, 0xb, 0xf0, 0xaf, 0x90, 0x0,
    0x0, 0xbf, 0x9f, 0xfd, 0x0, 0x0, 0xb, 0xff,
    0x97, 0xf9, 0x0, 0x0, 0xbf, 0xa0, 0xd, 0xf4,
    0x0, 0xb, 0xf0, 0x0, 0x3f, 0xd0, 0x0, 0xbf,
    0x0, 0x0, 0x8f, 0x90, 0xb, 0xf0, 0x0, 0x0,
    0xdf, 0x40, 0xbf, 0x0, 0x0, 0x4, 0xfe, 0xb,
    0xf0, 0x0, 0x0, 0x9, 0xfa,

    /* U+004C "L" */
    0xbf, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x0,
    0x0, 0xbf, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0x0, 0x0, 0xbf, 0x0, 0x0, 0x0, 0xb, 0xf0,
    0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0, 0xb,
    0xf0, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0,
    0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0xbf, 0x99,
    0x99, 0x99, 0x3b, 0xff, 0xff, 0xff, 0xf6,

    /* U+004D "M" */
    0xaf, 0xf1, 0x0, 0x0, 0x0, 0xdf, 0xaa, 0xff,
    0x50, 0x0, 0x0, 0x2f, 0xfa, 0xaf, 0xea, 0x0,
    0x0, 0x7, 0xef, 0xaa, 0xf9, 0xf0, 0x0, 0x0,
    0xc9, 0xfa, 0xaf, 0x4f, 0x40, 0x0, 0x1f, 0x4f,
    0xaa, 0xf0, 0xe9, 0x0, 0x6, 0xe0, 0xfa, 0xaf,
    0xa, 0xe0, 0x0, 0xba, 0xf, 0xaa, 0xf0, 0x5f,
    0x30, 0xf, 0x50, 0xfa, 0xaf, 0x0, 0xf8, 0x5,
    0xf0, 0xf, 0xaa, 0xf0, 0xb, 0xd0, 0xaa, 0x0,
    0xfa, 0xaf, 0x0, 0x6f, 0x2f, 0x50, 0xf, 0xaa,
    0xf0, 0x1, 0xfb, 0xf1, 0x0, 0xfa, 0xaf, 0x0,
    0xc, 0xfb, 0x0, 0xf, 0xaa, 0xf0, 0x0, 0x7f,
    0x60, 0x0, 0xfa,

    /* U+004E "N" */
    0xaf, 0x60, 0x0, 0x0, 0x1f, 0x8a, 0xff, 0x10,
    0x0, 0x1, 0xf8, 0xaf, 0xfa, 0x0, 0x0, 0x1f,
    0x8a, 0xfb, 0xf4, 0x0, 0x1, 0xf8, 0xaf, 0x1f,
    0xd0, 0x0, 0x1f, 0x8a, 0xf0, 0x7f, 0x80, 0x1,
    0xf8, 0xaf, 0x0, 0xdf, 0x20, 0x1f, 0x8a, 0xf0,
    0x3, 0xfb, 0x1, 0xf8, 0xaf, 0x0, 0x9, 0xf5,
    0x1f, 0x8a, 0xf0, 0x0, 0x1e, 0xe2, 0xf8, 0xaf,
    0x0, 0x0, 0x6f, 0xbf, 0x8a, 0xf0, 0x0, 0x0,
    0xcf, 0xf8, 0xaf, 0x0, 0x0, 0x2, 0xff, 0x8a,
    0xf0, 0x0, 0x0, 0x8, 0xf8,

    /* U+004F "O" */
    0x0, 0x0, 0x7c, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x1d, 0xfd, 0x99, 0xdf, 0xd2, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x6, 0xfd, 0x0, 0x6, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x70, 0xc, 0xf0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0xb, 0xf0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x9,
    0xf2, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x9, 0xf2,
    0xf, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xf0, 0xb,
    0xf0, 0x0, 0x0, 0x0, 0xe, 0xc0, 0x6, 0xf7,
    0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0xcf, 0x60,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0x1c, 0xfd, 0x99,
    0xcf, 0xd2, 0x0, 0x0, 0x0, 0x6c, 0xef, 0xc7,
    0x0, 0x0,

    /* U+0050 "P" */
    0x9f, 0xff, 0xff, 0xfc, 0x50, 0x9, 0xfa, 0x99,
    0x9a, 0xff, 0x60, 0x9f, 0x10, 0x0, 0x2, 0xfe,
    0x9, 0xf1, 0x0, 0x0, 0xa, 0xf2, 0x9f, 0x10,
    0x0, 0x0, 0x9f, 0x39, 0xf1, 0x0, 0x0, 0xc,
    0xf0, 0x9f, 0x10, 0x0, 0x29, 0xfa, 0x9, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x9f, 0xa9, 0x99, 0x84,
    0x0, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x10, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x7c, 0xfe, 0xc6, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x99, 0xdf, 0xc1, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0x7, 0xfc, 0x0, 0x7, 0xf5, 0x0,
    0x0, 0x0, 0x9f, 0x50, 0xd, 0xe0, 0x0, 0x0,
    0x0, 0x1f, 0xb0, 0x1f, 0xa0, 0x0, 0x0, 0x0,
    0xc, 0xf0, 0x2f, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xf0, 0x2f, 0x80, 0x0, 0x0, 0x0, 0xb, 0xf0,
    0x1f, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xf0, 0xd,
    0xe0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x7, 0xf6,
    0x0, 0xa, 0x80, 0x8f, 0x50, 0x0, 0xdf, 0x50,
    0x8, 0xee, 0xfc, 0x0, 0x0, 0x2d, 0xfc, 0x99,
    0xdf, 0xf8, 0x0, 0x0, 0x0, 0x7c, 0xfe, 0xc6,
    0x7f, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x90,

    /* U+0052 "R" */
    0x9f, 0xff, 0xff, 0xfe, 0xa2, 0x0, 0x9f, 0x98,
    0x88, 0x8c, 0xfe, 0x10, 0x9f, 0x20, 0x0, 0x0,
    0x8f, 0x70, 0x9f, 0x20, 0x0, 0x0, 0x1f, 0xa0,
    0x9f, 0x20, 0x0, 0x0, 0x2f, 0xa0, 0x9f, 0x20,
    0x0, 0x0, 0x9f, 0x60, 0x9f, 0x98, 0x88, 0x9d,
    0xfb, 0x0, 0x9f, 0xff, 0xff, 0xfc, 0x60, 0x0,
    0x9f, 0x20, 0x4, 0xed, 0x20, 0x0, 0x9f, 0x20,
    0x0, 0x3f, 0xd1, 0x0, 0x9f, 0x20, 0x0, 0x8,
    0xfa, 0x0, 0x9f, 0x20, 0x0, 0x0, 0xdf, 0x30,
    0x9f, 0x20, 0x0, 0x0, 0x4f, 0xd0, 0x9f, 0x20,
    0x0, 0x0, 0xa, 0xf7,

    /* U+0053 "S" */
    0x0, 0x8, 0xdf, 0xfc, 0x60, 0x0, 0x0, 0xdf,
    0xc9, 0xae, 0xfa, 0x0, 0x7, 0xf6, 0x0, 0x0,
    0xaf, 0x40, 0xa, 0xf0, 0x0, 0x0, 0x2f, 0x90,
    0x9, 0xf4, 0x0, 0x0, 0x2, 0x0, 0x2, 0xff,
    0xa5, 0x10, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xfd,
    0x70, 0x0, 0x0, 0x0, 0x38, 0xcf, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x1, 0x8f, 0xb0, 0x1a, 0x50,
    0x0, 0x0, 0xb, 0xf0, 0xf, 0xb0, 0x0, 0x0,
    0xa, 0xf0, 0xa, 0xf8, 0x0, 0x0, 0x4f, 0xa0,
    0x1, 0xdf, 0xea, 0xac, 0xfe, 0x10, 0x0, 0x7,
    0xce, 0xfd, 0x81, 0x0,

    /* U+0054 "T" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x99, 0x9b,
    0xfb, 0x99, 0x96, 0x0, 0x0, 0x5f, 0x60, 0x0,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x60, 0x0, 0x0, 0x0, 0x5, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x60, 0x0, 0x0, 0x0,
    0x5, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x60,
    0x0, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x60, 0x0, 0x0, 0x0, 0x5, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x60, 0x0, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0x0,

    /* U+0055 "U" */
    0x9f, 0x20, 0x0, 0x0, 0x2f, 0x89, 0xf2, 0x0,
    0x0, 0x2, 0xf8, 0x9f, 0x20, 0x0, 0x0, 0x2f,
    0x89, 0xf2, 0x0, 0x0, 0x2, 0xf8, 0x9f, 0x20,
    0x0, 0x0, 0x2f, 0x89, 0xf2, 0x0, 0x0, 0x2,
    0xf8, 0x9f, 0x20, 0x0, 0x0, 0x2f, 0x89, 0xf2,
    0x0, 0x0, 0x2, 0xf8, 0x8f, 0x20, 0x0, 0x0,
    0x2f, 0x87, 0xf3, 0x0, 0x0, 0x4, 0xf7, 0x5f,
    0x70, 0x0, 0x0, 0x7f, 0x41, 0xfe, 0x20, 0x0,
    0x2e, 0xe0, 0x6, 0xff, 0xba, 0xbf, 0xf5, 0x0,
    0x4, 0xbe, 0xfe, 0xa3, 0x0,

    /* U+0056 "V" */
    0xbf, 0x10, 0x0, 0x0, 0x1, 0xfa, 0x6f, 0x60,
    0x0, 0x0, 0x6, 0xf5, 0xf, 0xb0, 0x0, 0x0,
    0xb, 0xe0, 0xa, 0xf1, 0x0, 0x0, 0x1f, 0x90,
    0x5, 0xf6, 0x0, 0x0, 0x6f, 0x30, 0x0, 0xeb,
    0x0, 0x0, 0xce, 0x0, 0x0, 0x9f, 0x10, 0x1,
    0xf8, 0x0, 0x0, 0x3f, 0x60, 0x7, 0xf2, 0x0,
    0x0, 0xe, 0xb0, 0xc, 0xc0, 0x0, 0x0, 0x8,
    0xf1, 0x2f, 0x70, 0x0, 0x0, 0x2, 0xf5, 0x7f,
    0x10, 0x0, 0x0, 0x0, 0xda, 0xcb, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0,

    /* U+0057 "W" */
    0xaf, 0x10, 0x0, 0xa, 0xfb, 0x0, 0x0, 0xf,
    0xa6, 0xf4, 0x0, 0x0, 0xef, 0xf0, 0x0, 0x4,
    0xf6, 0x2f, 0x80, 0x0, 0x2f, 0x9f, 0x30, 0x0,
    0x7f, 0x20, 0xeb, 0x0, 0x6, 0xf2, 0xf7, 0x0,
    0xb, 0xe0, 0xa, 0xe0, 0x0, 0xac, 0xd, 0xb0,
    0x0, 0xea, 0x0, 0x7f, 0x20, 0xe, 0x80, 0x8f,
    0x0, 0x2f, 0x60, 0x3, 0xf5, 0x2, 0xf4, 0x4,
    0xf4, 0x6, 0xf2, 0x0, 0xf, 0x90, 0x7f, 0x0,
    0xf, 0x80, 0x9e, 0x0, 0x0, 0xbc, 0xb, 0xc0,
    0x0, 0xcb, 0xd, 0xa0, 0x0, 0x7, 0xf0, 0xf8,
    0x0, 0x8, 0xf1, 0xf6, 0x0, 0x0, 0x3f, 0x5f,
    0x40, 0x0, 0x4f, 0x6f, 0x20, 0x0, 0x0, 0xfc,
    0xf0, 0x0, 0x0, 0xfc, 0xe0, 0x0, 0x0, 0xb,
    0xfc, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0,
    0x7f, 0x80, 0x0, 0x0, 0x8f, 0x60, 0x0,

    /* U+0058 "X" */
    0x1e, 0xe1, 0x0, 0x0, 0xa, 0xf3, 0x5, 0xfa,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0xbf, 0x50, 0x1,
    0xfc, 0x0, 0x0, 0x1e, 0xe1, 0xc, 0xf2, 0x0,
    0x0, 0x6, 0xf9, 0x6f, 0x70, 0x0, 0x0, 0x0,
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x0, 0x0,
    0x0, 0x2, 0xfb, 0xcf, 0x40, 0x0, 0x0, 0xd,
    0xf2, 0x2f, 0xe0, 0x0, 0x0, 0x8f, 0x70, 0x7,
    0xf9, 0x0, 0x3, 0xfc, 0x0, 0x0, 0xdf, 0x30,
    0xd, 0xf2, 0x0, 0x0, 0x3f, 0xd0, 0x9f, 0x70,
    0x0, 0x0, 0x8, 0xf8,

    /* U+0059 "Y" */
    0xaf, 0x60, 0x0, 0x0, 0x6, 0xf8, 0x1e, 0xe1,
    0x0, 0x0, 0x1f, 0xd0, 0x6, 0xfa, 0x0, 0x0,
    0xaf, 0x40, 0x0, 0xcf, 0x30, 0x3, 0xfa, 0x0,
    0x0, 0x3f, 0xc0, 0xd, 0xe1, 0x0, 0x0, 0x9,
    0xf6, 0x6f, 0x50, 0x0, 0x0, 0x0, 0xee, 0xeb,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xb0, 0x0, 0x0,

    /* U+005A "Z" */
    0xe, 0xff, 0xff, 0xff, 0xff, 0x50, 0x89, 0x99,
    0x99, 0x9f, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x0, 0x0, 0x0, 0x3, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0xdf, 0x30, 0x0, 0x0, 0x0, 0xaf, 0x70,
    0x0, 0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0,
    0x2f, 0xe1, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0x0, 0x1, 0xee, 0x10, 0x0,
    0x0, 0x0, 0x9f, 0xc9, 0x99, 0x99, 0x99, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+005B "[" */
    0xcf, 0xfb, 0xcd, 0x64, 0xcc, 0x0, 0xcc, 0x0,
    0xcc, 0x0, 0xcc, 0x0, 0xcc, 0x0, 0xcc, 0x0,
    0xcc, 0x0, 0xcc, 0x0, 0xcc, 0x0, 0xcc, 0x0,
    0xcc, 0x0, 0xcc, 0x0, 0xcc, 0x0, 0xcc, 0x0,
    0xcd, 0x64, 0xcf, 0xfb,

    /* U+005C "\\" */
    0xd6, 0x0, 0x9, 0xa0, 0x0, 0x5e, 0x0, 0x1,
    0xf3, 0x0, 0xc, 0x70, 0x0, 0x8b, 0x0, 0x4,
    0xf0, 0x0, 0xf, 0x40, 0x0, 0xb8, 0x0, 0x7,
    0xc0, 0x0, 0x3f, 0x10, 0x0, 0xe5, 0x0, 0xa,
    0x90, 0x0, 0x6d,

    /* U+005D "]" */
    0xaf, 0xfd, 0x36, 0xdd, 0x0, 0xcd, 0x0, 0xcd,
    0x0, 0xcd, 0x0, 0xcd, 0x0, 0xcd, 0x0, 0xcd,
    0x0, 0xcd, 0x0, 0xcd, 0x0, 0xcd, 0x0, 0xcd,
    0x0, 0xcd, 0x0, 0xcd, 0x0, 0xcd, 0x0, 0xcd,
    0x36, 0xdd, 0xaf, 0xfd,

    /* U+005E "^" */
    0x0, 0x4, 0x70, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x4f, 0xdb, 0x0, 0x0, 0xac, 0x5f, 0x10,
    0x1, 0xf6, 0xf, 0x80, 0x7, 0xf1, 0x9, 0xe0,
    0xe, 0xa0, 0x3, 0xf5, 0x5f, 0x40, 0x0, 0xdc,

    /* U+005F "_" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x3, 0x33, 0x33, 0x33,
    0x33, 0x30,

    /* U+0060 "`" */
    0x8, 0x80, 0x0, 0x6f, 0x60, 0x0, 0x9d, 0x0,

    /* U+0061 "a" */
    0x0, 0x6c, 0xff, 0xd7, 0x0, 0x7, 0xfb, 0x67,
    0xcf, 0x50, 0xe, 0xb0, 0x0, 0xf, 0xb0, 0x0,
    0x10, 0x0, 0x1e, 0xb0, 0x0, 0x49, 0xce, 0xff,
    0xc0, 0x9, 0xfd, 0x96, 0x2d, 0xc0, 0x3f, 0xa0,
    0x0, 0xf, 0xc0, 0x4f, 0x60, 0x0, 0x5f, 0xc0,
    0x1e, 0xe6, 0x59, 0xff, 0xd0, 0x3, 0xcf, 0xec,
    0x58, 0xf1,

    /* U+0062 "b" */
    0xdc, 0x0, 0x0, 0x0, 0xd, 0xc0, 0x0, 0x0,
    0x0, 0xdc, 0x0, 0x0, 0x0, 0xd, 0xc0, 0x0,
    0x0, 0x0, 0xdc, 0x6d, 0xfc, 0x40, 0xd, 0xff,
    0x87, 0xdf, 0x40, 0xdf, 0x50, 0x1, 0xed, 0xd,
    0xd0, 0x0, 0x8, 0xf1, 0xda, 0x0, 0x0, 0x6f,
    0x3d, 0xa0, 0x0, 0x6, 0xf3, 0xdc, 0x0, 0x0,
    0x8f, 0x1d, 0xf1, 0x0, 0xe, 0xc0, 0xdf, 0xc4,
    0x3b, 0xf3, 0xd, 0xa8, 0xef, 0xc3, 0x0,

    /* U+0063 "c" */
    0x0, 0x3b, 0xfe, 0xb2, 0x0, 0x4f, 0xd7, 0x7e,
    0xe1, 0xd, 0xd0, 0x0, 0x3f, 0x72, 0xf8, 0x0,
    0x0, 0x31, 0x4f, 0x50, 0x0, 0x0, 0x4, 0xf5,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x9, 0x70,
    0xdd, 0x0, 0x2, 0xf8, 0x4, 0xfd, 0x77, 0xee,
    0x10, 0x4, 0xcf, 0xea, 0x10,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0, 0x0,
    0xdb, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0,
    0x0, 0xdb, 0x0, 0x5d, 0xfd, 0x4d, 0xb0, 0x6f,
    0xc7, 0x9f, 0xfb, 0xe, 0xc0, 0x0, 0x5f, 0xb3,
    0xf6, 0x0, 0x0, 0xeb, 0x5f, 0x40, 0x0, 0xc,
    0xb5, 0xf4, 0x0, 0x0, 0xcb, 0x3f, 0x70, 0x0,
    0xe, 0xb0, 0xed, 0x0, 0x5, 0xfb, 0x4, 0xfd,
    0x68, 0xfe, 0xb0, 0x4, 0xcf, 0xd5, 0xcb,

    /* U+0065 "e" */
    0x0, 0x3b, 0xff, 0xb3, 0x0, 0x4, 0xfd, 0x77,
    0xdf, 0x40, 0xd, 0xd0, 0x0, 0xd, 0xd0, 0x2f,
    0x81, 0x11, 0x18, 0xf1, 0x4f, 0xff, 0xff, 0xff,
    0xf3, 0x5f, 0x84, 0x44, 0x44, 0x41, 0x3f, 0x80,
    0x0, 0x3, 0x40, 0xe, 0xe1, 0x0, 0xd, 0xe0,
    0x4, 0xfe, 0x76, 0xcf, 0x50, 0x0, 0x3b, 0xff,
    0xc4, 0x0,

    /* U+0066 "f" */
    0x0, 0x7e, 0xf7, 0x3, 0xfd, 0x84, 0x6, 0xf3,
    0x0, 0x7, 0xf2, 0x0, 0xdf, 0xff, 0xf0, 0x39,
    0xf6, 0x40, 0x7, 0xf2, 0x0, 0x7, 0xf2, 0x0,
    0x7, 0xf2, 0x0, 0x7, 0xf2, 0x0, 0x7, 0xf2,
    0x0, 0x7, 0xf2, 0x0, 0x7, 0xf2, 0x0, 0x7,
    0xf2, 0x0,

    /* U+0067 "g" */
    0x0, 0x5c, 0xfd, 0x6a, 0xd0, 0x5f, 0xc7, 0x9f,
    0xed, 0xe, 0xd0, 0x0, 0x5f, 0xd3, 0xf6, 0x0,
    0x0, 0xed, 0x5f, 0x40, 0x0, 0xb, 0xd6, 0xf3,
    0x0, 0x0, 0xbd, 0x4f, 0x60, 0x0, 0xd, 0xd0,
    0xec, 0x0, 0x4, 0xfd, 0x5, 0xfc, 0x78, 0xff,
    0xc0, 0x5, 0xdf, 0xd5, 0xcc, 0x1, 0x0, 0x0,
    0xe, 0xb0, 0xf9, 0x0, 0x3, 0xf7, 0xb, 0xf9,
    0x68, 0xee, 0x10, 0x8, 0xdf, 0xe9, 0x10,

    /* U+0068 "h" */
    0xdc, 0x0, 0x0, 0x0, 0xdc, 0x0, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0x0, 0xdc, 0x0, 0x0, 0x0,
    0xdc, 0x5d, 0xfd, 0x60, 0xdf, 0xf9, 0x7d, 0xf5,
    0xdf, 0x40, 0x1, 0xfa, 0xde, 0x0, 0x0, 0xdc,
    0xdc, 0x0, 0x0, 0xcc, 0xdc, 0x0, 0x0, 0xcc,
    0xdc, 0x0, 0x0, 0xcc, 0xdc, 0x0, 0x0, 0xcc,
    0xdc, 0x0, 0x0, 0xcc, 0xdc, 0x0, 0x0, 0xcc,

    /* U+0069 "i" */
    0xcc, 0xbb, 0x0, 0x0, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,

    /* U+006A "j" */
    0x0, 0xdc, 0x0, 0xcb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdc, 0x0, 0xdc, 0x0, 0xdc, 0x0, 0xdc,
    0x0, 0xdc, 0x0, 0xdc, 0x0, 0xdc, 0x0, 0xdc,
    0x0, 0xdc, 0x0, 0xdc, 0x0, 0xdc, 0x0, 0xeb,
    0x59, 0xf8, 0xaf, 0xb1,

    /* U+006B "k" */
    0xcc, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0,
    0xcc, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0,
    0xcc, 0x0, 0x1d, 0xe2, 0xcc, 0x0, 0xce, 0x20,
    0xcc, 0xc, 0xf3, 0x0, 0xcc, 0xaf, 0x40, 0x0,
    0xcf, 0xff, 0x70, 0x0, 0xcf, 0x5b, 0xf2, 0x0,
    0xcc, 0x2, 0xfb, 0x0, 0xcc, 0x0, 0x8f, 0x50,
    0xcc, 0x0, 0xe, 0xe1, 0xcc, 0x0, 0x5, 0xf9,

    /* U+006C "l" */
    0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb,
    0xdb, 0xdb, 0xdb, 0xdb, 0xdb, 0xdb,

    /* U+006D "m" */
    0xd9, 0x6d, 0xfd, 0x41, 0xaf, 0xe9, 0xd, 0xed,
    0x54, 0xdf, 0xd8, 0x48, 0xf7, 0xdf, 0x20, 0x6,
    0xfa, 0x0, 0xd, 0xbd, 0xd0, 0x0, 0x4f, 0x60,
    0x0, 0xbd, 0xdc, 0x0, 0x4, 0xf5, 0x0, 0xb,
    0xdd, 0xc0, 0x0, 0x4f, 0x50, 0x0, 0xbd, 0xdc,
    0x0, 0x4, 0xf5, 0x0, 0xb, 0xdd, 0xc0, 0x0,
    0x4f, 0x50, 0x0, 0xbd, 0xdc, 0x0, 0x4, 0xf5,
    0x0, 0xb, 0xdd, 0xc0, 0x0, 0x4f, 0x50, 0x0,
    0xbd,

    /* U+006E "n" */
    0xd9, 0x6d, 0xfd, 0x60, 0xde, 0xd6, 0x4b, 0xf5,
    0xdf, 0x20, 0x0, 0xfa, 0xdd, 0x0, 0x0, 0xdc,
    0xdc, 0x0, 0x0, 0xdc, 0xdc, 0x0, 0x0, 0xdc,
    0xdc, 0x0, 0x0, 0xdc, 0xdc, 0x0, 0x0, 0xdc,
    0xdc, 0x0, 0x0, 0xdc, 0xdc, 0x0, 0x0, 0xdc,

    /* U+006F "o" */
    0x0, 0x4b, 0xff, 0xb3, 0x0, 0x5, 0xfd, 0x77,
    0xef, 0x40, 0xe, 0xd0, 0x0, 0x1e, 0xd0, 0x3f,
    0x60, 0x0, 0x8, 0xf3, 0x5f, 0x40, 0x0, 0x5,
    0xf5, 0x6f, 0x40, 0x0, 0x5, 0xf4, 0x4f, 0x60,
    0x0, 0x8, 0xf2, 0xe, 0xd0, 0x0, 0x1e, 0xe0,
    0x5, 0xfd, 0x77, 0xdf, 0x40, 0x0, 0x4c, 0xff,
    0xb3, 0x0,

    /* U+0070 "p" */
    0xda, 0x7e, 0xfc, 0x40, 0xd, 0xfe, 0x42, 0xaf,
    0x40, 0xdf, 0x30, 0x0, 0xdd, 0xd, 0xc0, 0x0,
    0x7, 0xf2, 0xda, 0x0, 0x0, 0x5f, 0x4d, 0xa0,
    0x0, 0x5, 0xf3, 0xdd, 0x0, 0x0, 0x8f, 0x1d,
    0xf4, 0x0, 0x1e, 0xc0, 0xdf, 0xf8, 0x7d, 0xf3,
    0xd, 0xc6, 0xef, 0xb3, 0x0, 0xdc, 0x0, 0x0,
    0x0, 0xd, 0xc0, 0x0, 0x0, 0x0, 0xdc, 0x0,
    0x0, 0x0, 0xd, 0xc0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x5c, 0xfd, 0x5b, 0xb0, 0x5f, 0xc6, 0x8f,
    0xeb, 0xe, 0xd0, 0x0, 0x6f, 0xb3, 0xf7, 0x0,
    0x0, 0xeb, 0x5f, 0x50, 0x0, 0xc, 0xb5, 0xf5,
    0x0, 0x0, 0xcb, 0x2f, 0x70, 0x0, 0xe, 0xb0,
    0xde, 0x0, 0x5, 0xfb, 0x4, 0xfd, 0x78, 0xff,
    0xb0, 0x4, 0xcf, 0xd5, 0xdb, 0x0, 0x0, 0x0,
    0xd, 0xb0, 0x0, 0x0, 0x0, 0xdb, 0x0, 0x0,
    0x0, 0xd, 0xb0, 0x0, 0x0, 0x0, 0xdb,

    /* U+0072 "r" */
    0xd9, 0x9f, 0xe1, 0xde, 0xe8, 0xa0, 0xdf, 0x20,
    0x0, 0xdd, 0x0, 0x0, 0xdc, 0x0, 0x0, 0xdc,
    0x0, 0x0, 0xdc, 0x0, 0x0, 0xdc, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0xdc, 0x0, 0x0,

    /* U+0073 "s" */
    0x1, 0xae, 0xfd, 0x70, 0x0, 0xce, 0x76, 0xaf,
    0x80, 0x2f, 0x60, 0x0, 0xaa, 0x1, 0xfd, 0x40,
    0x0, 0x0, 0x6, 0xff, 0xfb, 0x60, 0x0, 0x1,
    0x6b, 0xff, 0xc0, 0x3, 0x0, 0x0, 0x9f, 0x34,
    0xf6, 0x0, 0x6, 0xf3, 0xc, 0xf8, 0x68, 0xfc,
    0x0, 0x19, 0xef, 0xe9, 0x10,

    /* U+0074 "t" */
    0x0, 0x10, 0x0, 0x4f, 0x0, 0x8, 0xf0, 0x0,
    0x8f, 0x0, 0xbf, 0xff, 0xa3, 0xaf, 0x53, 0x8,
    0xf0, 0x0, 0x8f, 0x0, 0x8, 0xf0, 0x0, 0x8f,
    0x0, 0x8, 0xf0, 0x0, 0x8f, 0x10, 0x6, 0xfb,
    0x60, 0x1c, 0xfb,

    /* U+0075 "u" */
    0xdb, 0x0, 0x0, 0xdb, 0xdb, 0x0, 0x0, 0xdb,
    0xdb, 0x0, 0x0, 0xdb, 0xdb, 0x0, 0x0, 0xdb,
    0xdb, 0x0, 0x0, 0xdb, 0xdb, 0x0, 0x0, 0xeb,
    0xdc, 0x0, 0x0, 0xfb, 0xbe, 0x0, 0x5, 0xfb,
    0x6f, 0xc7, 0x9f, 0xdb, 0x7, 0xef, 0xc3, 0xbb,

    /* U+0076 "v" */
    0x9f, 0x10, 0x0, 0xf, 0x93, 0xf6, 0x0, 0x5,
    0xf4, 0xe, 0xb0, 0x0, 0xbe, 0x0, 0x8f, 0x10,
    0xf, 0x80, 0x2, 0xf6, 0x5, 0xf3, 0x0, 0xd,
    0xb0, 0xbd, 0x0, 0x0, 0x7f, 0x2f, 0x70, 0x0,
    0x1, 0xfb, 0xf2, 0x0, 0x0, 0xc, 0xfc, 0x0,
    0x0, 0x0, 0x6f, 0x60, 0x0,

    /* U+0077 "w" */
    0xdd, 0x0, 0x6, 0xf7, 0x0, 0xd, 0xb8, 0xf1,
    0x0, 0xaf, 0xa0, 0x1, 0xf6, 0x3f, 0x50, 0xe,
    0xee, 0x0, 0x5f, 0x20, 0xf9, 0x3, 0xf7, 0xf2,
    0xa, 0xd0, 0xa, 0xd0, 0x7e, 0x1f, 0x60, 0xe8,
    0x0, 0x6f, 0x1b, 0xa0, 0xda, 0x3f, 0x40, 0x1,
    0xf5, 0xf6, 0x9, 0xe7, 0xf0, 0x0, 0xc, 0xcf,
    0x20, 0x5f, 0xda, 0x0, 0x0, 0x8f, 0xe0, 0x1,
    0xff, 0x50, 0x0, 0x3, 0xfa, 0x0, 0xd, 0xf1,
    0x0,

    /* U+0078 "x" */
    0x4f, 0xa0, 0x0, 0x9f, 0x40, 0xaf, 0x40, 0x4f,
    0x90, 0x1, 0xed, 0x1e, 0xd0, 0x0, 0x4, 0xfd,
    0xf3, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0,
    0xdf, 0xd0, 0x0, 0x0, 0x9f, 0x7f, 0x90, 0x0,
    0x3f, 0x90, 0x9f, 0x30, 0xd, 0xe1, 0x1, 0xed,
    0x8, 0xf5, 0x0, 0x6, 0xf8,

    /* U+0079 "y" */
    0x8f, 0x20, 0x0, 0xe, 0xa2, 0xf8, 0x0, 0x4,
    0xf5, 0xd, 0xd0, 0x0, 0x9f, 0x0, 0x7f, 0x20,
    0xe, 0x90, 0x1, 0xf8, 0x4, 0xf4, 0x0, 0xc,
    0xd0, 0xae, 0x0, 0x0, 0x6f, 0x2e, 0x90, 0x0,
    0x1, 0xfa, 0xf3, 0x0, 0x0, 0xb, 0xfd, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0xa, 0xcf,
    0x50, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0,

    /* U+007A "z" */
    0x4f, 0xff, 0xff, 0xff, 0x51, 0x55, 0x55, 0x5f,
    0xe1, 0x0, 0x0, 0xa, 0xf3, 0x0, 0x0, 0x7,
    0xf7, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x2,
    0xed, 0x0, 0x0, 0x0, 0xde, 0x20, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x0, 0x7f, 0xb6, 0x66, 0x66,
    0x4a, 0xff, 0xff, 0xff, 0xf9,

    /* U+007B "{" */
    0x0, 0x9, 0xf9, 0x0, 0x5f, 0xa4, 0x0, 0x9e,
    0x0, 0x0, 0xad, 0x0, 0x0, 0xac, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0xcb, 0x0, 0x1, 0xf7, 0x0,
    0x4d, 0xd1, 0x0, 0x8f, 0x60, 0x0, 0x5, 0xf4,
    0x0, 0x0, 0xda, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0xad, 0x0, 0x0, 0x9d, 0x0,
    0x0, 0x5f, 0x94, 0x0, 0x9, 0xf9,

    /* U+007C "|" */
    0x5f, 0x5, 0xf0, 0x5f, 0x5, 0xf0, 0x5f, 0x5,
    0xf0, 0x5f, 0x5, 0xf0, 0x5f, 0x5, 0xf0, 0x5f,
    0x5, 0xf0, 0x5f, 0x5, 0xf0, 0x5f, 0x5, 0xf0,
    0x5f, 0x5, 0xf0,

    /* U+007D "}" */
    0x9f, 0x90, 0x0, 0x4a, 0xf5, 0x0, 0x0, 0xe9,
    0x0, 0x0, 0xca, 0x0, 0x0, 0xca, 0x0, 0x0,
    0xcb, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0xd, 0xd4, 0x0, 0x6, 0xf8, 0x0, 0x4f,
    0x50, 0x0, 0xad, 0x0, 0x0, 0xcb, 0x0, 0x0,
    0xca, 0x0, 0x0, 0xca, 0x0, 0x0, 0xe9, 0x0,
    0x49, 0xf6, 0x0, 0x9f, 0x90, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xc6,
    0x0, 0x18, 0x3f, 0xed, 0xff, 0xec, 0xfb, 0x35,
    0x0, 0x28, 0xef, 0xb3, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xba, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0xdf, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x1, 0x6a,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x7c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xb7, 0x3f,
    0xf2, 0x0, 0x0, 0x5f, 0xff, 0xfe, 0x95, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x5, 0xff, 0x73, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0,
    0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x20, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0x69,
    0x8f, 0xf2, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0x20, 0x3, 0x58, 0xfe, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf2, 0x2d, 0xff, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0xfd, 0xb, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x4, 0xbd, 0xc8, 0x10,
    0xaf, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x51, 0x6, 0x88, 0x88, 0x88, 0x88, 0x88, 0x30,
    0x15, 0xf7, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0x8f, 0xfd, 0xcf, 0xf3, 0x33, 0x33, 0x33,
    0x6f, 0xec, 0xdf, 0xf2, 0xc, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0x70, 0x2f, 0xf2, 0xc, 0xe0, 0x0,
    0x0, 0x0, 0x3f, 0x80, 0x2f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf4, 0x2d,
    0xf9, 0x99, 0x99, 0x99, 0xbf, 0x92, 0x4f, 0xf2,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x2f,
    0xfb, 0xaf, 0xf1, 0x11, 0x11, 0x11, 0x5f, 0xda,
    0xbf, 0xf9, 0x8e, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0xc8, 0x9f, 0xf2, 0xc, 0xe0, 0x0, 0x0, 0x0,
    0x3f, 0x70, 0x2f, 0xf6, 0x4d, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0xa4, 0x6f, 0xfe, 0xef, 0xfb, 0xbb,
    0xbb, 0xbb, 0xcf, 0xfe, 0xef, 0xc2, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x2c,

    /* U+F00B "" */
    0x58, 0x88, 0x70, 0x28, 0x88, 0x88, 0x88, 0x88,
    0x85, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xd1, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xe1, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x58, 0x88, 0x60, 0x27, 0x88, 0x88,
    0x88, 0x88, 0x85, 0x47, 0x77, 0x50, 0x17, 0x77,
    0x77, 0x77, 0x77, 0x74, 0xff, 0xff, 0xf3, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xf1, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0x90, 0x9, 0xd1, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf9, 0x0, 0x9f, 0xfd,
    0x10, 0x0, 0x7, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xd1, 0x0, 0x7f, 0xff, 0xf9, 0x0, 0x0,
    0x2e, 0xff, 0xfd, 0x17, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xef, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x2d, 0xb0, 0x0, 0x0, 0x6, 0xe6, 0xd, 0xff,
    0xc0, 0x0, 0x6, 0xff, 0xf3, 0xcf, 0xff, 0xc0,
    0x6, 0xff, 0xff, 0x31, 0xdf, 0xff, 0xc7, 0xff,
    0xff, 0x50, 0x1, 0xdf, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x6, 0xff, 0xff, 0xdf,
    0xff, 0xc0, 0x6, 0xff, 0xff, 0x51, 0xdf, 0xff,
    0xc0, 0xff, 0xff, 0x50, 0x1, 0xdf, 0xff, 0x58,
    0xff, 0x50, 0x0, 0x1, 0xdf, 0xd0, 0x5, 0x30,
    0x0, 0x0, 0x1, 0x61, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0xcd, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xe3, 0x3, 0xff,
    0xa0, 0xb, 0xc1, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0x3f, 0xfa, 0x4, 0xff, 0xd1, 0x0, 0x4, 0xff,
    0xf6, 0x3, 0xff, 0xa0, 0x1e, 0xff, 0xa0, 0x0,
    0xdf, 0xf7, 0x0, 0x3f, 0xfa, 0x0, 0x2e, 0xff,
    0x40, 0x3f, 0xfc, 0x0, 0x3, 0xff, 0xa0, 0x0,
    0x6f, 0xfa, 0x8, 0xff, 0x60, 0x0, 0x3f, 0xfa,
    0x0, 0x0, 0xef, 0xf0, 0xaf, 0xf2, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0xb, 0xff, 0x1b, 0xff, 0x10,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xbf, 0xf1, 0x9f,
    0xf3, 0x0, 0x0, 0x24, 0x0, 0x0, 0xd, 0xff,
    0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf8, 0x0, 0x9f, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x9f, 0xfe, 0x10, 0x0, 0xcf, 0xfe,
    0x71, 0x0, 0x4, 0xcf, 0xff, 0x50, 0x0, 0x1,
    0xdf, 0xff, 0xfe, 0xdf, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xce, 0xfd, 0xa5,
    0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x4, 0x66, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x78, 0x17, 0xff, 0xff,
    0xff, 0x71, 0x87, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x3f, 0xff,
    0xff, 0xfe, 0x88, 0xef, 0xff, 0xff, 0xf3, 0x8,
    0xff, 0xff, 0xd0, 0x0, 0xd, 0xff, 0xff, 0x80,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0xbf, 0xff, 0x30, 0x0, 0x3, 0xff,
    0xfb, 0x0, 0x1, 0xcf, 0xff, 0x80, 0x0, 0x8,
    0xff, 0xfc, 0x10, 0x3e, 0xff, 0xff, 0xf6, 0x0,
    0x6f, 0xff, 0xff, 0xe3, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0x8e, 0xff, 0xff, 0xff, 0xe8, 0xfd, 0x0, 0x0,
    0x11, 0x1, 0x9f, 0xff, 0xf9, 0x10, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xee, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0x92, 0x0, 0x6b,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xe4, 0xa, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xf6, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0x31, 0xcf, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x25, 0x70,
    0xaf, 0xff, 0xf2, 0x0, 0x0, 0x1, 0xbf, 0xfa,
    0x8, 0xff, 0xb0, 0x7f, 0xff, 0x40, 0x0, 0x2,
    0xdf, 0xf8, 0xa, 0xff, 0xff, 0xd2, 0x5f, 0xff,
    0x50, 0x4, 0xff, 0xf5, 0x1d, 0xff, 0xff, 0xff,
    0xe4, 0x2e, 0xff, 0x70, 0xdf, 0xe3, 0x3e, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x1c, 0xff, 0x13, 0xb1,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0x60, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd,
    0x88, 0xbf, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x70, 0x3, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf7, 0x0, 0x3f, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x70,
    0x3, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf5, 0x0, 0x2f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x9, 0xaa, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xee, 0xef, 0xff, 0xfe, 0xee, 0xc0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x24, 0x44, 0x44, 0x7,
    0xff, 0x70, 0x44, 0x44, 0x42, 0xff, 0xff, 0xff,
    0xc1, 0x66, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x66, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x66, 0xc4,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x0, 0x4, 0x44, 0x44, 0x44, 0x44, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x6, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x50, 0x0, 0xbf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x10, 0x6f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xfa, 0xe, 0xff, 0xcc, 0xcc, 0x20, 0x0, 0x0,
    0xbc, 0xcc, 0xef, 0xf2, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0x88, 0x8e, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x15, 0x66, 0x40, 0x0, 0x5,
    0xcb, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0x92,
    0x7, 0xff, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x66, 0xff, 0x0, 0x8f, 0xff, 0xa4, 0x12,
    0x5b, 0xff, 0xfd, 0xff, 0x4, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xd, 0xff, 0x30,
    0x0, 0x0, 0x45, 0x46, 0xff, 0xff, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0x8f,
    0xf1, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0x2, 0x10, 0x0, 0x0, 0x0, 0x12, 0x22, 0x22,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x67, 0x77, 0x77, 0x75, 0x0, 0x0,
    0x0, 0x6, 0x73, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x3f, 0xf6, 0xff, 0xff, 0xee, 0xfd,
    0x0, 0x0, 0x0, 0xcf, 0xf1, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xa0, 0xff, 0xff,
    0xfb, 0x20, 0x0, 0x2, 0xbf, 0xfe, 0x10, 0xff,
    0x8d, 0xff, 0xfc, 0xa9, 0xcf, 0xff, 0xe2, 0x0,
    0xff, 0x61, 0x9f, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0xff, 0x70, 0x1, 0x7c, 0xee, 0xd9, 0x30,
    0x0, 0x0, 0x56, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0, 0x3,
    0xef, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xdf,
    0xff, 0xf0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x0,
    0x0, 0xbf, 0xf0, 0x0, 0x0, 0x0, 0xbe, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xf0, 0x7, 0x10, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x3f, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xd, 0xf3, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0x70, 0x8b, 0xbb,
    0xdf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x40, 0x0, 0x0, 0x2d, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0x0, 0x0, 0x40, 0x1c,
    0xf4, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x5f, 0xb0, 0x1e, 0xe1, 0x0, 0x0, 0x3, 0xef,
    0xff, 0x0, 0x0, 0xaf, 0xa0, 0x6f, 0x70, 0xdf,
    0xff, 0xff, 0xff, 0xf0, 0x7, 0x10, 0xbf, 0x30,
    0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0x3, 0xfd,
    0x3, 0xf9, 0xa, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x7, 0xf5, 0xe, 0xc0, 0x8f, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x4f, 0x70, 0xdd, 0x7,
    0xf3, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1d, 0xf3,
    0xf, 0xb0, 0x9f, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x3, 0xf7, 0x7, 0xf6, 0xc, 0xf0, 0x7b, 0xbb,
    0xdf, 0xff, 0xf0, 0x0, 0x3, 0xfe, 0x12, 0xfa,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x3, 0xff,
    0x40, 0xaf, 0x30, 0x0, 0x0, 0x0, 0xbf, 0xf0,
    0x0, 0x3c, 0x30, 0x6f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0x6f, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x90, 0x0, 0x0,

    /* U+F03E "" */
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xa3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xd5, 0x6e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0x50, 0x7,
    0xff, 0xff, 0xf5, 0x9, 0xff, 0xff, 0xff, 0xfb,
    0xbf, 0xff, 0xff, 0x50, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xcb, 0xff, 0xf5, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xfc, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xc0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x74, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x47, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F043 "" */
    0x0, 0x0, 0x6, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0x2c, 0xf8, 0x4f, 0xff,
    0xff, 0xff, 0xf0, 0x7f, 0xf2, 0x5c, 0xff, 0xff,
    0xfb, 0x0, 0xef, 0xe5, 0x8, 0xff, 0xff, 0x30,
    0x2, 0xef, 0xff, 0xff, 0xff, 0x50, 0x0, 0x1,
    0xaf, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x3,
    0x31, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x6b, 0x90, 0x0, 0x0, 0x3, 0xa2, 0x9f, 0xe0,
    0x0, 0x0, 0x4f, 0xf9, 0x9f, 0xe0, 0x0, 0x5,
    0xff, 0xfa, 0x9f, 0xe0, 0x0, 0x6f, 0xff, 0xfa,
    0x9f, 0xe0, 0x7, 0xff, 0xff, 0xfa, 0x9f, 0xe0,
    0x8f, 0xff, 0xff, 0xfa, 0x9f, 0xe9, 0xff, 0xff,
    0xff, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x9f, 0xfe,
    0xff, 0xff, 0xff, 0xfa, 0x9f, 0xe1, 0xdf, 0xff,
    0xff, 0xfa, 0x9f, 0xe0, 0x1c, 0xff, 0xff, 0xfa,
    0x9f, 0xe0, 0x0, 0xbf, 0xff, 0xfa, 0x9f, 0xe0,
    0x0, 0xa, 0xff, 0xfa, 0x9f, 0xe0, 0x0, 0x0,
    0x9f, 0xfa, 0x9f, 0xe0, 0x0, 0x0, 0x8, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x3a, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x3a, 0xbb, 0xb9, 0x10, 0x3, 0xab, 0xbb, 0x91,
    0xef, 0xff, 0xff, 0xa0, 0xe, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xfe, 0x40, 0x7, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x91,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x4a, 0x20, 0x0, 0x0, 0xa, 0xb4, 0xbf, 0xe3,
    0x0, 0x0, 0xf, 0xf8, 0xcf, 0xff, 0x40, 0x0,
    0xf, 0xf8, 0xcf, 0xff, 0xf5, 0x0, 0xf, 0xf8,
    0xcf, 0xff, 0xff, 0x60, 0xf, 0xf8, 0xcf, 0xff,
    0xff, 0xf7, 0xf, 0xf8, 0xcf, 0xff, 0xff, 0xff,
    0x9f, 0xf8, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xff, 0xff, 0xdf, 0xf8, 0xcf, 0xff, 0xff, 0xfc,
    0x1f, 0xf8, 0xcf, 0xff, 0xff, 0xb0, 0xf, 0xf8,
    0xcf, 0xff, 0xfa, 0x0, 0xf, 0xf8, 0xcf, 0xff,
    0x80, 0x0, 0xf, 0xf8, 0xbf, 0xf7, 0x0, 0x0,
    0xf, 0xf8, 0x7f, 0x60, 0x0, 0x0, 0xf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2, 0xca, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x34, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x42, 0x0, 0x3, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x10, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x20, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0,
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff, 0x50,
    0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f,
    0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0x20, 0x0, 0x0, 0x0, 0x3c, 0x60,

    /* U+F054 "" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc1,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x20, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0,
    0x0, 0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff,
    0x50, 0x0, 0x0, 0x9f, 0xff, 0x60, 0x0, 0x0,
    0x9f, 0xff, 0x60, 0x0, 0x0, 0xe, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x3d, 0x50, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x39, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x36, 0x77, 0x77, 0xef, 0xfc, 0x77, 0x77, 0x61,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xae, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x61,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x3, 0x68, 0x87, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xff, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xfd, 0x63, 0x25, 0xbf, 0xff, 0x70, 0x0, 0x0,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xa0, 0x0, 0x6, 0xff, 0xfd, 0x0, 0x8, 0xfc,
    0x20, 0x9f, 0xff, 0xa0, 0x2, 0xff, 0xff, 0x50,
    0x0, 0x8f, 0xfe, 0x12, 0xff, 0xff, 0x60, 0xcf,
    0xff, 0xf2, 0x16, 0x7f, 0xff, 0xf5, 0xe, 0xff,
    0xfe, 0x1e, 0xff, 0xff, 0x12, 0xff, 0xff, 0xff,
    0x70, 0xdf, 0xff, 0xf2, 0x6f, 0xff, 0xf3, 0xe,
    0xff, 0xff, 0xf3, 0xf, 0xff, 0xfb, 0x0, 0xbf,
    0xff, 0x90, 0x4f, 0xff, 0xf8, 0x5, 0xff, 0xfe,
    0x10, 0x1, 0xdf, 0xff, 0x40, 0x28, 0x84, 0x1,
    0xef, 0xff, 0x30, 0x0, 0x1, 0xbf, 0xff, 0x60,
    0x0, 0x4, 0xef, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x6e, 0xff, 0xfb, 0xbe, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x71,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x4a, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x60, 0x0, 0x15, 0x78, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xa6, 0xdf, 0xff,
    0xff, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xe8, 0x32, 0x5b, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x10,
    0x6, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xf8, 0x7f, 0xd3, 0x9, 0xff, 0xfb, 0x0,
    0x0, 0x1e, 0x70, 0x1, 0xbf, 0xfe, 0xff, 0xf2,
    0x1f, 0xff, 0xf7, 0x0, 0x9, 0xff, 0xa0, 0x0,
    0x8f, 0xff, 0xff, 0x70, 0xdf, 0xff, 0xf1, 0x0,
    0xcf, 0xff, 0xd1, 0x0, 0x5f, 0xff, 0xf9, 0xc,
    0xff, 0xff, 0x30, 0x5, 0xff, 0xff, 0x60, 0x0,
    0x2d, 0xff, 0xb0, 0xef, 0xff, 0xb0, 0x0, 0xa,
    0xff, 0xfc, 0x0, 0x0, 0xa, 0xff, 0xef, 0xff,
    0xe1, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x10, 0x0, 0x3, 0xef, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xcb, 0x80,
    0x1, 0xbf, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xdf, 0xfe, 0x70, 0x0, 0x8f, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xcd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x33,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf9, 0x0, 0x6f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfa, 0x0, 0x6f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb,
    0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfd, 0x0, 0x9f,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xcb, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff,
    0xfc, 0x0, 0x2, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x60, 0xa, 0xff, 0xff,
    0xff, 0xfe, 0x42, 0xcf, 0xff, 0xff, 0xff, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x23,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf9, 0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0x1,
    0x29, 0xff, 0x90, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xfe, 0x20,
    0x5, 0xff, 0xff, 0xff, 0xfd, 0x9a, 0xad, 0xff,
    0xd0, 0x5f, 0xff, 0xbd, 0xff, 0xe2, 0x0, 0x0,
    0xcf, 0x44, 0xff, 0xf9, 0x8, 0xfe, 0x20, 0x0,
    0x0, 0x4, 0x4f, 0xff, 0x90, 0x4, 0xd2, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xa1, 0x91, 0x6,
    0xf6, 0x0, 0x0, 0x2, 0xef, 0xfb, 0xc, 0xfc,
    0x8, 0xff, 0x60, 0xef, 0xff, 0xff, 0xb0, 0x1d,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xfc, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xfe, 0xdd, 0xdd, 0xb0,
    0x0, 0x0, 0x2d, 0xde, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfa, 0xcf, 0xfe, 0x30, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xc, 0xff, 0xe3, 0x0,
    0x6, 0xff, 0xf9, 0x0, 0x0, 0xcf, 0xfe, 0x30,
    0x5f, 0xff, 0x90, 0x0, 0x0, 0xc, 0xff, 0xe2,
    0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6,
    0x1b, 0x80, 0x0, 0x0, 0x0, 0x0, 0xb, 0x90,

    /* U+F078 "" */
    0x3, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0x20,
    0x6f, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2,
    0xaf, 0xfe, 0x30, 0x0, 0x0, 0x5, 0xff, 0xf6,
    0x1c, 0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0x90,
    0x1, 0xcf, 0xfe, 0x30, 0x5, 0xff, 0xf9, 0x0,
    0x0, 0x1c, 0xff, 0xe3, 0x5f, 0xff, 0x90, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0x90, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x26, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xa0,
    0x0, 0x44, 0x44, 0x44, 0x44, 0x42, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xfa, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xa0, 0xaf, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0xe, 0xfd, 0xef, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0xb, 0xe2, 0xdf, 0x67,
    0xf5, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x4,
    0x15, 0xfe, 0x3, 0x20, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x6f, 0xd6, 0xfe, 0x4f, 0xf1,
    0x0, 0x0, 0xdf, 0x94, 0x44, 0x44, 0x41, 0x3f,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x24, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x4f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0,

    /* U+F07B "" */
    0x17, 0x88, 0x88, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x44,
    0x44, 0x44, 0x30, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x47, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x11, 0x2f, 0xff, 0xf7, 0x11, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x24, 0x44, 0x42, 0x1f,
    0xff, 0xf6, 0x24, 0x44, 0x42, 0xff, 0xff, 0xfc,
    0x8, 0xbb, 0xa2, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x55, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x66, 0xc4,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb8,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x90,
    0x0, 0x0, 0x4, 0xa5, 0x0, 0x0, 0xaf, 0xff,
    0xd0, 0x0, 0x0, 0x7d, 0xff, 0xf4, 0x2, 0xcf,
    0xff, 0xe2, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xe9,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xfc, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xfd, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x35, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xfa, 0x0, 0x0, 0x1, 0x9c, 0xa1,
    0xaf, 0xfe, 0xff, 0x60, 0x0, 0x2e, 0xff, 0xf9,
    0xef, 0x60, 0xaf, 0xb0, 0x3, 0xef, 0xff, 0xb0,
    0xef, 0x92, 0xcf, 0x90, 0x3e, 0xff, 0xfa, 0x0,
    0x7f, 0xff, 0xff, 0xe6, 0xff, 0xff, 0xa0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x36, 0xef, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0xaf, 0xfe, 0xff, 0xc2, 0xdf, 0xff, 0xd1, 0x0,
    0xef, 0x60, 0xaf, 0xa0, 0x1c, 0xff, 0xfe, 0x20,
    0xef, 0x92, 0xcf, 0xa0, 0x0, 0xcf, 0xff, 0xe2,
    0x7f, 0xff, 0xff, 0x40, 0x0, 0xb, 0xff, 0xf9,
    0x8, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x58, 0x50,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf1, 0x68, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x7f, 0x80,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x7f, 0xf8,
    0x36, 0x62, 0xaf, 0xff, 0xff, 0xf1, 0x36, 0x66,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xf6, 0x22, 0x22,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf7, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x20, 0x0, 0x0,

    /* U+F0C7 "" */
    0x5, 0x66, 0x66, 0x66, 0x66, 0x64, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xef, 0xf9, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf6,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf9,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xc5, 0x7f, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfe, 0x0, 0x5, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfa, 0x0, 0x1, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfd, 0x0, 0x4, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0x93, 0x4d, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,

    /* U+F0C9 "" */
    0x79, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x95,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x30, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xa1, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x3d, 0xfe, 0x42,
    0xdf, 0xff, 0xff, 0xff, 0xfd, 0x26, 0xff, 0xff,
    0xf9, 0x19, 0xff, 0xff, 0xff, 0x91, 0xbf, 0xff,
    0xff, 0xff, 0xd3, 0x5f, 0xff, 0xe5, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0x99, 0x17, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x77, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F0E7 "" */
    0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xa5, 0x55, 0x40, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x1, 0x22, 0x23, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x8d, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x8b, 0xbc, 0xfa, 0xfd, 0xbb, 0x90, 0x0, 0x0,
    0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x95, 0xff, 0xff, 0xf1, 0x79, 0x0,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf1, 0x7f, 0xb0,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf1, 0x7f, 0xfa,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf2, 0x2, 0x21,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xdd, 0xdc,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x7a, 0xaa, 0x58, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x8, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xcf, 0xfa, 0x40, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f,
    0xf4, 0x4, 0xd0, 0x2f, 0x0, 0xf3, 0x3, 0xf0,
    0xf, 0xf4, 0xff, 0x40, 0x5d, 0x2, 0xf0, 0xf,
    0x40, 0x4f, 0x0, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0x22, 0xb7, 0x29, 0xa2, 0x4f, 0x42, 0xcf,
    0xff, 0x4f, 0xff, 0xf0, 0xa, 0x60, 0x79, 0x2,
    0xf2, 0xb, 0xff, 0xf4, 0xff, 0xff, 0xdd, 0xfe,
    0xdf, 0xfd, 0xef, 0xed, 0xff, 0xff, 0x4f, 0xf8,
    0x48, 0xe4, 0x44, 0x44, 0x44, 0x47, 0xf4, 0x5f,
    0xf4, 0xff, 0x40, 0x4d, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0xff, 0x4f, 0xf7, 0x48, 0xe4, 0x44,
    0x44, 0x44, 0x47, 0xf4, 0x4f, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8b, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x17, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x29,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x56,
    0x66, 0x66, 0x7f, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x58, 0x88, 0x88, 0x87, 0x6, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfe, 0xf, 0x90, 0x0, 0xff, 0xff,
    0xff, 0xfe, 0xf, 0xf9, 0x0, 0xff, 0xff, 0xff,
    0xfe, 0xf, 0xff, 0x90, 0xff, 0xff, 0xff, 0xfe,
    0xf, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0x32,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x14, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x36,
    0x89, 0xa9, 0x74, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc6,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x6, 0xff,
    0xff, 0xfc, 0x75, 0x43, 0x46, 0x9e, 0xff, 0xff,
    0xb1, 0x8, 0xff, 0xfe, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xff, 0xe2, 0xcf, 0xfa, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0x40, 0xb6, 0x0, 0x0, 0x59, 0xde, 0xfe, 0xc7,
    0x20, 0x0, 0x1b, 0x50, 0x0, 0x0, 0x5, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xfe, 0xde, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x40,
    0x0, 0x2, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x1, 0xb6, 0x0, 0x0, 0x0, 0x0, 0x1b, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0x5f, 0xf4, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x3f, 0xf5, 0xff, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xef,
    0x5f, 0xf4, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xf5, 0xff, 0x45, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F241 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x4d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x46, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x30, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F242 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4c,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x4c, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x46, 0x88, 0x88,
    0x88, 0x80, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F243 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x49,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0x9f, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x49, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0x9f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x44, 0x88, 0x87,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F244 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x9f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xef, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x2e, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xa1, 0x0, 0xcf, 0xff, 0xd4,
    0x9f, 0x55, 0x55, 0x55, 0x55, 0x55, 0x7f, 0xf7,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xaf, 0xff, 0xa0, 0x0,
    0xb, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xb2, 0x0,
    0x9c, 0x90, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0,
    0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0x2, 0xbb, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xf9, 0x9f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xbd, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x1, 0x7b, 0xdd, 0xb8, 0x20, 0x0, 0x0,
    0x5f, 0xff, 0xdf, 0xff, 0xf6, 0x0, 0x4, 0xff,
    0xff, 0x68, 0xff, 0xff, 0x40, 0xe, 0xff, 0xff,
    0x60, 0x8f, 0xff, 0xd0, 0x4f, 0xff, 0xff, 0x60,
    0x9, 0xff, 0xf3, 0x8f, 0xf6, 0xbf, 0x61, 0xc0,
    0x9f, 0xf7, 0xbf, 0xf6, 0xb, 0x60, 0xe2, 0x5f,
    0xf9, 0xdf, 0xff, 0x50, 0x20, 0x33, 0xff, 0xfb,
    0xef, 0xff, 0xf5, 0x0, 0x2e, 0xff, 0xfc, 0xef,
    0xff, 0xfc, 0x0, 0x7f, 0xff, 0xfc, 0xdf, 0xff,
    0xd1, 0x0, 0x9, 0xff, 0xfc, 0xcf, 0xfc, 0x13,
    0x50, 0x90, 0xaf, 0xfb, 0xaf, 0xf2, 0x4f, 0x60,
    0xf3, 0x2f, 0xf9, 0x6f, 0xfd, 0xff, 0x70, 0x52,
    0xef, 0xf6, 0x1f, 0xff, 0xff, 0x70, 0x2e, 0xff,
    0xf1, 0x9, 0xff, 0xff, 0x72, 0xef, 0xff, 0x90,
    0x0, 0xbf, 0xff, 0xae, 0xff, 0xfd, 0x10, 0x0,
    0x5, 0xcf, 0xff, 0xfd, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x5, 0x88, 0x88, 0x30, 0x0, 0x0,
    0x56, 0x66, 0x7f, 0xff, 0xff, 0xe6, 0x66, 0x63,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd8,
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0x1f, 0xf6, 0xaf, 0xc4, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0x1f, 0xf6, 0xaf, 0xc4, 0xff, 0xa0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x31, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x44, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x44, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0x44, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0x41, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x57, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x72, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0xbf, 0xff, 0xff, 0xf8,
    0xaf, 0xff, 0xa8, 0xff, 0xff, 0xf8, 0x0, 0xbf,
    0xff, 0xff, 0xfa, 0x0, 0xaf, 0xa0, 0xa, 0xff,
    0xff, 0x80, 0xbf, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x60, 0x3, 0xff, 0xff, 0xf8, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x9f, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xff, 0xb0, 0x3, 0xe3, 0x0,
    0xbf, 0xff, 0xf8, 0x0, 0x4f, 0xff, 0xff, 0xfe,
    0x13, 0xff, 0xf3, 0x1e, 0xff, 0xff, 0x80, 0x0,
    0x4f, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90,

    /* U+F7C2 "" */
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xcf,
    0x47, 0xf4, 0xd8, 0x4f, 0xf5, 0xc, 0xff, 0x3,
    0xe0, 0xc5, 0xe, 0xf5, 0xcf, 0xff, 0x3, 0xe0,
    0xc5, 0xe, 0xf5, 0xff, 0xff, 0x24, 0xe2, 0xc6,
    0x2e, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x1, 0x34,
    0x44, 0x44, 0x44, 0x42, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x10, 0x0, 0x3e, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0, 0x4f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10,
    0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x3e, 0xff, 0xfe, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xca, 0x0, 0x2e, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 80, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 80, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21, .adv_w = 102, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 36, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 106, .adv_w = 160, .box_w = 10, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 191, .adv_w = 256, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 289, .adv_w = 192, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 379, .adv_w = 55, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 387, .adv_w = 96, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 432, .adv_w = 96, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 477, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 498, .adv_w = 168, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 539, .adv_w = 80, .box_w = 3, .box_h = 5, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 547, .adv_w = 96, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 553, .adv_w = 80, .box_w = 3, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 556, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 591, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 661, .adv_w = 160, .box_w = 6, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 703, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 773, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 843, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 913, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 983, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1053, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1123, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1193, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1263, .adv_w = 80, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1278, .adv_w = 80, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1298, .adv_w = 168, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1348, .adv_w = 168, .box_w = 9, .box_h = 6, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1375, .adv_w = 168, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1425, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1495, .adv_w = 292, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1657, .adv_w = 192, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1755, .adv_w = 192, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1832, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1923, .adv_w = 208, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2007, .adv_w = 192, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2084, .adv_w = 176, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2154, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2245, .adv_w = 208, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2322, .adv_w = 80, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2343, .adv_w = 144, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2399, .adv_w = 192, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2476, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2539, .adv_w = 240, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2630, .adv_w = 208, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2707, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2805, .adv_w = 192, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2882, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2987, .adv_w = 208, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3071, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3155, .adv_w = 176, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3232, .adv_w = 208, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3309, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3393, .adv_w = 272, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3512, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3596, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3680, .adv_w = 176, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3757, .adv_w = 80, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3793, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3828, .adv_w = 80, .box_w = 4, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3864, .adv_w = 135, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 3896, .adv_w = 160, .box_w = 12, .box_h = 3, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 3914, .adv_w = 96, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 11},
    {.bitmap_index = 3922, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3972, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4035, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4080, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4143, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4193, .adv_w = 80, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4235, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4298, .adv_w = 160, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4354, .adv_w = 64, .box_w = 2, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4368, .adv_w = 64, .box_w = 4, .box_h = 18, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4404, .adv_w = 144, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4460, .adv_w = 64, .box_w = 2, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4474, .adv_w = 240, .box_w = 13, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4539, .adv_w = 160, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4579, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4629, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4692, .adv_w = 160, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4755, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4785, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4830, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4865, .adv_w = 160, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4905, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4950, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5015, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5060, .adv_w = 144, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5123, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5168, .adv_w = 96, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5222, .adv_w = 75, .box_w = 3, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5249, .adv_w = 96, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5303, .adv_w = 168, .box_w = 10, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 5328, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5509, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5635, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5788, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5914, .adv_w = 198, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5999, .adv_w = 288, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6170, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6341, .adv_w = 324, .box_w = 21, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6520, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6691, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6838, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7009, .adv_w = 144, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7077, .adv_w = 216, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7182, .adv_w = 324, .box_w = 21, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7371, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7497, .adv_w = 198, .box_w = 13, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7621, .adv_w = 252, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 7723, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7875, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8011, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8147, .adv_w = 252, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8249, .adv_w = 252, .box_w = 18, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8402, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8490, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8578, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8714, .adv_w = 252, .box_w = 16, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 8746, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8893, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9112, .adv_w = 324, .box_w = 22, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9321, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9474, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 9554, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 9634, .adv_w = 360, .box_w = 24, .box_h = 15, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 9814, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9940, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10111, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10292, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10428, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10580, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10716, .adv_w = 252, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10836, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10962, .adv_w = 180, .box_w = 13, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11086, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11238, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11390, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11537, .adv_w = 288, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11737, .adv_w = 216, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11870, .adv_w = 360, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12077, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12215, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12353, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12491, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12629, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12767, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12940, .adv_w = 252, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13073, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13225, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13406, .adv_w = 360, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13567, .adv_w = 216, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13700, .adv_w = 290, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_arial_18 = {
#else
lv_font_t lv_font_arial_18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 18,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_ARIAL_18*/

