#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG__BT01_ALPHA_15X15
#define LV_ATTRIBUTE_IMG__BT01_ALPHA_15X15
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG__BT01_ALPHA_15X15 uint8_t _bt01_alpha_15x15_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0xff, 0x37, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x72, 0x17, 0x03, 0x00, 0x00, 0x00, 0x00, 
  0x37, 0x18, 0x37, 0x55, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x37, 0xc6, 0x37, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x72, 0x37, 0xc6, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x37, 0x55, 0x37, 0x77, 0x00, 0x00, 0x37, 0x55, 0x37, 0x4e, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x37, 0xc6, 0x37, 0x72, 0x37, 0xff, 0x37, 0x55, 0x37, 0xc6, 0x37, 0x55, 0x00, 0x00, 0x37, 0x1b, 0x37, 0xff, 0x37, 0x39, 0x17, 0x22, 0x37, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xc6, 0x37, 0xff, 0x37, 0xff, 0x37, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x81, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x53, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xc6, 0x37, 0xff, 0x37, 0xff, 0x37, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x7e, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x37, 0xc6, 0x37, 0x72, 0x37, 0xff, 0x37, 0x55, 0x37, 0xc6, 0x37, 0x55, 0x00, 0x00, 0x37, 0x1b, 0x37, 0xff, 0x37, 0x39, 0x17, 0x22, 0x37, 0xff, 0x00, 0x00, 
  0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x37, 0x55, 0x37, 0x96, 0x00, 0x00, 0x37, 0x55, 0x37, 0x4e, 0x00, 0x00, 
  0x37, 0x18, 0x37, 0x55, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x55, 0x37, 0xc6, 0x37, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x72, 0x37, 0xc6, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0xff, 0x37, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x72, 0x17, 0x06, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xff, 0x37, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x72, 0x75, 0x05, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xbc, 0x1c, 0x18, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x72, 0xdb, 0x14, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0x77, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0x4e, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0x72, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x1b, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0x39, 0xdb, 0x14, 0x22, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x14, 0x81, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x1c, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdc, 0x14, 0x53, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x14, 0x7e, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x1c, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0x72, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0xc6, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x1b, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0x39, 0xdc, 0x14, 0x22, 0xdb, 0x14, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0x96, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0x4e, 0x00, 0x00, 0x00, 
  0xbc, 0x1c, 0x18, 0xdb, 0x14, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0x55, 0xdb, 0x14, 0xc6, 0xbb, 0x14, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x72, 0xdb, 0x14, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0x72, 0x7b, 0x05, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x14, 0xff, 0xbb, 0x14, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x72, 0x05, 0x75, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x1c, 0xbc, 0x18, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x72, 0x14, 0xdb, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0x77, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0x4e, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0x72, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x1b, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0x39, 0x14, 0xdb, 0x22, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xbb, 0x81, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x1c, 0xdb, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdc, 0x53, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdc, 0x7e, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x1c, 0xdb, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0x72, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0xc6, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x1b, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0x39, 0x14, 0xdc, 0x22, 0x14, 0xdb, 0xff, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0x96, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0x4e, 0x00, 0x00, 0x00, 
  0x1c, 0xbc, 0x18, 0x14, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0x55, 0x14, 0xdb, 0xc6, 0x14, 0xbb, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x72, 0x14, 0xdb, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0x72, 0x05, 0x7b, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0xdb, 0xff, 0x14, 0xbb, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 32
  /*Pixel format: Alpha 8 bit, Red: 8 bit, Green: 8 bit, Blue: 8 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xff, 0xd9, 0x96, 0x12, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x96, 0x12, 0x72, 0xaa, 0xaa, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xdf, 0x95, 0x15, 0x18, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x97, 0x12, 0xc6, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x96, 0x12, 0x72, 0xdb, 0x97, 0x12, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x96, 0x11, 0x77, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x96, 0x10, 0x4e, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x97, 0x12, 0xc6, 0xd9, 0x96, 0x12, 0x72, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x97, 0x12, 0xc6, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x97, 0x13, 0x1b, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x94, 0x12, 0x39, 0xda, 0x96, 0x0f, 0x22, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x97, 0x12, 0xc6, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xff, 0xd9, 0x96, 0x12, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x94, 0x12, 0x81, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x96, 0x14, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdd, 0x97, 0x12, 0x53, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x97, 0x12, 0xc6, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xff, 0xd9, 0x96, 0x12, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdd, 0x96, 0x12, 0x7e, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x96, 0x14, 0x27, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x97, 0x12, 0xc6, 0xd9, 0x96, 0x12, 0x72, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x97, 0x12, 0xc6, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x97, 0x13, 0x1b, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x94, 0x12, 0x39, 0xe1, 0x96, 0x0f, 0x22, 0xdb, 0x96, 0x12, 0xff, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x96, 0x12, 0x55, 0xda, 0x96, 0x11, 0x96, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x96, 0x10, 0x4e, 0x00, 0x00, 0x00, 0x00, 
  0xdf, 0x95, 0x15, 0x18, 0xdb, 0x96, 0x12, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0x55, 0xdb, 0x97, 0x12, 0xc6, 0xdb, 0x94, 0x12, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x96, 0x12, 0x72, 0xdb, 0x97, 0x12, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xdb, 0x96, 0x12, 0xff, 0xda, 0x95, 0x12, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x96, 0x12, 0x72, 0xd4, 0xaa, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdb, 0x96, 0x12, 0xff, 0xd8, 0x95, 0x11, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t _bt01_alpha_15x15 = {
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .header.always_zero = 0,
  .header.reserved = 0,
  .header.w = 15,
  .header.h = 15,
  .data_size = 225 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .data = _bt01_alpha_15x15_map,
};
