/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_BT_page(lv_ui *ui)
{
    //Write codes BT_page
    ui->BT_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->BT_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->BT_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for BT_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->BT_page, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui->BT_page, &_bg03_320x240, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(ui->BT_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_recolor_opa(ui->BT_page, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_cont_1
    ui->BT_page_cont_1 = lv_obj_create(ui->BT_page);
    lv_obj_set_pos(ui->BT_page_cont_1, 10, 33);
    lv_obj_set_size(ui->BT_page_cont_1, 169, 198);
    lv_obj_set_scrollbar_mode(ui->BT_page_cont_1, LV_SCROLLBAR_MODE_OFF);

    //Write style for BT_page_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_cont_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->BT_page_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->BT_page_cont_1, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->BT_page_cont_1, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_cont_1, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->BT_page_cont_1, lv_color_hex(0x93BCC7), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->BT_page_cont_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_BT_list
    ui->BT_page_BT_list = lv_list_create(ui->BT_page_cont_1);
    ui->BT_page_BT_list_item0 = lv_list_add_btn(ui->BT_page_BT_list, &_bt01_alpha_20x20, "LHC02200000001");
    ui->BT_page_BT_list_item1 = lv_list_add_btn(ui->BT_page_BT_list, &_bt01_alpha_20x20, "LHC02200000002");
    ui->BT_page_BT_list_item2 = lv_list_add_btn(ui->BT_page_BT_list, &_bt01_alpha_20x20, "LHC02200000003");
    ui->BT_page_BT_list_item3 = lv_list_add_btn(ui->BT_page_BT_list, &_bt01_alpha_20x20, "LHC02200000004");
    ui->BT_page_BT_list_item4 = lv_list_add_btn(ui->BT_page_BT_list, &_bt01_alpha_20x20, "LHC02200000005");
    ui->BT_page_BT_list_item5 = lv_list_add_btn(ui->BT_page_BT_list, &_bt01_alpha_20x20, "LHC02200000006");
    lv_obj_set_pos(ui->BT_page_BT_list, 4, 19);
    lv_obj_set_size(ui->BT_page_BT_list, 156, 142);
    lv_obj_set_scrollbar_mode(ui->BT_page_BT_list, LV_SCROLLBAR_MODE_OFF);

    //Write style state: LV_STATE_DEFAULT for &style_BT_page_BT_list_main_main_default
    static lv_style_t style_BT_page_BT_list_main_main_default;
    ui_init_style(&style_BT_page_BT_list_main_main_default);

    lv_style_set_pad_top(&style_BT_page_BT_list_main_main_default, 2);
    lv_style_set_pad_left(&style_BT_page_BT_list_main_main_default, 0);
    lv_style_set_pad_right(&style_BT_page_BT_list_main_main_default, 0);
    lv_style_set_pad_bottom(&style_BT_page_BT_list_main_main_default, 14);
    lv_style_set_bg_opa(&style_BT_page_BT_list_main_main_default, 236);
    lv_style_set_bg_color(&style_BT_page_BT_list_main_main_default, lv_color_hex(0x19292a));
    lv_style_set_bg_grad_dir(&style_BT_page_BT_list_main_main_default, LV_GRAD_DIR_NONE);
    lv_style_set_border_width(&style_BT_page_BT_list_main_main_default, 2);
    lv_style_set_border_opa(&style_BT_page_BT_list_main_main_default, 255);
    lv_style_set_border_color(&style_BT_page_BT_list_main_main_default, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_BT_page_BT_list_main_main_default, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_BT_page_BT_list_main_main_default, 7);
    lv_style_set_shadow_width(&style_BT_page_BT_list_main_main_default, 0);
    lv_obj_add_style(ui->BT_page_BT_list, &style_BT_page_BT_list_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_BT_page_BT_list_main_scrollbar_default
    static lv_style_t style_BT_page_BT_list_main_scrollbar_default;
    ui_init_style(&style_BT_page_BT_list_main_scrollbar_default);

    lv_style_set_radius(&style_BT_page_BT_list_main_scrollbar_default, 2);
    lv_style_set_bg_opa(&style_BT_page_BT_list_main_scrollbar_default, 255);
    lv_style_set_bg_color(&style_BT_page_BT_list_main_scrollbar_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_BT_page_BT_list_main_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->BT_page_BT_list, &style_BT_page_BT_list_main_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_BT_page_BT_list_extra_btns_main_default
    static lv_style_t style_BT_page_BT_list_extra_btns_main_default;
    ui_init_style(&style_BT_page_BT_list_extra_btns_main_default);

    lv_style_set_pad_top(&style_BT_page_BT_list_extra_btns_main_default, 8);
    lv_style_set_pad_left(&style_BT_page_BT_list_extra_btns_main_default, 10);
    lv_style_set_pad_right(&style_BT_page_BT_list_extra_btns_main_default, 0);
    lv_style_set_pad_bottom(&style_BT_page_BT_list_extra_btns_main_default, 10);
    lv_style_set_border_width(&style_BT_page_BT_list_extra_btns_main_default, 1);
    lv_style_set_border_opa(&style_BT_page_BT_list_extra_btns_main_default, 255);
    lv_style_set_border_color(&style_BT_page_BT_list_extra_btns_main_default, lv_color_hex(0xaaaaaa));
    lv_style_set_border_side(&style_BT_page_BT_list_extra_btns_main_default, LV_BORDER_SIDE_FULL);
    lv_style_set_text_color(&style_BT_page_BT_list_extra_btns_main_default, lv_color_hex(0xfef6ea));
    lv_style_set_text_font(&style_BT_page_BT_list_extra_btns_main_default, &lv_font_HarmonyOS_Sans_SC_Light_12);
    lv_style_set_text_opa(&style_BT_page_BT_list_extra_btns_main_default, 255);
    lv_style_set_radius(&style_BT_page_BT_list_extra_btns_main_default, 0);
    lv_style_set_bg_opa(&style_BT_page_BT_list_extra_btns_main_default, 0);
    lv_obj_add_style(ui->BT_page_BT_list_item5, &style_BT_page_BT_list_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->BT_page_BT_list_item4, &style_BT_page_BT_list_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->BT_page_BT_list_item3, &style_BT_page_BT_list_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->BT_page_BT_list_item2, &style_BT_page_BT_list_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->BT_page_BT_list_item1, &style_BT_page_BT_list_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->BT_page_BT_list_item0, &style_BT_page_BT_list_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_BT_page_BT_list_extra_texts_main_default
    static lv_style_t style_BT_page_BT_list_extra_texts_main_default;
    ui_init_style(&style_BT_page_BT_list_extra_texts_main_default);

    lv_style_set_pad_top(&style_BT_page_BT_list_extra_texts_main_default, 8);
    lv_style_set_pad_left(&style_BT_page_BT_list_extra_texts_main_default, 5);
    lv_style_set_pad_right(&style_BT_page_BT_list_extra_texts_main_default, 0);
    lv_style_set_pad_bottom(&style_BT_page_BT_list_extra_texts_main_default, 0);
    lv_style_set_border_width(&style_BT_page_BT_list_extra_texts_main_default, 0);
    lv_style_set_text_color(&style_BT_page_BT_list_extra_texts_main_default, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_BT_page_BT_list_extra_texts_main_default, &lv_font_HarmonyOS_Sans_SC_Light_12);
    lv_style_set_text_opa(&style_BT_page_BT_list_extra_texts_main_default, 255);
    lv_style_set_radius(&style_BT_page_BT_list_extra_texts_main_default, 2);
    lv_style_set_transform_width(&style_BT_page_BT_list_extra_texts_main_default, 0);
    lv_style_set_bg_opa(&style_BT_page_BT_list_extra_texts_main_default, 0);

    //Write codes BT_page_label_1
    ui->BT_page_label_1 = lv_label_create(ui->BT_page_cont_1);
    lv_label_set_text(ui->BT_page_label_1, "蓝牙设备列表");
    lv_label_set_long_mode(ui->BT_page_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_1, 26, 3);
    lv_obj_set_size(ui->BT_page_label_1, 101, 12);

    //Write style for BT_page_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_1, lv_color_hex(0xfbfbfb), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_1, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_btn_1
    ui->BT_page_btn_1 = lv_btn_create(ui->BT_page_cont_1);
    ui->BT_page_btn_1_label = lv_label_create(ui->BT_page_btn_1);
    lv_label_set_text(ui->BT_page_btn_1_label, "点击搜索蓝牙");
    lv_label_set_long_mode(ui->BT_page_btn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->BT_page_btn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->BT_page_btn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->BT_page_btn_1_label, LV_PCT(100));
    lv_obj_set_pos(ui->BT_page_btn_1, 6, 166);
    lv_obj_set_size(ui->BT_page_btn_1, 61, 21);

    //Write style for BT_page_btn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->BT_page_btn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->BT_page_btn_1, lv_color_hex(0x009ea9), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->BT_page_btn_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->BT_page_btn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_btn_1, 18, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_btn_1, 12, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(ui->BT_page_btn_1, lv_color_hex(0x0d4b3b), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui->BT_page_btn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui->BT_page_btn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui->BT_page_btn_1, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui->BT_page_btn_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_btn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_btn_1, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_btn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_btn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_btn_2
    ui->BT_page_btn_2 = lv_btn_create(ui->BT_page_cont_1);
    ui->BT_page_btn_2_label = lv_label_create(ui->BT_page_btn_2);
    lv_label_set_text(ui->BT_page_btn_2_label, "打印二维码");
    lv_label_set_long_mode(ui->BT_page_btn_2_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->BT_page_btn_2_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->BT_page_btn_2, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->BT_page_btn_2_label, LV_PCT(100));
    lv_obj_set_pos(ui->BT_page_btn_2, 78, 167);
    lv_obj_set_size(ui->BT_page_btn_2, 61, 21);

    //Write style for BT_page_btn_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->BT_page_btn_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->BT_page_btn_2, lv_color_hex(0x009ea9), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->BT_page_btn_2, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->BT_page_btn_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_btn_2, 18, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_btn_2, 12, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(ui->BT_page_btn_2, lv_color_hex(0x0d4b3b), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(ui->BT_page_btn_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(ui->BT_page_btn_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(ui->BT_page_btn_2, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(ui->BT_page_btn_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_btn_2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_btn_2, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_btn_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_btn_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_cont_2
    ui->BT_page_cont_2 = lv_obj_create(ui->BT_page);
    lv_obj_set_pos(ui->BT_page_cont_2, 188, 107);
    lv_obj_set_size(ui->BT_page_cont_2, 124, 128);
    lv_obj_set_scrollbar_mode(ui->BT_page_cont_2, LV_SCROLLBAR_MODE_OFF);

    //Write style for BT_page_cont_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_cont_2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->BT_page_cont_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->BT_page_cont_2, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->BT_page_cont_2, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_cont_2, 7, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_cont_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->BT_page_cont_2, lv_color_hex(0x78B6C2), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->BT_page_cont_2, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_cont_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_label_2
    ui->BT_page_label_2 = lv_label_create(ui->BT_page_cont_2);
    lv_label_set_text(ui->BT_page_label_2, "扫码连接蓝牙");
    lv_label_set_long_mode(ui->BT_page_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_2, 23, 1);
    lv_obj_set_size(ui->BT_page_label_2, 80, 14);

    //Write style for BT_page_label_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_2, lv_color_hex(0x359d60), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_2, &lv_font_HarmonyOS_Sans_SC_Light_12, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_2, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_2, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_label_3
    ui->BT_page_label_3 = lv_label_create(ui->BT_page_cont_2);
    lv_label_set_text(ui->BT_page_label_3, "C02200000001");
    lv_label_set_long_mode(ui->BT_page_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_3, 9, 105);
    lv_obj_set_size(ui->BT_page_label_3, 114, 13);

    //Write style for BT_page_label_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_3, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_3, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_label_4
    ui->BT_page_label_4 = lv_label_create(ui->BT_page);
    lv_label_set_text(ui->BT_page_label_4, "蓝牙模组测试");
    lv_label_set_long_mode(ui->BT_page_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_4, 99, 9);
    lv_obj_set_size(ui->BT_page_label_4, 122, 24);

    //Write style for BT_page_label_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_4, lv_color_hex(0x08a271), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_4, &lv_font_HarmonyOS_Sans_SC_Light_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_4, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_cont_3
    ui->BT_page_cont_3 = lv_obj_create(ui->BT_page);
    lv_obj_set_pos(ui->BT_page_cont_3, 188, 33);
    lv_obj_set_size(ui->BT_page_cont_3, 122, 67);
    lv_obj_set_scrollbar_mode(ui->BT_page_cont_3, LV_SCROLLBAR_MODE_OFF);

    //Write style for BT_page_cont_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_cont_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->BT_page_cont_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->BT_page_cont_3, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->BT_page_cont_3, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_cont_3, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_cont_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->BT_page_cont_3, lv_color_hex(0x93BCC7), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->BT_page_cont_3, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_cont_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_label_5
    ui->BT_page_label_5 = lv_label_create(ui->BT_page_cont_3);
    lv_label_set_text(ui->BT_page_label_5, "测试结果");
    lv_label_set_long_mode(ui->BT_page_label_5, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_5, 28, 3);
    lv_obj_set_size(ui->BT_page_label_5, 66, 12);

    //Write style for BT_page_label_5, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_5, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_5, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_5, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_5, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_5, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_5, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_label_6
    ui->BT_page_label_6 = lv_label_create(ui->BT_page_cont_3);
    lv_label_set_text(ui->BT_page_label_6, "当前测试ID:0");
    lv_label_set_long_mode(ui->BT_page_label_6, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_6, 4, 23);
    lv_obj_set_size(ui->BT_page_label_6, 90, 15);

    //Write style for BT_page_label_6, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_6, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_6, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_6, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_6, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_6, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_6, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_label_7
    ui->BT_page_label_7 = lv_label_create(ui->BT_page_cont_3);
    lv_label_set_text(ui->BT_page_label_7, "测试输出：未测试");
    lv_label_set_long_mode(ui->BT_page_label_7, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->BT_page_label_7, 2, 47);
    lv_obj_set_size(ui->BT_page_label_7, 98, 12);

    //Write style for BT_page_label_7, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->BT_page_label_7, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_label_7, &lv_font_HarmonyOS_Sans_SC_Light_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_label_7, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->BT_page_label_7, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_label_7, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_label_7, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes BT_page_imgbtn_1
    ui->BT_page_imgbtn_1 = lv_imgbtn_create(ui->BT_page);
    lv_obj_add_flag(ui->BT_page_imgbtn_1, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->BT_page_imgbtn_1, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_26x26, NULL);
    ui->BT_page_imgbtn_1_label = lv_label_create(ui->BT_page_imgbtn_1);
    lv_label_set_text(ui->BT_page_imgbtn_1_label, "");
    lv_label_set_long_mode(ui->BT_page_imgbtn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->BT_page_imgbtn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->BT_page_imgbtn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->BT_page_imgbtn_1, 4, 5);
    lv_obj_set_size(ui->BT_page_imgbtn_1, 26, 26);

    //Write style for BT_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->BT_page_imgbtn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->BT_page_imgbtn_1, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->BT_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->BT_page_imgbtn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->BT_page_imgbtn_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for BT_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->BT_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->BT_page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->BT_page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->BT_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for BT_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->BT_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->BT_page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->BT_page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->BT_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for BT_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->BT_page_imgbtn_1, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->BT_page_imgbtn_1, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //The custom code of BT_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->BT_page);

    //Init events for screen.
    // events_init_BT_page(ui);
}
