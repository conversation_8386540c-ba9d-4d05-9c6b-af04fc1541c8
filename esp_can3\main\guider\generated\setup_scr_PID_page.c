/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_PID_page(lv_ui *ui)
{
    //Write codes PID_page
    ui->PID_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->PID_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->PID_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for PID_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui->PID_page, &_bg01_320x240, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(ui->PID_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_recolor_opa(ui->PID_page, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_Curren_loop
    ui->PID_page_Curren_loop = lv_obj_create(ui->PID_page);
    lv_obj_set_pos(ui->PID_page_Curren_loop, 1, 32);
    lv_obj_set_size(ui->PID_page_Curren_loop, 316, 66);
    lv_obj_set_scrollbar_mode(ui->PID_page_Curren_loop, LV_SCROLLBAR_MODE_OFF);

    //Write style for PID_page_Curren_loop, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_Curren_loop, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_Curren_loop, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_Curren_loop, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_Curren_loop, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_Curren_loop, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_Curren_loop, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_Curren_loop, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_Curren_loop, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_Curren_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_Curren_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_Curren_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_Curren_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_Curren_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_3
    ui->PID_page_label_3 = lv_label_create(ui->PID_page_Curren_loop);
    lv_label_set_text(ui->PID_page_label_3, "电流环");
    lv_label_set_long_mode(ui->PID_page_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_3, 118, 5);
    lv_obj_set_size(ui->PID_page_label_3, 70, 10);

    //Write style for PID_page_label_3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_3, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_3, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kp1
    ui->PID_page_label_kp1 = lv_label_create(ui->PID_page_Curren_loop);
    lv_label_set_text(ui->PID_page_label_kp1, "KP：");
    lv_label_set_long_mode(ui->PID_page_label_kp1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kp1, 2, 23);
    lv_obj_set_size(ui->PID_page_label_kp1, 36, 10);

    //Write style for PID_page_label_kp1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kp1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kp1, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kp1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kp1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kp1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kp1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_ki1
    ui->PID_page_label_ki1 = lv_label_create(ui->PID_page_Curren_loop);
    lv_label_set_text(ui->PID_page_label_ki1, "KI：");
    lv_label_set_long_mode(ui->PID_page_label_ki1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_ki1, 1, 43);
    lv_obj_set_size(ui->PID_page_label_ki1, 36, 17);

    //Write style for PID_page_label_ki1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_ki1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_ki1, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_ki1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_ki1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_ki1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_ki1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_slider_kcp
    ui->PID_page_slider_kcp = lv_slider_create(ui->PID_page_Curren_loop);
    lv_slider_set_range(ui->PID_page_slider_kcp, 0, 100);
    lv_slider_set_mode(ui->PID_page_slider_kcp, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->PID_page_slider_kcp, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->PID_page_slider_kcp, 34, 24);
    lv_obj_set_size(ui->PID_page_slider_kcp, 200, 7);

    //Write style for PID_page_slider_kcp, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kcp, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kcp, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kcp, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kcp, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->PID_page_slider_kcp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_slider_kcp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kcp, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kcp, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kcp, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kcp, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kcp, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kcp, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kcp, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kcp, lv_color_hex(0x2195f6), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kcp, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kcp, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kcp
    ui->PID_page_label_kcp = lv_label_create(ui->PID_page_Curren_loop);
    lv_label_set_text(ui->PID_page_label_kcp, "0");
    lv_label_set_long_mode(ui->PID_page_label_kcp, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kcp, 243, 18);
    lv_obj_set_size(ui->PID_page_label_kcp, 68, 17);

    //Write style for PID_page_label_kcp, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kcp, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_label_kcp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_label_kcp, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_label_kcp, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kcp, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kcp, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kcp, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kcp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kcp, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kcp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kcp, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kcp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_label_kcp, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_label_kcp, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kcp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kcp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kcp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kcp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kcp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_slider_kci
    ui->PID_page_slider_kci = lv_slider_create(ui->PID_page_Curren_loop);
    lv_slider_set_range(ui->PID_page_slider_kci, 0, 100);
    lv_slider_set_mode(ui->PID_page_slider_kci, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->PID_page_slider_kci, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->PID_page_slider_kci, 34, 48);
    lv_obj_set_size(ui->PID_page_slider_kci, 200, 7);

    //Write style for PID_page_slider_kci, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kci, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kci, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kci, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kci, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->PID_page_slider_kci, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_slider_kci, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kci, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kci, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kci, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kci, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kci, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kci, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kci, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kci, lv_color_hex(0x2195f6), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kci, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kci, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes PID_page_title_bar
    ui->PID_page_title_bar = lv_obj_create(ui->PID_page);
    lv_obj_set_pos(ui->PID_page_title_bar, 40, 3);
    lv_obj_set_size(ui->PID_page_title_bar, 111, 23);
    lv_obj_set_scrollbar_mode(ui->PID_page_title_bar, LV_SCROLLBAR_MODE_OFF);

    //Write style for PID_page_title_bar, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_title_bar, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_title_bar, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_title_bar, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_title_bar, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_title_bar, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_title_bar, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_title_bar, lv_color_hex(0x7C997F), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_title_bar, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_title_bar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_4
    ui->PID_page_label_4 = lv_label_create(ui->PID_page_title_bar);
    lv_label_set_text(ui->PID_page_label_4, "PID参数");
    lv_label_set_long_mode(ui->PID_page_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_4, 12, 2);
    lv_obj_set_size(ui->PID_page_label_4, 83, 16);

    //Write style for PID_page_label_4, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_4, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_4, &lv_font_HarmonyOS_Sans_SC_Light_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_4, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_imgbtn_1
    ui->PID_page_imgbtn_1 = lv_imgbtn_create(ui->PID_page);
    lv_obj_add_flag(ui->PID_page_imgbtn_1, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->PID_page_imgbtn_1, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_29x26, NULL);
    ui->PID_page_imgbtn_1_label = lv_label_create(ui->PID_page_imgbtn_1);
    lv_label_set_text(ui->PID_page_imgbtn_1_label, "");
    lv_label_set_long_mode(ui->PID_page_imgbtn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->PID_page_imgbtn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->PID_page_imgbtn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->PID_page_imgbtn_1, 4, 3);
    lv_obj_set_size(ui->PID_page_imgbtn_1, 29, 26);

    //Write style for PID_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->PID_page_imgbtn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_imgbtn_1, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_imgbtn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->PID_page_imgbtn_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->PID_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->PID_page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->PID_page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->PID_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for PID_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->PID_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->PID_page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->PID_page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->PID_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for PID_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->PID_page_imgbtn_1, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->PID_page_imgbtn_1, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //Write codes PID_page_label_kci
    ui->PID_page_label_kci = lv_label_create(ui->PID_page);
    lv_label_set_text(ui->PID_page_label_kci, "0");
    lv_label_set_long_mode(ui->PID_page_label_kci, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kci, 245, 74);
    lv_obj_set_size(ui->PID_page_label_kci, 68, 17);

    //Write style for PID_page_label_kci, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kci, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_label_kci, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_label_kci, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_label_kci, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kci, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kci, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kci, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kci, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kci, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kci, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kci, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kci, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_label_kci, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_label_kci, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kci, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kci, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kci, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kci, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kci, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_Speed_loop
    ui->PID_page_Speed_loop = lv_obj_create(ui->PID_page);
    lv_obj_set_pos(ui->PID_page_Speed_loop, 1, 101);
    lv_obj_set_size(ui->PID_page_Speed_loop, 316, 66);
    lv_obj_set_scrollbar_mode(ui->PID_page_Speed_loop, LV_SCROLLBAR_MODE_OFF);

    //Write style for PID_page_Speed_loop, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_Speed_loop, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_Speed_loop, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_Speed_loop, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_Speed_loop, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_Speed_loop, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_Speed_loop, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_Speed_loop, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_Speed_loop, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_Speed_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_Speed_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_Speed_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_Speed_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_Speed_loop, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_40
    ui->PID_page_label_40 = lv_label_create(ui->PID_page_Speed_loop);
    lv_label_set_text(ui->PID_page_label_40, "速度环");
    lv_label_set_long_mode(ui->PID_page_label_40, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_40, 118, 5);
    lv_obj_set_size(ui->PID_page_label_40, 70, 10);

    //Write style for PID_page_label_40, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_40, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_40, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_40, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_40, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_40, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_40, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kp2
    ui->PID_page_label_kp2 = lv_label_create(ui->PID_page_Speed_loop);
    lv_label_set_text(ui->PID_page_label_kp2, "KP：");
    lv_label_set_long_mode(ui->PID_page_label_kp2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kp2, 2, 23);
    lv_obj_set_size(ui->PID_page_label_kp2, 36, 10);

    //Write style for PID_page_label_kp2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kp2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kp2, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kp2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kp2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kp2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kp2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_ki2
    ui->PID_page_label_ki2 = lv_label_create(ui->PID_page_Speed_loop);
    lv_label_set_text(ui->PID_page_label_ki2, "KI：");
    lv_label_set_long_mode(ui->PID_page_label_ki2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_ki2, 1, 43);
    lv_obj_set_size(ui->PID_page_label_ki2, 36, 17);

    //Write style for PID_page_label_ki2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_ki2, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_ki2, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_ki2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_ki2, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_ki2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_ki2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_slider_kc
    ui->PID_page_slider_kc = lv_slider_create(ui->PID_page_Speed_loop);
    lv_slider_set_range(ui->PID_page_slider_kc, 0, 100);
    lv_slider_set_mode(ui->PID_page_slider_kc, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->PID_page_slider_kc, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->PID_page_slider_kc, 34, 24);
    lv_obj_set_size(ui->PID_page_slider_kc, 200, 7);

    //Write style for PID_page_slider_kc, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kc, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kc, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kc, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kc, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->PID_page_slider_kc, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_slider_kc, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kc, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kc, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kc, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kc, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kc, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kc, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kc, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kc, lv_color_hex(0x2195f6), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kc, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kc, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kvp
    ui->PID_page_label_kvp = lv_label_create(ui->PID_page_Speed_loop);
    lv_label_set_text(ui->PID_page_label_kvp, "10A");
    lv_label_set_long_mode(ui->PID_page_label_kvp, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kvp, 242, 19);
    lv_obj_set_size(ui->PID_page_label_kvp, 68, 19);

    //Write style for PID_page_label_kvp, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kvp, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_label_kvp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_label_kvp, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_label_kvp, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kvp, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kvp, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kvp, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kvp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kvp, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kvp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kvp, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kvp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_label_kvp, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_label_kvp, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kvp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kvp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kvp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kvp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kvp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_slider_kvi
    ui->PID_page_slider_kvi = lv_slider_create(ui->PID_page_Speed_loop);
    lv_slider_set_range(ui->PID_page_slider_kvi, 0, 100);
    lv_slider_set_mode(ui->PID_page_slider_kvi, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->PID_page_slider_kvi, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->PID_page_slider_kvi, 34, 48);
    lv_obj_set_size(ui->PID_page_slider_kvi, 200, 7);

    //Write style for PID_page_slider_kvi, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kvi, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kvi, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kvi, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kvi, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->PID_page_slider_kvi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_slider_kvi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kvi, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kvi, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kvi, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kvi, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kvi, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kvi, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kvi, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kvi, lv_color_hex(0x2195f6), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kvi, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kvi, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kvi
    ui->PID_page_label_kvi = lv_label_create(ui->PID_page_Speed_loop);
    lv_label_set_text(ui->PID_page_label_kvi, "10A");
    lv_label_set_long_mode(ui->PID_page_label_kvi, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kvi, 243, 42);
    lv_obj_set_size(ui->PID_page_label_kvi, 65, 19);

    //Write style for PID_page_label_kvi, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kvi, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_label_kvi, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_label_kvi, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_label_kvi, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kvi, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kvi, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kvi, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kvi, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kvi, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kvi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kvi, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kvi, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_label_kvi, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_label_kvi, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kvi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kvi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kvi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kvi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kvi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_cont_1
    ui->PID_page_cont_1 = lv_obj_create(ui->PID_page);
    lv_obj_set_pos(ui->PID_page_cont_1, 1, 171);
    lv_obj_set_size(ui->PID_page_cont_1, 316, 66);
    lv_obj_set_scrollbar_mode(ui->PID_page_cont_1, LV_SCROLLBAR_MODE_OFF);

    //Write style for PID_page_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_cont_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_cont_1, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_cont_1, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_cont_1, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_cont_1, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_cont_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_44
    ui->PID_page_label_44 = lv_label_create(ui->PID_page_cont_1);
    lv_label_set_text(ui->PID_page_label_44, "位置环");
    lv_label_set_long_mode(ui->PID_page_label_44, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_44, 118, 7);
    lv_obj_set_size(ui->PID_page_label_44, 70, 10);

    //Write style for PID_page_label_44, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_44, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_44, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_44, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_44, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_44, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_44, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kp3
    ui->PID_page_label_kp3 = lv_label_create(ui->PID_page_cont_1);
    lv_label_set_text(ui->PID_page_label_kp3, "KP：");
    lv_label_set_long_mode(ui->PID_page_label_kp3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kp3, 2, 23);
    lv_obj_set_size(ui->PID_page_label_kp3, 36, 10);

    //Write style for PID_page_label_kp3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kp3, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kp3, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kp3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kp3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kp3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kp3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_label_ki3
    ui->PID_page_label_ki3 = lv_label_create(ui->PID_page_cont_1);
    lv_label_set_text(ui->PID_page_label_ki3, "KI：");
    lv_label_set_long_mode(ui->PID_page_label_ki3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_ki3, 1, 43);
    lv_obj_set_size(ui->PID_page_label_ki3, 36, 17);

    //Write style for PID_page_label_ki3, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_ki3, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_ki3, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_ki3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_ki3, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_ki3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_ki3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_slider_kpp
    ui->PID_page_slider_kpp = lv_slider_create(ui->PID_page_cont_1);
    lv_slider_set_range(ui->PID_page_slider_kpp, 0, 100);
    lv_slider_set_mode(ui->PID_page_slider_kpp, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->PID_page_slider_kpp, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->PID_page_slider_kpp, 34, 24);
    lv_obj_set_size(ui->PID_page_slider_kpp, 200, 7);

    //Write style for PID_page_slider_kpp, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kpp, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kpp, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kpp, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kpp, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->PID_page_slider_kpp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_slider_kpp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kpp, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kpp, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kpp, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kpp, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kpp, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kpp, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kpp, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kpp, lv_color_hex(0x2195f6), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kpp, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kpp, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kpp
    ui->PID_page_label_kpp = lv_label_create(ui->PID_page_cont_1);
    lv_label_set_text(ui->PID_page_label_kpp, "10A");
    lv_label_set_long_mode(ui->PID_page_label_kpp, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kpp, 241, 19);
    lv_obj_set_size(ui->PID_page_label_kpp, 68, 19);

    //Write style for PID_page_label_kpp, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kpp, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_label_kpp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_label_kpp, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_label_kpp, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kpp, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kpp, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kpp, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kpp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kpp, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kpp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kpp, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kpp, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_label_kpp, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_label_kpp, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kpp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kpp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kpp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kpp, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kpp, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_slider_kpi
    ui->PID_page_slider_kpi = lv_slider_create(ui->PID_page_cont_1);
    lv_slider_set_range(ui->PID_page_slider_kpi, 0, 100);
    lv_slider_set_mode(ui->PID_page_slider_kpi, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->PID_page_slider_kpi, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->PID_page_slider_kpi, 34, 48);
    lv_obj_set_size(ui->PID_page_slider_kpi, 200, 7);

    //Write style for PID_page_slider_kpi, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kpi, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kpi, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kpi, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kpi, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->PID_page_slider_kpi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_slider_kpi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kpi, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kpi, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kpi, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kpi, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kpi, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for PID_page_slider_kpi, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_slider_kpi, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_slider_kpi, lv_color_hex(0x2195f6), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_slider_kpi, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_slider_kpi, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes PID_page_label_kpi
    ui->PID_page_label_kpi = lv_label_create(ui->PID_page_cont_1);
    lv_label_set_text(ui->PID_page_label_kpi, "10A");
    lv_label_set_long_mode(ui->PID_page_label_kpi, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->PID_page_label_kpi, 241, 42);
    lv_obj_set_size(ui->PID_page_label_kpi, 68, 19);

    //Write style for PID_page_label_kpi, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->PID_page_label_kpi, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_label_kpi, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_label_kpi, lv_color_hex(0xe2e0e0), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_label_kpi, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_label_kpi, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_label_kpi, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_label_kpi, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_label_kpi, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->PID_page_label_kpi, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->PID_page_label_kpi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_label_kpi, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_label_kpi, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_label_kpi, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_label_kpi, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_label_kpi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_label_kpi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->PID_page_label_kpi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_label_kpi, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_label_kpi, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_btn_write
    ui->PID_page_btn_write = lv_btn_create(ui->PID_page);
    ui->PID_page_btn_write_label = lv_label_create(ui->PID_page_btn_write);
    lv_label_set_text(ui->PID_page_btn_write_label, "参数写入");
    lv_label_set_long_mode(ui->PID_page_btn_write_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->PID_page_btn_write_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->PID_page_btn_write, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->PID_page_btn_write_label, LV_PCT(100));
    lv_obj_set_pos(ui->PID_page_btn_write, 237, 4);
    lv_obj_set_size(ui->PID_page_btn_write, 75, 24);

    //Write style for PID_page_btn_write, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->PID_page_btn_write, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_btn_write, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_btn_write, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->PID_page_btn_write, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_btn_write, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_btn_write, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->PID_page_btn_write, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_btn_write, &lv_font_HarmonyOS_Sans_SC_Medium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_btn_write, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->PID_page_btn_write, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes PID_page_ddlist_choose
    ui->PID_page_ddlist_choose = lv_dropdown_create(ui->PID_page);
    lv_dropdown_set_options(ui->PID_page_ddlist_choose, "电机1\n电机2");
    lv_obj_set_pos(ui->PID_page_ddlist_choose, 156, 3);
    lv_obj_set_size(ui->PID_page_ddlist_choose, 71, 27);

    //Write style for PID_page_ddlist_choose, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->PID_page_ddlist_choose, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->PID_page_ddlist_choose, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->PID_page_ddlist_choose, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->PID_page_ddlist_choose, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->PID_page_ddlist_choose, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->PID_page_ddlist_choose, lv_color_hex(0xb9b4b4), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->PID_page_ddlist_choose, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->PID_page_ddlist_choose, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->PID_page_ddlist_choose, 6, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->PID_page_ddlist_choose, 6, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->PID_page_ddlist_choose, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->PID_page_ddlist_choose, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->PID_page_ddlist_choose, lv_color_hex(0x3D324A), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->PID_page_ddlist_choose, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->PID_page_ddlist_choose, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_CHECKED for &style_PID_page_ddlist_choose_extra_list_selected_checked
    static lv_style_t style_PID_page_ddlist_choose_extra_list_selected_checked;
    ui_init_style(&style_PID_page_ddlist_choose_extra_list_selected_checked);

    lv_style_set_border_width(&style_PID_page_ddlist_choose_extra_list_selected_checked, 1);
    lv_style_set_border_opa(&style_PID_page_ddlist_choose_extra_list_selected_checked, 255);
    lv_style_set_border_color(&style_PID_page_ddlist_choose_extra_list_selected_checked, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_PID_page_ddlist_choose_extra_list_selected_checked, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_PID_page_ddlist_choose_extra_list_selected_checked, 2);
    lv_style_set_bg_opa(&style_PID_page_ddlist_choose_extra_list_selected_checked, 255);
    lv_style_set_bg_color(&style_PID_page_ddlist_choose_extra_list_selected_checked, lv_color_hex(0x00a1b5));
    lv_style_set_bg_grad_dir(&style_PID_page_ddlist_choose_extra_list_selected_checked, LV_GRAD_DIR_NONE);
    lv_obj_add_style(lv_dropdown_get_list(ui->PID_page_ddlist_choose), &style_PID_page_ddlist_choose_extra_list_selected_checked, LV_PART_SELECTED|LV_STATE_CHECKED);

    //Write style state: LV_STATE_DEFAULT for &style_PID_page_ddlist_choose_extra_list_main_default
    static lv_style_t style_PID_page_ddlist_choose_extra_list_main_default;
    ui_init_style(&style_PID_page_ddlist_choose_extra_list_main_default);

    lv_style_set_max_height(&style_PID_page_ddlist_choose_extra_list_main_default, 180);
    lv_style_set_text_color(&style_PID_page_ddlist_choose_extra_list_main_default, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_PID_page_ddlist_choose_extra_list_main_default, &lv_font_HarmonyOS_Sans_SC_Medium_10);
    lv_style_set_text_opa(&style_PID_page_ddlist_choose_extra_list_main_default, 255);
    lv_style_set_border_width(&style_PID_page_ddlist_choose_extra_list_main_default, 1);
    lv_style_set_border_opa(&style_PID_page_ddlist_choose_extra_list_main_default, 255);
    lv_style_set_border_color(&style_PID_page_ddlist_choose_extra_list_main_default, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_PID_page_ddlist_choose_extra_list_main_default, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_PID_page_ddlist_choose_extra_list_main_default, 2);
    lv_style_set_bg_opa(&style_PID_page_ddlist_choose_extra_list_main_default, 255);
    lv_style_set_bg_color(&style_PID_page_ddlist_choose_extra_list_main_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_PID_page_ddlist_choose_extra_list_main_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(lv_dropdown_get_list(ui->PID_page_ddlist_choose), &style_PID_page_ddlist_choose_extra_list_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_PID_page_ddlist_choose_extra_list_scrollbar_default
    static lv_style_t style_PID_page_ddlist_choose_extra_list_scrollbar_default;
    ui_init_style(&style_PID_page_ddlist_choose_extra_list_scrollbar_default);

    lv_style_set_radius(&style_PID_page_ddlist_choose_extra_list_scrollbar_default, 2);
    lv_style_set_bg_opa(&style_PID_page_ddlist_choose_extra_list_scrollbar_default, 255);
    lv_style_set_bg_color(&style_PID_page_ddlist_choose_extra_list_scrollbar_default, lv_color_hex(0x00ff00));
    lv_style_set_bg_grad_dir(&style_PID_page_ddlist_choose_extra_list_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(lv_dropdown_get_list(ui->PID_page_ddlist_choose), &style_PID_page_ddlist_choose_extra_list_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //The custom code of PID_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->PID_page);

    //Init events for screen.
    // events_init_PID_page(ui);
}
