/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_Start_Page(lv_ui *ui)
{
    //Write codes Start_Page
    ui->Start_Page = lv_obj_create(NULL);
    lv_obj_set_size(ui->Start_Page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->Start_Page, LV_SCROLLBAR_MODE_OFF);

    //Write style for Start_Page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Start_Page, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui->Start_Page, &_Start_bg_320x240, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(ui->Start_Page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_recolor_opa(ui->Start_Page, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Start_Page_ProgressBar
    ui->Start_Page_ProgressBar = lv_bar_create(ui->Start_Page);
    lv_obj_set_style_anim_time(ui->Start_Page_ProgressBar, 5000, 0);
    lv_bar_set_mode(ui->Start_Page_ProgressBar, LV_BAR_MODE_SYMMETRICAL);
    lv_bar_set_range(ui->Start_Page_ProgressBar, 0, 100);
    lv_bar_set_value(ui->Start_Page_ProgressBar, 100, LV_ANIM_ON);
    lv_obj_set_pos(ui->Start_Page_ProgressBar, 32, 212);
    lv_obj_set_size(ui->Start_Page_ProgressBar, 256, 20);

    //Write style for Start_Page_ProgressBar, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Start_Page_ProgressBar, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Start_Page_ProgressBar, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Start_Page_ProgressBar, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Start_Page_ProgressBar, 7, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Start_Page_ProgressBar, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Start_Page_ProgressBar, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Start_Page_ProgressBar, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Start_Page_ProgressBar, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Start_Page_ProgressBar, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Start_Page_ProgressBar, 7, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write codes Start_Page_textView
    ui->Start_Page_textView = lv_label_create(ui->Start_Page);
    lv_label_set_text(ui->Start_Page_textView, "系统加载中......");
    lv_label_set_long_mode(ui->Start_Page_textView, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->Start_Page_textView, 26, 191);
    lv_obj_set_size(ui->Start_Page_textView, 100, 13);

    //Write style for Start_Page_textView, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Start_Page_textView, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Start_Page_textView, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Start_Page_textView, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->Start_Page_textView, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Start_Page_textView, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Start_Page_textView, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of Start_Page.


    //Update current screen layout.
    // lv_obj_update_layout(ui->Start_Page);

}
