/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_ai_page(lv_ui *ui)
{
    //Write codes ai_page
    ui->ai_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->ai_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->ai_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for ai_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->ai_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->ai_page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->ai_page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes ai_page_label_assistant
    ui->ai_page_label_assistant = lv_label_create(ui->ai_page);
    lv_label_set_text(ui->ai_page_label_assistant, "");
    lv_label_set_long_mode(ui->ai_page_label_assistant, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->ai_page_label_assistant, 10, 94);
    lv_obj_set_size(ui->ai_page_label_assistant, 300, 100);

    //Write style for ai_page_label_assistant, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->ai_page_label_assistant, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->ai_page_label_assistant, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->ai_page_label_assistant, lv_color_hex(0x2FCADA), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->ai_page_label_assistant, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->ai_page_label_assistant, lv_color_hex(0x00ff08), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->ai_page_label_assistant, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->ai_page_label_assistant, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->ai_page_label_assistant, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->ai_page_label_assistant, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes ai_page_img_emoji
    ui->ai_page_img_emoji = lv_img_create(ui->ai_page);
    lv_obj_add_flag(ui->ai_page_img_emoji, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->ai_page_img_emoji, &_neutral_48_alpha_48x48);
    lv_img_set_pivot(ui->ai_page_img_emoji, 50,50);
    lv_img_set_angle(ui->ai_page_img_emoji, 0);
    lv_obj_set_pos(ui->ai_page_img_emoji, 136, 40);
    lv_obj_set_size(ui->ai_page_img_emoji, 48, 48);

    //Write style for ai_page_img_emoji, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->ai_page_img_emoji, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->ai_page_img_emoji, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->ai_page_img_emoji, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->ai_page_img_emoji, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes ai_page_label_state
    ui->ai_page_label_state = lv_label_create(ui->ai_page);
    lv_label_set_text(ui->ai_page_label_state, "请稍后");
    lv_label_set_long_mode(ui->ai_page_label_state, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->ai_page_label_state, 1, 2);
    lv_obj_set_size(ui->ai_page_label_state, 312, 32);

    //Write style for ai_page_label_state, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->ai_page_label_state, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->ai_page_label_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->ai_page_label_state, lv_color_hex(0x2FDAAE), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->ai_page_label_state, LV_BORDER_SIDE_BOTTOM, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->ai_page_label_state, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->ai_page_label_state, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->ai_page_label_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->ai_page_label_state, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->ai_page_label_state, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->ai_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes ai_page_label_user
    ui->ai_page_label_user = lv_label_create(ui->ai_page);
    lv_label_set_text(ui->ai_page_label_user, "");
    lv_label_set_long_mode(ui->ai_page_label_user, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->ai_page_label_user, 1, 206);
    lv_obj_set_size(ui->ai_page_label_user, 318, 32);

    //Write style for ai_page_label_user, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->ai_page_label_user, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->ai_page_label_user, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->ai_page_label_user, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->ai_page_label_user, LV_BORDER_SIDE_TOP, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->ai_page_label_user, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->ai_page_label_user, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->ai_page_label_user, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->ai_page_label_user, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->ai_page_label_user, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of ai_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->ai_page);

}
