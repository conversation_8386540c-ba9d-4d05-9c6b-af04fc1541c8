/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_devoce_info_page(lv_ui *ui)
{
    //Write codes devoce_info_page
    ui->devoce_info_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->devoce_info_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->devoce_info_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for devoce_info_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->devoce_info_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->devoce_info_page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->devoce_info_page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes devoce_info_page_imgbtn_1
    ui->devoce_info_page_imgbtn_1 = lv_imgbtn_create(ui->devoce_info_page);
    lv_obj_add_flag(ui->devoce_info_page_imgbtn_1, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->devoce_info_page_imgbtn_1, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_26x26, NULL);
    ui->devoce_info_page_imgbtn_1_label = lv_label_create(ui->devoce_info_page_imgbtn_1);
    lv_label_set_text(ui->devoce_info_page_imgbtn_1_label, "");
    lv_label_set_long_mode(ui->devoce_info_page_imgbtn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->devoce_info_page_imgbtn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->devoce_info_page_imgbtn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->devoce_info_page_imgbtn_1, 4, 5);
    lv_obj_set_size(ui->devoce_info_page_imgbtn_1, 26, 26);

    //Write style for devoce_info_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->devoce_info_page_imgbtn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->devoce_info_page_imgbtn_1, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->devoce_info_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->devoce_info_page_imgbtn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->devoce_info_page_imgbtn_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for devoce_info_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->devoce_info_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->devoce_info_page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->devoce_info_page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->devoce_info_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for devoce_info_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->devoce_info_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->devoce_info_page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->devoce_info_page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->devoce_info_page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for devoce_info_page_imgbtn_1, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->devoce_info_page_imgbtn_1, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->devoce_info_page_imgbtn_1, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //Write codes devoce_info_page_cont_1
    ui->devoce_info_page_cont_1 = lv_obj_create(ui->devoce_info_page);
    lv_obj_set_pos(ui->devoce_info_page_cont_1, 94, 10);
    lv_obj_set_size(ui->devoce_info_page_cont_1, 140, 34);
    lv_obj_set_scrollbar_mode(ui->devoce_info_page_cont_1, LV_SCROLLBAR_MODE_OFF);

    //Write style for devoce_info_page_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->devoce_info_page_cont_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->devoce_info_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->devoce_info_page_cont_1, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->devoce_info_page_cont_1, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_cont_1, 9, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->devoce_info_page_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->devoce_info_page_cont_1, lv_color_hex(0x3c3c3c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->devoce_info_page_cont_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->devoce_info_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->devoce_info_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->devoce_info_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->devoce_info_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes devoce_info_page_label_23
    ui->devoce_info_page_label_23 = lv_label_create(ui->devoce_info_page_cont_1);
    lv_label_set_text(ui->devoce_info_page_label_23, "设备信息");
    lv_label_set_long_mode(ui->devoce_info_page_label_23, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->devoce_info_page_label_23, 9, 5);
    lv_obj_set_size(ui->devoce_info_page_label_23, 118, 18);

    //Write style for devoce_info_page_label_23, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->devoce_info_page_label_23, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->devoce_info_page_label_23, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->devoce_info_page_label_23, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->devoce_info_page_label_23, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->devoce_info_page_label_23, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_label_23, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes devoce_info_page_label_24
    ui->devoce_info_page_label_24 = lv_label_create(ui->devoce_info_page);
    lv_label_set_text(ui->devoce_info_page_label_24, "主控芯片：");
    lv_label_set_long_mode(ui->devoce_info_page_label_24, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->devoce_info_page_label_24, 9, 57);
    lv_obj_set_size(ui->devoce_info_page_label_24, 89, 26);

    //Write style for devoce_info_page_label_24, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->devoce_info_page_label_24, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->devoce_info_page_label_24, &lv_font_HarmonyOS_Sans_SC_Medium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->devoce_info_page_label_24, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->devoce_info_page_label_24, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->devoce_info_page_label_24, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_label_24, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes devoce_info_page_label_25
    ui->devoce_info_page_label_25 = lv_label_create(ui->devoce_info_page);
    lv_label_set_text(ui->devoce_info_page_label_25, "ESP32S3");
    lv_label_set_long_mode(ui->devoce_info_page_label_25, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->devoce_info_page_label_25, 136, 57);
    lv_obj_set_size(ui->devoce_info_page_label_25, 84, 22);

    //Write style for devoce_info_page_label_25, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->devoce_info_page_label_25, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->devoce_info_page_label_25, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->devoce_info_page_label_25, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->devoce_info_page_label_25, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->devoce_info_page_label_25, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_label_25, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes devoce_info_page_label_26
    ui->devoce_info_page_label_26 = lv_label_create(ui->devoce_info_page);
    lv_label_set_text(ui->devoce_info_page_label_26, "固件版本：");
    lv_label_set_long_mode(ui->devoce_info_page_label_26, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->devoce_info_page_label_26, 9, 90);
    lv_obj_set_size(ui->devoce_info_page_label_26, 89, 26);

    //Write style for devoce_info_page_label_26, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->devoce_info_page_label_26, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->devoce_info_page_label_26, &lv_font_HarmonyOS_Sans_SC_Medium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->devoce_info_page_label_26, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->devoce_info_page_label_26, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->devoce_info_page_label_26, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_label_26, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes devoce_info_page_label_version
    ui->devoce_info_page_label_version = lv_label_create(ui->devoce_info_page);
    lv_label_set_text(ui->devoce_info_page_label_version, "V0.0.0");
    lv_label_set_long_mode(ui->devoce_info_page_label_version, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->devoce_info_page_label_version, 136, 90);
    lv_obj_set_size(ui->devoce_info_page_label_version, 89, 26);

    //Write style for devoce_info_page_label_version, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->devoce_info_page_label_version, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->devoce_info_page_label_version, &lv_font_montserratMedium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->devoce_info_page_label_version, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->devoce_info_page_label_version, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->devoce_info_page_label_version, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->devoce_info_page_label_version, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of devoce_info_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->devoce_info_page);

    //Init events for screen.
    // events_init_devoce_info_page(ui);
}
