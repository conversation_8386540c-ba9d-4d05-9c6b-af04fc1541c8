#include "ui_user_inc.h"
#include "freertos/FreeRTOS.h"
#include "gui_port.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "device_bsp.h"
#include "lvgl.h"

/*页面通知服务*/
#define TAG "ui notif"

extern void ui_notif_scr_can_connect_task(ui_notif_msg_t *msg);
extern void ui_notif_scr_wave_task(ui_notif_msg_t *msg);
extern void ui_notif_scr_can_connect_task(ui_notif_msg_t *msg);

// STATIC
static void _notif_serv_task(void *pvParameters);
static void _notif_serv_destroy(void);

typedef struct
{
    QueueHandle_t serv_q;
    bool running;
} ui_notif_service_t;

ui_notif_service_t ui_notif_serv_param;
// periph_service_handle_t ui_notif_service;

void ui_notif_service_init()
{
    ui_notif_serv_param.running = false;
    // 增加队列大小到100，应对高频消息
    ui_notif_serv_param.serv_q = xQueueCreate(100, sizeof(ui_notif_msg_t));

    // 设置中等优先级4，低于CAN接收任务(7)但保证UI响应性
    xTaskCreatePinnedToCore(_notif_serv_task, "ui_notif_serv", 8 * 1024, (void *)&ui_notif_serv_param, 4, NULL, 0);

}

void ui_notif_service_commit(ui_notif_msg_t *msg)
{
    BaseType_t xReturn = pdPASS;

    if (ui_notif_serv_param.serv_q) {
        // 使用50ms超时，避免完全阻塞但给队列一些等待时间
        xReturn = xQueueSend(ui_notif_serv_param.serv_q, msg, pdMS_TO_TICKS(50));
        if (xReturn != pdTRUE) {
            // 获取队列当前使用情况
            UBaseType_t queue_waiting = uxQueueMessagesWaiting(ui_notif_serv_param.serv_q);
            UBaseType_t queue_spaces = uxQueueSpacesAvailable(ui_notif_serv_param.serv_q);
            ESP_LOGW("ui_notif", "Queue send failed, queue usage: %d/100, available: %d", 
                     queue_waiting, queue_spaces);
        }
    }
}

/***********************************STATIC*************************************/
static void _notif_serv_destroy()
{
    ui_notif_serv_param.running = false;

    if (ui_notif_serv_param.serv_q)
    {
        vQueueDelete(ui_notif_serv_param.serv_q);
    }
}

static void _notif_serv_task(void *pvParameters)
{
    ui_notif_service_t *service = (ui_notif_service_t *)pvParameters;

    BaseType_t xReturn = pdFALSE;
    ui_notif_msg_t msg = {0};
    service->running = true;
    while (service->running)
    {
        xReturn = xQueueReceive(service->serv_q, &msg, portMAX_DELAY);
        if (xReturn == pdTRUE)
        {
            // if (msg.type == UI_NOTIF_SERV_DESTROY)
            // {
            //     hal_service_destroy(ui_notif_service);
            //     continue;
            // }

            ui_scr_t *scr = ui_scr_get_cur();

            if (scr == NULL)
            {
                continue;
            }

            if (lvgl_port_lock(10))  
            {
                // layer top msg
                // ui_notif_scr_layertop_task(&msg);
                switch ((int)scr->id)
                {
                case UI_SCR_ID_MENU_PAGE:
                    // ui_notif_scr_menu_task(&msg);
                    break;
                case UI_SCR_ID_CAN_CONNET_PAGE:
                    ui_notif_scr_can_connect_task(&msg);
                    break;
                case UI_SCR_ID_BT_PAGE:
                    // ui_notif_scr_network_task(&msg);
                    break;
                case UI_SCR_ID_SETTINGS_PAGE:
                    // ui_notif_scr_setting_time_task(&msg);
                    break;
                case UI_SCR_ID_PID_PAGE:
                    // ui_notif_scr_update_task(&msg);
                    break;
                case UI_SCR_ID_AI_PAGE:
                    // ui_notif_scr_file_task(&msg);
                    break;
                case UI_SCR_ID_WAVE_PAGE:
                    ui_notif_scr_wave_task(&msg);
                    break;
                case UI_SCR_ID_WIFI_PAGE:
                    // ui_notif_scr_calendar_task(&msg);
                    break;
                case UI_SCR_ID_DEVOCE_INFO_PAGE:
                    // ui_notif_scr_timer_task(&msg);
                    break;
                case UI_SCR_ID_UPDATE_PAGE:
                    // ui_notif_scr_recorder_task(&msg);
                    break;
                default:
                    break;
                }
                // 释放LVGL锁
                lvgl_port_unlock();
            }
            else
            {
                // 获取锁失败，记录警告但不阻塞任务
                ESP_LOGW("ui_notif", "Failed to acquire LVGL lock within timeout, skipping update");
            }
        }

        // 减少任务延迟，提高处理速度
        vTaskDelay(1);
        
        // 定期监控队列使用情况（每5秒打印一次）
        static uint32_t last_monitor_time = 0;
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        if (current_time - last_monitor_time > 5000) {
            UBaseType_t queue_waiting = uxQueueMessagesWaiting(service->serv_q);
            if (queue_waiting > 50) {  // 队列使用超过50%时警告
                ESP_LOGW(TAG, "Queue usage high: %d/100 messages", queue_waiting);
            }
            last_monitor_time = current_time;
        }
    }

    ESP_LOGI(TAG, "ui notif service stopped!");
    vTaskDelete(NULL);
}
