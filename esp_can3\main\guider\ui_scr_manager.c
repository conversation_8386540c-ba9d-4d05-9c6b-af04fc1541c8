//UI 屏幕管理器。它的核心职责是管理应用程序中所有不同的 UI 屏幕（或称为页面、视图），
//并处理它们之间的导航（跳转、返回）以及生命周期。
// 优点：
// 集中管理：提供了一个中心点来管理所有 UI 屏幕。
// 模块化：每个屏幕的定义和初始化逻辑可以独立存在，管理器只负责调度。
// 可扩展性：方便添加新的屏幕到系统中。
// 状态保持：利用 RTC 内存实现了跨休眠的屏幕状态恢复。
// 导航控制：提供了简单的页面跳转和返回机制。

#include "ui_user_inc.h"

#define TAG "ui manager"

lv_ui guider_ui;
lv_ll_t ui_scr_ll;

// 分配到RTC内存中，休眠唤醒后保持不变
RTC_NOINIT_ATTR static ui_scr_id_t scr_back_id, scr_cur_id;

// ui_toast_txt_t ui_toast = UI_TOAST_TXT_DEFAULT();

static ui_scr_t *ui_scr_find(ui_scr_id_t id)
{
    ui_scr_t *scr;

    if (_lv_ll_is_empty(&ui_scr_ll))
        return NULL;

    scr = _lv_ll_get_head(&ui_scr_ll);

    while (scr)
    {
        if (scr->id == id)
            return scr;

        scr = _lv_ll_get_next(&ui_scr_ll, scr);
    }

    return NULL;
}

void ui_scr_init(ui_scr_id_t start_id)
{
    ESP_LOGI(TAG, "Initializing UI screen manager...");
    _lv_ll_init(&(ui_scr_ll), sizeof(ui_scr_t));

    // 初始化键盘
    ESP_LOGI(TAG, "Initializing keyboard...");
    init_keyboard(&guider_ui);

    // 添加页面到页面管理列表
    ESP_LOGI(TAG, "Adding screens to screen manager...");
    ui_scr_add(&def_scr_start_page);
    ui_scr_add(&def_scr_menu_page);
    ui_scr_add(&def_scr_can_connet_page);
    ui_scr_add(&def_scr_bt_page);
    ui_scr_add(&def_scr_settings_page);
    ui_scr_add(&def_scr_pid_page);
    ui_scr_add(&def_scr_ai_page);
    ui_scr_add(&def_scr_wave_page);
    ui_scr_add(&def_scr_WIFI_page);
    ui_scr_add(&def_scr_devoce_info_page);
    ui_scr_add(&def_scr_update_page);
    ui_scr_add(&def_scr_motor_update_page);
    
    if (start_id > UI_SCR_ID_ALL_MAX) {
        ESP_LOGW(TAG, "Invalid start ID %d, using default %d", start_id, DEF_UI_SCR_START);
        start_id = DEF_UI_SCR_START;
    }

    // 预初始化菜单页面，确保菜单页面对象在启动时就被创建
    ESP_LOGI(TAG, "Pre-initializing Menu page...");
    ui_scr_t *menu_scr = ui_scr_find(UI_SCR_ID_MENU_PAGE);
    if (menu_scr && menu_scr->setup_handle) {
        ESP_LOGI(TAG, "Setting up Menu page objects...");
        menu_scr->setup_handle(&guider_ui);
        if (guider_ui.Menu_page) {
            ESP_LOGI(TAG, "Menu page pre-initialized successfully");
        } else {
            ESP_LOGE(TAG, "Failed to pre-initialize Menu page!");
        }
    }

    // 加载起始页面
    ui_scr_t *scr_find = ui_scr_find(start_id);
    if (scr_find == NULL) {
        ESP_LOGE(TAG, "Failed to find screen with ID %d", start_id);
        return;
    }

    ESP_LOGI(TAG, "Setting up initial screen '%s' (ID: %d)...", scr_find->name, scr_find->id);
    
    // 初始化页面
    if (scr_find->setup_handle) {
        ESP_LOGI(TAG, "Calling screen setup handler...");
        scr_find->setup_handle(&guider_ui);
    } else {
        ESP_LOGW(TAG, "Screen '%s' has no setup handler", scr_find->name);
    }
    
    if (scr_find->setup_user_handle) {
        ESP_LOGI(TAG, "Calling screen user setup handler...");
        scr_find->setup_user_handle(&guider_ui); // 用户自定义初始化
    }
    
    // 验证对象已正确创建
    if (*(scr_find->obj) == NULL) {
        ESP_LOGE(TAG, "Screen object is NULL, initialization failed");
        return;
    }
    
    // 加载页面
    ESP_LOGI(TAG, "Loading initial screen...");
    lv_scr_load_anim(*(scr_find->obj), LV_SCR_LOAD_ANIM_NONE, 100, 0, true);
    scr_cur_id = scr_find->id;
    
    ESP_LOGI(TAG, "UI screen manager initialization complete");
}

void ui_scr_goto(ui_scr_id_t id, bool anim)
{
    ui_scr_t *scr_find = ui_scr_find(id);
    if (scr_find == NULL)
    {
        ESP_LOGE(TAG, "Screen with ID %d not found!", id);
        return;
    }

    // 检查当前活动屏幕
    lv_obj_t *act_scr = lv_scr_act();
    if (act_scr == NULL)
    {
        ESP_LOGE(TAG, "Active screen is NULL!");
        return;
    }

    lv_disp_t *d = lv_obj_get_disp(act_scr);
    if (d == NULL)
    {
        ESP_LOGE(TAG, "Display is NULL!");
        return;
    }

    // 保存上一个页面ID用于后退功能
    if (scr_cur_id > 0)
        scr_back_id = scr_cur_id;

    // 确保没有页面切换正在进行
    if (d->prev_scr == NULL && (d->scr_to_load == NULL || d->scr_to_load == act_scr))
    {
        ESP_LOGI(TAG, "Going to screen '%s' (ID: %d)", scr_find->name, scr_find->id);

        // 强制初始化目标页面（即使已初始化过）
        ESP_LOGI(TAG, "Setting up target screen...");
        if (scr_find->setup_handle)
        {
            ESP_LOGI(TAG, "Calling screen setup handler");
            scr_find->setup_handle(&guider_ui);
        }
        else
        {
            ESP_LOGW(TAG, "Screen '%s' has no setup handler", scr_find->name);
        }

        if (scr_find->setup_user_handle)
        {
            ESP_LOGI(TAG, "Calling screen user setup handler");
            scr_find->setup_user_handle(&guider_ui);
        }

        // 验证对象已正确创建
        if (*(scr_find->obj) == NULL)
        {
            ESP_LOGE(TAG, "Failed to initialize screen object!");
            return;
        }

        // 根据anim参数决定是否使用动画
        if (anim)
        {
            ESP_LOGI(TAG, "Loading screen with animation: type=%d, time=%d", 
                    scr_find->load_anim, scr_find->anim_time);
            
            // 设置背景色，使过渡更平滑
            lv_obj_set_style_bg_opa(act_scr, LV_OPA_MAX, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(act_scr, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);

            lv_scr_load_anim(*(scr_find->obj), scr_find->load_anim, scr_find->anim_time, 0, true);
        }
        else
        {
            ESP_LOGI(TAG, "Loading screen without animation");
            lv_scr_load(*(scr_find->obj));
        }

        scr_cur_id = scr_find->id;
        ESP_LOGI(TAG, "Screen transition initiated successfully");
    }
    else
    {
        ESP_LOGW(TAG, "Screen transition already in progress, request ignored");
    }
}

void ui_scr_goback(bool anim)
{
    if (scr_back_id > 0)
    {
        ui_scr_goto(scr_back_id, anim);
    }
}

void ui_scr_add(ui_scr_t *scr)
{
    if (scr == NULL)
        return;

    ui_scr_t *scr_find = ui_scr_find(scr->id);

    if (scr_find)
    {
        ESP_LOGI(TAG, "scr %s exist!", scr_find->name);
    }
    else
    {
        ui_scr_t *new_node = _lv_ll_ins_head(&ui_scr_ll);
        memcpy(new_node, scr, sizeof(ui_scr_t));
        ESP_LOGI(TAG, "scr %s add!", scr->name);
    }
}

void ui_scr_remove(ui_scr_id_t id)
{
    ui_scr_t *scr_find = ui_scr_find(id);

    if (scr_find)
    {
        ESP_LOGI(TAG, "scr %s removed!", scr_find->name);
        _lv_ll_remove(&ui_scr_ll, scr_find);
        lv_mem_free(scr_find);
    }
}

ui_scr_t *ui_scr_get(ui_scr_id_t id)
{
    return ui_scr_find(id);
}

ui_scr_t *ui_scr_get_cur()
{
    return ui_scr_find(scr_cur_id);
}

void ui_scr_set_animtime(ui_scr_id_t id, int time)
{
    ui_scr_t *scr_find = ui_scr_find(id);
    if (scr_find)
    {
        scr_find->anim_time = time;
    }
}

void ui_scr_set_animtype(ui_scr_id_t id, lv_scr_load_anim_t anim)
{
    ui_scr_t *scr_find = ui_scr_find(id);
    if (scr_find)
    {
        scr_find->load_anim = anim;
    }
}