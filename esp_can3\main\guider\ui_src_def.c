#include "ui_user_inc.h"
#include "events_init.h"
/**页面定义 */

extern void events_init_Start_Page(lv_ui *ui);
extern void events_init_Menu_page(lv_ui *ui);
extern void events_init_MeterTestOne_Page(lv_ui *ui);
extern void events_init_MeterTestTwo_page(lv_ui *ui);
extern void events_init_BT_page(lv_ui *ui);
extern void events_init_Settings_Page(lv_ui *ui);
extern void events_init_Wave_Page(lv_ui *ui);
extern void events_init_PrinterSetting_page(lv_ui *ui);
extern void events_init_Use_feedback_page(lv_ui *ui);
extern void events_init_Reward_developers_page(lv_ui *ui);

/***********************************************MENU PAGES************************************************/
ui_scr_t def_scr_start_page = {
    .obj = &guider_ui.Start_Page,
    .id = UI_SCR_ID_START_PAGE,
    .name = "Start_Page",
    .anim_time = 300,  // 更长的动画时间，更平滑
    .load_anim = LV_SCR_LOAD_ANIM_FADE_ON,  // 淡入效果
    .setup_handle = setup_scr_Start_Page,
    .setup_user_handle = events_init_Start_Page,
};

ui_scr_t def_scr_menu_page = {
    .obj = &guider_ui.Menu_page,
    .id = UI_SCR_ID_MENU_PAGE,
    .name = "Menu_page",
    .anim_time = 300,  // 更长的动画时间，更平滑
    .load_anim = LV_SCR_LOAD_ANIM_MOVE_TOP,  // 从下向上移动效果
    .setup_handle = setup_scr_Menu_page,
    .setup_user_handle = events_init_Menu_page,
};

ui_scr_t def_scr_can_connet_page = {
    .obj = &guider_ui.Can_Connet_page,
    .id = UI_SCR_ID_CAN_CONNET_PAGE,
    .name = "Can_Connet_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_Can_Connet_page,
    .setup_user_handle = events_init_Can_Connet_page,
};

ui_scr_t def_scr_pid_page = {
    .obj = &guider_ui.PID_page,
    .id = UI_SCR_ID_PID_PAGE,
    .name = "PID_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_PID_page,
    .setup_user_handle = events_init_PID_page,
};

ui_scr_t def_scr_bt_page = {
    .obj = &guider_ui.BT_page,
    .id = UI_SCR_ID_BT_PAGE,
    .name = "BT_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_BT_page,
    .setup_user_handle = events_init_BT_page,
};

ui_scr_t def_scr_ai_page = {
    .obj = &guider_ui.ai_page,
    .id = UI_SCR_ID_AI_PAGE,
    .name = "ai_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_ai_page,
    .setup_user_handle = NULL,//events_init_ai_page,
};

ui_scr_t def_scr_settings_page = {
    .obj = &guider_ui.Settings_Page,
    .id = UI_SCR_ID_SETTINGS_PAGE,
    .name = "Settings_Page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_Settings_Page,
    .setup_user_handle = events_init_Settings_Page,
};

ui_scr_t def_scr_wave_page = {
    .obj = &guider_ui.Wave_Page,
    .id = UI_SCR_ID_WAVE_PAGE,
    .name = "Wave_Page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_Wave_Page,
    .setup_user_handle = events_init_Wave_Page,
};

ui_scr_t def_scr_devoce_info_page = {
    .obj = &guider_ui.devoce_info_page,
    .id = UI_SCR_ID_DEVOCE_INFO_PAGE,
    .name = "devoce_info_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_devoce_info_page,
    .setup_user_handle = events_init_devoce_info_page,
};

ui_scr_t def_scr_WIFI_page = {
    .obj = &guider_ui.WIFI_page,
    .id = UI_SCR_ID_WIFI_PAGE,
    .name = "WIFI_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_WIFI_page,
    .setup_user_handle = events_init_WIFI_page,
};

ui_scr_t def_scr_update_page = {
    .obj = &guider_ui.update_page,
    .id = UI_SCR_ID_UPDATE_PAGE,
    .name = "update_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_update_page,
    .setup_user_handle = events_init_update_Page,
};

ui_scr_t def_scr_motor_update_page = {
    .obj = &guider_ui.motor_update_page,
    .id = UI_SCR_ID_MOTOR_UPDATE_PAGE,
    .name = "motor_update_page",
    .anim_time = DEF_UI_SCR_ANIMTIME,
    .load_anim = DEF_UI_SCR_ANIMTYPE,
    .setup_handle = setup_scr_motor_update_page,
    .setup_user_handle = events_init_motor_update_page,
};
/***********************************************SETTING PAGES************************************************/
// ui_scr_t def_scr_setting_network = {
//     .obj = &guider_ui.screen_setting_network,
//     .id = UI_SCR_ID_SETTING_NETWORK,
//     .name = "setting network",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_network,
//     .setup_user_handle = setup_user_scr_setting_network,
// };

// ui_scr_t def_scr_setting_bkl = {
//     .obj = &guider_ui.screen_setting_bkl,
//     .id = UI_SCR_ID_SETTING_BKL,
//     .name = "setting bkl",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_bkl,
//     .setup_user_handle = setup_user_scr_setting_bkl,
// };

// ui_scr_t def_scr_setting_sound = {
//     .obj = &guider_ui.screen_setting_sound,
//     .id = UI_SCR_ID_SETTING_SOUND,
//     .name = "setting sound",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_sound,
//     .setup_user_handle = setup_user_scr_setting_sound,
// };

// ui_scr_t def_scr_setting_time = {
//     .obj = &guider_ui.screen_setting_time,
//     .id = UI_SCR_ID_SETTING_TIME,
//     .name = "setting time",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_time,
//     .setup_user_handle = setup_user_scr_setting_time,
// };

// ui_scr_t def_scr_setting_sleep = {
//     .obj = &guider_ui.screen_setting_sleep,
//     .id = UI_SCR_ID_SETTING_SLEEP,
//     .name = "setting sleep",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_sleep,
//     .setup_user_handle = setup_user_scr_setting_sleep,
// };

// ui_scr_t def_scr_setting_webserver = {
//     .obj = &guider_ui.screen_setting_webserver,
//     .id = UI_SCR_ID_SETTING_WEBSERVER,
//     .name = "setting webserver",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_webserver,
//     .setup_user_handle = setup_user_scr_setting_webserver,
// };

// ui_scr_t def_scr_setting_ai = {
//     .obj = &guider_ui.screen_setting_ai,
//     .id = UI_SCR_ID_SETTING_AI,
//     .name = "setting ai",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_ai,
//     .setup_user_handle = setup_user_scr_setting_ai,
// };

// ui_scr_t def_scr_setting_about = {
//     .obj = &guider_ui.screen_setting_about,
//     .id = UI_SCR_ID_SETTING_ABOUT,
//     .name = "setting about",
//     .anim_time = DEF_UI_SCR_ANIMTIME,
//     .load_anim = DEF_UI_SCR_ANIMTYPE,
//     .setup_handle = setup_scr_screen_setting_about,
//     .setup_user_handle = setup_user_scr_setting_about,
// };


