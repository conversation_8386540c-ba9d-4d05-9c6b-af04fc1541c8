#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "esp_log.h"
#include "ui_user_inc.h"

#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

#define MENU_TAG "MENU_EVENTS"

// 按钮配置结构体
typedef struct {
    lv_obj_t **btn_obj;        // 按钮对象的指针
    ui_scr_id_t target_page;   // 目标页面ID
    const char *log_message;   // 日志消息
    bool load_page;            // 是否需要加载页面
} menu_button_config_t;

// 通用按钮事件处理函数
static void generic_menu_button_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        menu_button_config_t *config = (menu_button_config_t *)lv_event_get_user_data(e);
        if (config) {
            // ESP_LOGI(MENU_TAG, "%s", config->log_message);
            ui_scr_goto(config->target_page, config->load_page);
        }
        break;
    }
    default:
        break;
    }
}

// 通用的返回主菜单事件处理函数
void back_to_menu_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {      
        if (guider_ui.Menu_page == NULL) {
            ESP_LOGE("EVENTS", "Menu_page is NULL, initializing");
            ui_scr_t *menu_scr = ui_scr_get(UI_SCR_ID_MENU_PAGE);
            if (menu_scr && menu_scr->setup_handle) {
                menu_scr->setup_handle(&guider_ui);
            } else {
                ESP_LOGE("EVENTS", "Menu_page setup_handle is NULL, cannot proceed");
                return;
            }
        }        
        ui_scr_goto(UI_SCR_ID_MENU_PAGE, false);
        break;
    }
    default:
        break;
    }
}

// 按钮配置数组
static menu_button_config_t menu_buttons[] = {
    {&guider_ui.Menu_page_btn_wave, UI_SCR_ID_WAVE_PAGE, "Wave button clicked", true},
    {&guider_ui.Menu_page_btn_ai, UI_SCR_ID_AI_PAGE, "AI button clicked", true},
    {&guider_ui.Menu_page_btn_pid, UI_SCR_ID_PID_PAGE, "PID button clicked", true},
    {&guider_ui.Menu_page_btn_setting, UI_SCR_ID_SETTINGS_PAGE, "Setting button clicked", true},
    {&guider_ui.Menu_page_btn_bt, UI_SCR_ID_BT_PAGE, "BT button clicked", true},
    {&guider_ui.Menu_page_btn_can, UI_SCR_ID_CAN_CONNET_PAGE, "CAN button clicked", true},
    {&guider_ui.Menu_page_btn_wifi, UI_SCR_ID_WIFI_PAGE, "WIFI button clicked", true},
    {&guider_ui.Menu_page_btn_device, UI_SCR_ID_DEVOCE_INFO_PAGE, "Device button clicked", true},
    {&guider_ui.Menu_page_btn_ota, UI_SCR_ID_UPDATE_PAGE, "Update button clicked", true},
    {&guider_ui.Menu_page_btn_motor_update, UI_SCR_ID_MOTOR_UPDATE_PAGE, "Motor update button clicked", true}

};

#define MENU_BUTTONS_COUNT (sizeof(menu_buttons) / sizeof(menu_buttons[0]))

// 菜单页面初始化
void ui_setup_menu(void) {
    ESP_LOGI(MENU_TAG, "设置菜单页面");
    
    // 获取菜单页面
    ui_scr_t *menu_scr = ui_scr_get(UI_SCR_ID_MENU_PAGE);
    if (menu_scr == NULL) {
        ESP_LOGE(MENU_TAG, "Menu page not found in screen manager");
        return;
    }
    
    // 初始化菜单页面
    if (guider_ui.Menu_page == NULL && menu_scr->setup_handle) {
        menu_scr->setup_handle(&guider_ui);
    }
    
    // 使用ui_scr_goto函数切换到菜单页面
    ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
}

void events_init_Menu_page(lv_ui *ui)
{
    ESP_LOGI(MENU_TAG, "Initializing menu page events");

    // 使用循环注册所有按钮事件
    for (int i = 0; i < MENU_BUTTONS_COUNT; i++) {
        if (*(menu_buttons[i].btn_obj) != NULL) {
            lv_obj_add_event_cb(*(menu_buttons[i].btn_obj), generic_menu_button_event_handler, LV_EVENT_ALL, &menu_buttons[i]);
        } else {
            ESP_LOGW(MENU_TAG, "Button %d is NULL, skipping event registration", i);
        }
    }

    ESP_LOGI(MENU_TAG, "Menu page events initialization complete");
}