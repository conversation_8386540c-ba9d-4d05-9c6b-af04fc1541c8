#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

static void Settings_Page_ta_2_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_FOCUSED:
    {
        break;
    }
    default:
        break;
    }
}

void events_init_Settings_Page (lv_ui *ui)
{
    // lv_obj_add_event_cb(ui->Settings_Page_ta_2, Settings_Page_ta_2_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Settings_Page_imgbtn_1, back_to_menu_event_handler, LV_EVENT_ALL, ui);
}