#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "esp_log.h"
#include "ui_user_inc.h"

#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif
// 定时器回调函数，用于在进度条完成后跳转到菜单页面
static void progress_complete_timer_cb(lv_timer_t * timer)
{
    // 添加安全检查，确保UI对象存在且有效
    if (lv_scr_act() == NULL) {
        ESP_LOGE("EVENTS", "Active screen is NULL, skipping animation");
        lv_timer_del(timer);
        return;
    }
    
    // 通过屏幕管理器检查菜单页面是否可用
    ui_scr_t *menu_scr = ui_scr_get(UI_SCR_ID_MENU_PAGE);
    if (menu_scr == NULL || *(menu_scr->obj) == NULL) {
        ESP_LOGE("EVENTS", "Menu page not properly initialized");
        lv_timer_del(timer);
        return;
    }
    
    ESP_LOGI("EVENTS", "Progress complete, navigating to menu page");
    // 使用更安全的跳转方式，使用ui_scr_goto函数
    ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
    
    // 删除定时器
    lv_timer_del(timer);
}


static void Start_Page_ProgressBar_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_VALUE_CHANGED:
    {
        lv_obj_t * progressbar = lv_event_get_target(e);
        int32_t value = lv_bar_get_value(progressbar);
        
        // 打印进度值
        ESP_LOGI("EVENTS", "Progress bar value changed: %ld", value);
        
        // 当进度条达到95%时，跳转到菜单页面
        if (value >= 95) {
            ESP_LOGI("EVENTS", "Progress bar reaching completion, navigating to menu page");
            
            // 使用ui_scr_goto跳转到菜单页面
            ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
        }
        break;
    }
    case LV_EVENT_SCROLL_END:
    {
        ESP_LOGI("EVENTS", "Progress bar scroll end event");
        
        // 使用ui_scr_goto跳转到菜单页面
        ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
        break;
    }
    default:
        break;
    }
}

static void Start_Page_btn_1_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_CLICKED:
    {
        // 通过屏幕管理器检查菜单页面是否可用
        ui_scr_t *menu_scr = ui_scr_get(UI_SCR_ID_MENU_PAGE);
        if (menu_scr == NULL || *(menu_scr->obj) == NULL) {
            ESP_LOGW("EVENTS", "Menu page not ready for navigation");
            return;
        }
        
        ESP_LOGI("EVENTS", "Button clicked, navigating to menu page");
        // 使用更安全的跳转方式
        ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
        break;
    }
    default:
        break;
    }
}

// 延迟跳转定时器回调
static void delayed_menu_load_timer_cb(lv_timer_t * timer)
{
    ESP_LOGI("EVENTS", "Delayed menu load timer triggered");
    
    // 使用ui_scr_goto跳转到菜单页面
    ui_scr_goto(UI_SCR_ID_MENU_PAGE, true);
    
    lv_timer_del(timer);
}

void events_init_Start_Page(lv_ui *ui)
{
    ESP_LOGI("EVENTS", "Initializing Start_Page events");
    
    lv_obj_add_event_cb(ui->Start_Page_ProgressBar, Start_Page_ProgressBar_event_handler, LV_EVENT_ALL, ui);
   

    // 创建直接的延迟跳转定时器，3秒后执行
    ESP_LOGI("EVENTS", "Creating delayed menu load timer...");
    lv_timer_t * timer = lv_timer_create(delayed_menu_load_timer_cb, 3000, NULL);
    lv_timer_set_repeat_count(timer, 1); // 只执行一次
    
    // 检查菜单页面是否已初始化
    if (guider_ui.Menu_page) {
        ESP_LOGI("EVENTS", "Menu_page is already initialized");
    } else {
        ESP_LOGW("EVENTS", "Menu_page is not initialized yet!");
    }
    
    ESP_LOGI("EVENTS", "Start_Page events initialized");
}