#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#include "canopen.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

static const char *TAG = "wave_setup";

// 全局图表系列指针定义
lv_chart_series_t *g_wave_chart_series = NULL;

// 外部函数声明
extern void wave_set_display_type(wave_data_type_t type);

/**
 * @brief 下拉框选择事件处理
 */
static void wave_dropdown_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);

    if (code == LV_EVENT_VALUE_CHANGED)
    {
        uint16_t selected = lv_dropdown_get_selected(obj);
        wave_data_type_t wave_type;

        // 根据下拉框选择映射到波形数据类型
        switch (selected)
        {
        case 0:
            wave_type = WAVE_DATA_CURRENT;
            break;
        case 1:
            wave_type = WAVE_DATA_SPEED;
            break;
        case 2:
            wave_type = WAVE_DATA_POSITION;
            break;
        case 3:
            wave_type = WAVE_DATA_VOLTAGE;
            break;
        case 4:
            wave_type = WAVE_DATA_TEMPERATURE;
            break;
        default:
            wave_type = WAVE_DATA_CURRENT;
            break;
        }

        // 切换波形显示类型
        wave_set_display_type(wave_type);

        ESP_LOGI(TAG, "Wave type changed to: %d, display refreshed", wave_type);
    }
}

/**
 * @brief 自动缩放按键事件处理 - 修改为开关模式
 */
static void Wave_Page_btn_auto_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code)
    {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "Auto scale toggle button clicked");

        // 切换自动缩放开关状态
        wave_toggle_auto_scale();

        ESP_LOGI(TAG, "Auto scale toggle completed");
        break;
    }
    default:
        break;
    }
}

/**
 * @brief Y轴分辨率增加按键事件处理（缩小Y轴显示范围，提高分辨率）
 */
static void Wave_Page_btn_biggeer_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code)
    {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "Y-axis resolution increase button clicked");

        // 增加Y轴分辨率（缩小显示范围）
        wave_increase_resolution();

        ESP_LOGI(TAG, "Y-axis resolution increase completed");
        break;
    }
    default:
        break;
    }
}

/**
 * @brief 扩大Y轴显示范围，
 */
static void Wave_Page_btn_smaller_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code)
    {
    case LV_EVENT_CLICKED:
    {
        ESP_LOGI(TAG, "Y-axis resolution decrease button clicked");

        // 减少Y轴分辨率（扩大显示范围）
        wave_decrease_resolution();

        ESP_LOGI(TAG, "Y-axis resolution decrease completed");
        break;
    }
    default:
        break;
    }
}

/**
 * @brief 滑块值变化事件处理 - 更新发送按钮文本
 */
static void Wave_Page_slider_target_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *slider = lv_event_get_target(e);

    if (code == LV_EVENT_VALUE_CHANGED)
    {
        // 获取滑块当前值
        int32_t slider_value = lv_slider_get_value(slider);

        // 获取UI对象
        lv_ui *ui = &guider_ui;

        // 更新发送按钮的文本显示滑块数值
        if (ui->Wave_Page_btn_send_label)
        {
            lv_label_set_text_fmt(ui->Wave_Page_btn_send_label, "%ld", slider_value);
        }

        // ESP_LOGI(TAG, "Slider value changed to: %ld, button text updated", slider_value);
    }
}

/**
 * @brief 发送按钮点击事件处理函数
 */
static void Wave_Page_btn_send_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED)
    {
        ESP_LOGI(TAG, "Send button clicked");

        // 获取UI对象
        lv_ui *ui = &guider_ui;

        // UI对象有效性检查
        if (!ui || !ui->Wave_Page_slider_target || !ui->Wave_Page_ddlist_1)
        {
            ESP_LOGE(TAG, "UI objects are invalid - ui: %p, slider: %p, dropdown: %p",
                     ui, ui ? ui->Wave_Page_slider_target : NULL, ui ? ui->Wave_Page_ddlist_1 : NULL);
            return;
        }

        // 获取滑块值
        int32_t target_value = lv_slider_get_value(ui->Wave_Page_slider_target);

        // 获取下拉框选择的控制模式
        uint16_t mode_index = lv_dropdown_get_selected(ui->Wave_Page_ddlist_1);

        // 控制模式到RPDO类型的映射
        uint16_t rpdo_type;
        switch (mode_index)
        {
        case 0: // 力矩环
            rpdo_type = 0x200;
            break;
        case 1: // 速度环
            rpdo_type = 0x300;
            break;
        case 2: // 位置环
            rpdo_type = 0x400;
            break;
        case 3: // 速度轨迹环
            rpdo_type = 0x500;
            break;
        case 4: // 位置轨迹环
            rpdo_type = 0x600;
            break;
        default:
            ESP_LOGW(TAG, "Unknown mode index: %d, using default torque mode (0x200)", mode_index);
            rpdo_type = 0x700; // 默认使用力矩环模式
            break;
        }

        // 使用默认节点ID（通常为1）
        uint8_t target_node_id = DEFAULT_NODE_ID;

        // 发送RPDO消息
        esp_err_t ret = canopen_send_rpdo(rpdo_type, target_node_id, target_value);

        // 记录发送结果
        if (ret == ESP_OK)
        {
            ESP_LOGI(TAG, "Target sent successfully - Mode: %d, Value: %ld, RPDO: 0x%03X",
                     mode_index, target_value, rpdo_type);
        }
        else
        {
            ESP_LOGE(TAG, "Failed to send target - Mode: %d, Value: %ld, Error: %s",
                     mode_index, target_value, esp_err_to_name(ret));
        }
    }
}

/**
 * @brief 初始化波形页面
 */
static void wave_page_init(lv_ui *ui)
{
    // 更新下拉框选项，添加更多数据类型
    lv_dropdown_set_options(ui->Wave_Page_ddlist_1,
                            "力矩环\n速度环\n位置环\n速度轨迹环\n位置轨迹环");

    // 设置默认选择
    lv_dropdown_set_selected(ui->Wave_Page_ddlist_1, 0);

    // 初始化图表设置
    if (ui->Wave_Page_chart_1)
    {
        // 设置图表为实时更新模式
        lv_chart_set_update_mode(ui->Wave_Page_chart_1, LV_CHART_UPDATE_MODE_SHIFT);

        // 设置点数
        lv_chart_set_point_count(ui->Wave_Page_chart_1, 50);

        // 创建数据系列 - 使用全局变量存储系列指针
        g_wave_chart_series = lv_chart_add_series(ui->Wave_Page_chart_1,
                                                  lv_color_hex(0xFF0034),
                                                  LV_CHART_AXIS_PRIMARY_Y);

        if (!g_wave_chart_series)
        {
            ESP_LOGE(TAG, "Failed to create chart series!");
            return;
        }
    }

    // 初始化按键颜色状态
    if (ui->Wave_Page_btn_auto)
    {
        // 自动缩放按键初始为蓝色（关闭状态）
        lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x0000FF), LV_STATE_DEFAULT);
    }

    // 初始化发送按钮文本为滑块的当前值
    if (ui->Wave_Page_slider_target && ui->Wave_Page_btn_send_label)
    {
        int32_t initial_value = lv_slider_get_value(ui->Wave_Page_slider_target);
        lv_label_set_text_fmt(ui->Wave_Page_btn_send_label, "%ld", initial_value);
    }

    // 初始化显示电流波形（与下拉框默认选择保持一致）
    wave_set_display_type(WAVE_DATA_CURRENT);

    ESP_LOGI(TAG, "Wave page initialized with current waveform");
}

void events_init_Wave_Page(lv_ui *ui)
{
    lv_obj_add_event_cb(ui->Wave_Page_imgbtn_1, back_to_menu_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Wave_Page_ddlist_1, wave_dropdown_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Wave_Page_btn_auto, Wave_Page_btn_auto_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Wave_Page_btn_biggeer, Wave_Page_btn_biggeer_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Wave_Page_btn_smaller, Wave_Page_btn_smaller_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Wave_Page_slider_target, Wave_Page_slider_target_event_handler, LV_EVENT_ALL, ui);
    lv_obj_add_event_cb(ui->Wave_Page_btn_send, Wave_Page_btn_send_event_handler, LV_EVENT_ALL, ui);

    // 初始化波形页面
    wave_page_init(ui);
}