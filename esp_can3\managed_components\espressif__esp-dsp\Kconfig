menu "DSP Library"


config DSP_OP<PERSON>MIZATIONS_SUPPORTED
   bool
   default y
   depends on IDF_TARGET_ESP32 || IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32P4

choice DSP_OPTIMIZATION
   bool "DSP Optimization"
   default DSP_OPTIMIZED if DSP_OPTIMIZATIONS_SUPPORTED
   default DSP_ANSI
   help
      An ANSI C version could be used for verification and debug purpose,
      or for chips where an optimized version is not available.

config DSP_ANSI
   bool "ANSI C"
config DSP_OPTIMIZED
   bool "Optimized"
   depends on DSP_OPTIMIZATIONS_SUPPORTED
endchoice

config DSP_OPTIMIZATION
   int
   default 0 if DSP_ANSI
   default 1 if DSP_OPTIMIZED

choice DSP_MAX_FFT_SIZE
   bool "Maximum FFT length"
   default DSP_MAX_FFT_SIZE_4096
   help
      This is default FFT size for internal usage.

config DSP_MAX_FFT_SIZE_512
   bool "512"
config DSP_MAX_FFT_SIZE_1024
   bool "1024"
config DSP_MAX_FFT_SIZE_2048
   bool "2048"
config DSP_MAX_FFT_SIZE_4096
   bool "4096"
config DSP_MAX_FFT_SIZE_8192
   bool "8192"
config DSP_MAX_FFT_SIZE_16384
   bool "16384"
config DSP_MAX_FFT_SIZE_32768
   bool "32768"
endchoice

config DSP_MAX_FFT_SIZE
   int
   default 512 if DSP_MAX_FFT_SIZE_512
   default 1024 if DSP_MAX_FFT_SIZE_1024
   default 2048 if DSP_MAX_FFT_SIZE_2048
   default 4096 if DSP_MAX_FFT_SIZE_4096
   default 8192 if DSP_MAX_FFT_SIZE_8192
   default 16384 if DSP_MAX_FFT_SIZE_16384
   default 32768 if DSP_MAX_FFT_SIZE_32768

endmenu
