[pytest]
# exclude examples/ota/simple_ota_example/pytest_simple_ota.py
norecursedirs = examples/ota/*
# only the files with prefix `pytest_` would be recognized as pytest test scripts.
python_files = pytest_*.py

# set traceback to "short" to prevent the overwhelming tracebacks
addopts =
  -s
  --embedded-services esp,idf
  --tb short
  --skip-check-coredump y

# ignore PytestExperimentalApiWarning for record_xml_attribute
filterwarnings =
  ignore::_pytest.warning_types.PytestExperimentalApiWarning


markers =
  # target markers
  target: target chip name (--target)
  # env markers
  env: target test env name (--env)
  # config markers
  config: choose specific bins built by `sdkconfig.ci.<config>`
  # app_path markers
  app_path: choose specific app_path, <app_path>[/build_xxx]


# log related
log_cli = True
log_cli_level = INFO
log_cli_format = %(asctime)s %(levelname)s %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# junit related
junit_family = xunit1


## log all to `system-out` when case fail
junit_logging = stdout
junit_log_passing_tests = False
