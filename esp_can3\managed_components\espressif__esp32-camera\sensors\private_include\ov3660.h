/*
 * This file is part of the OpenMV project.
 * Copyright (c) 2013/2014 <PERSON> <i.abdal<PERSON><PERSON>@gmail.com>
 * This work is licensed under the MIT license, see the file LICENSE for details.
 *
 * OV3660 driver.
 *
 */
#ifndef __OV3660_H__
#define __OV3660_H__

#include "sensor.h"

/**
 * @brief Detect sensor pid
 *
 * @param slv_addr SCCB address
 * @param id Detection result
 * @return
 *     0:       Can't detect this sensor
 *     Nonzero: This sensor has been detected
 */
int ov3660_detect(int slv_addr, sensor_id_t *id);

/**
 * @brief initialize sensor function pointers
 *
 * @param sensor pointer of sensor
 * @return
 *      Always 0
 */
int ov3660_init(sensor_t *sensor);

#endif // __OV3660_H__
