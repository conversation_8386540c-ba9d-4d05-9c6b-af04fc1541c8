/*
 * This file is part of the OpenMV project.
 * author: <PERSON> <juan<PERSON><PERSON><PERSON><PERSON><PERSON>@hotmail.com>
 * This work is licensed under the MIT license, see the file LICENSE for details.
 *
 * OV7670 driver.
 *
 */
#ifndef __OV7670_H__
#define __OV7670_H__
#include "sensor.h"

/**
 * @brief Detect sensor pid
 *
 * @param slv_addr SCCB address
 * @param id Detection result
 * @return
 *     0:       Can't detect this sensor
 *     Nonzero: This sensor has been detected
 */
int ov7670_detect(int slv_addr, sensor_id_t *id);

/**
 * @brief initialize sensor function pointers
 *
 * @param sensor pointer of sensor
 * @return
 *      Always 0
 */
int ov7670_init(sensor_t *sensor);

#endif // __OV7670_H__
