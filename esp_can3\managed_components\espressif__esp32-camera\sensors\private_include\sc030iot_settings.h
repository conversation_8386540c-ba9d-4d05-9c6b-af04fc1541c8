//version: V01P00_20220303
//Preview Type:0:DVP Raw 10 bit// 1:Raw 8 bit// 2:YUV422// 3:RAW16
//Preview Type:4:RGB565// 5:<PERSON>xart SPI// 6:MIPI 10bit// 7:MIPI 12bit// 8: MTK SPI
//port  0:MIPI// 1:Parallel// 2:MTK// 3:SPI// 4:TEST// 5: HISPI// 6 : Z2P/Z4P
//I2C Mode    :0:Normal 8Addr,8Data//  1:Samsung 8 Addr,8Data// 2:Micron 8 Addr,16Data
//I2C Mode    :3:St<PERSON>ro 16Addr,8Data//4:Micron2 16 Addr,16Data
//Out Format  :0:YCbYCr/RG_GB// 1:YCrYCb/GR_BG// 2:CbYCrY/GB_RG// 3:CrYCbY/BG_GR
//MCLK Speed  :0:6M//1:8M//2:10M//3:11.4M//4:12M//5:12.5M//6:13.5M//7:15M//8:18M//9:24M
//pin  :BIT0 pwdn// BIT1:reset
//avdd  0:3.3V// 1:2.5V// 2:1.8V
//dovdd  0:2.8V// 1:2.5V// 2:1.8V
//dvdd  0:1.8V// 1:1.5V// 2:1.2V

/*
[DataBase]
DBName=Dothinkey

[Vendor]
VendorName=SmartSens

[Sensor]
SensorName=SC031IOT
width=640
height=480
port=1
type=2
pin=3
SlaveID=0xd0
mode=0
FlagReg=0xf7
FlagMask=0xff
FlagData=0xfa
FlagReg1=0xf8
FlagMask1=0xff
FlagData1=0x46
outformat=0
mclk=20
avdd=2.80000
dovdd=2.800000
dvdd=1.5

Ext0=0
Ext1=0
Ext2=0
AFVCC=0.0000
VPP=0.000000
*/
#include <stdint.h>

static const uint8_t sc030iot_default_init_regs[][2] = {
    {0xf0, 0x30},
    {0x01, 0xff},
    {0x02, 0xff},
    {0x22, 0x07},
    {0x19, 0xff},
    {0x3f, 0x82},
    {0x30, 0x02},
    {0xf0, 0x01},
    {0x70, 0x00},
    {0x71, 0x80},
    {0x72, 0x20},
    {0x73, 0x00},
    {0x74, 0xe0},
    {0x75, 0x10},
    {0x76, 0x81},
    {0x77, 0x88},
    {0x78, 0xe1},
    {0x79, 0x01},
    {0xf5, 0x01},
    {0xf4, 0x0a},
    {0xf0, 0x36},
    {0x37, 0x79},
    {0x31, 0x82},
    {0x3e, 0x60},
    {0x30, 0xf0},
    {0x33, 0x33},
    {0xf0, 0x32},
    {0x48, 0x02},
    {0xf0, 0x33},
    {0x02, 0x12},
    {0x7c, 0x02},
    {0x7d, 0x0e},
    {0xa2, 0x04},
    {0x5e, 0x06},
    {0x5f, 0x0a},
    {0x0b, 0x58},
    {0x06, 0x38},
    {0xf0, 0x32},
    {0x48, 0x02},
    {0xf0, 0x39},
    {0x02, 0x70},
    {0xf0, 0x45},
    {0x09, 0x1c},
    {0xf0, 0x37},
    {0x22, 0x0d},
    {0xf0, 0x33},
    {0x33, 0x10},
    {0xb1, 0x80},
    {0x34, 0x40},
    {0x0b, 0x54},
    {0xb2, 0x78},
    {0xf0, 0x36},
    {0x11, 0x80},
    {0xf0, 0x30},
    {0x38, 0x44},
    {0xf0, 0x33},
    {0xb3, 0x51},
    {0x01, 0x10},
    {0x0b, 0x6c},
    {0x06, 0x24},
    {0xf0, 0x36},
    {0x31, 0x82},
    {0x3e, 0x60},
    {0x30, 0xf0},
    {0x33, 0x33},
    {0xf0, 0x34},
    {0x9f, 0x02},
    {0xa6, 0x40},
    {0xa7, 0x47},
    {0xe8, 0x5f},
    {0xa8, 0x51},
    {0xa9, 0x44},
    {0xe9, 0x36},
    {0xf0, 0x33},
    {0xb3, 0x51},
    {0x64, 0x17},
    {0x90, 0x01},
    {0x91, 0x03},
    {0x92, 0x07},
    {0x01, 0x10},
    {0x93, 0x10},
    {0x94, 0x10},
    {0x95, 0x10},
    {0x96, 0x01},
    {0x97, 0x07},
    {0x98, 0x1f},
    {0x99, 0x10},
    {0x9a, 0x20},
    {0x9b, 0x28},
    {0x9c, 0x28},
    {0xf0, 0x36},
    {0x70, 0x54},
    {0xb6, 0x40},
    {0xb7, 0x41},
    {0xb8, 0x43},
    {0xb9, 0x47},
    {0xba, 0x4f},
    {0xb0, 0x8b},
    {0xb1, 0x8b},
    {0xb2, 0x8b},
    {0xb3, 0x9b},
    {0xb4, 0xb8},
    {0xb5, 0xf0},
    {0x7e, 0x41},
    {0x7f, 0x47},
    {0x77, 0x80},
    {0x78, 0x84},
    {0x79, 0x8a},
    {0xa0, 0x47},
    {0xa1, 0x5f},
    {0x96, 0x43},
    {0x97, 0x44},
    {0x98, 0x54},
    {0xf0, 0x00},
    {0xf0, 0x01},
    {0x73, 0x00},
    {0x74, 0xe0},
    {0x70, 0x00},
    {0x71, 0x80},
    {0xf0, 0x36},
    {0x37, 0x74},
    {0xf0, 0x3f},
    {0x03, 0xa1},
    {0xf0, 0x36},//cvbs_off
    {0x11, 0x80},
    {0xf0, 0x01},
    {0x79, 0xc1},
    {0xf0, 0x37},
    {0x24, 0x21},
    {0xf0, 0x36},
    {0x41, 0x00},
    {0xea, 0x09},
    {0xeb, 0x03},
    {0xec, 0x19},
    {0xed, 0x38},
    {0xe9, 0x30},
    {0xf0, 0x33},
    {0x33, 0x00},
    {0x34, 0x00},
    {0xb1, 0x00},
    {0xf0, 0x00},
    {0xe0, 0x04},
    {0xf0, 0x01},
    {0x73, 0x00},
    {0x74, 0xe0},
    {0x70, 0x00},
    {0x71, 0x80},
    {0xf0, 0x36},
    {0x32, 0x44},
    {0xf0, 0x36},
    {0x3e, 0xe0},
    {0x70, 0x56},
    {0x7c, 0x43},
    {0x7d, 0x47},
    {0x74, 0x00},
    {0x75, 0x00},
    {0x76, 0x00},
    {0xa0, 0x47},
    {0xa1, 0x5f},
    {0x96, 0x22},
    {0x97, 0x22},
    {0x98, 0x22},
    {0xf0, 0x00},
    {0x72, 0x38},
    {0x7a, 0x80},
    {0x85, 0x18},
    {0x9b, 0x35},
    {0x9e, 0x20},
    {0xd0, 0x66},
    {0xd1, 0x34},
    {0Xd3, 0x44},
    {0xd6, 0x44},
    {0xb0, 0x41},
    {0xb2, 0x48},
    {0xb3, 0xf4},
    {0xb4, 0x0b},
    {0xb5, 0x78},
    {0xba, 0xff},
    {0xbb, 0xc0},
    {0xbc, 0x90},
    {0xbd, 0x3a},
    {0xc1, 0x67},
    {0xf0, 0x01},
    {0x20, 0x11},
    {0x23, 0x90},
    {0x24, 0x15},
    {0x25, 0x87},
    {0xbc, 0x9f},
    {0xbd, 0x3a},
    {0x48, 0xe6},
    {0x49, 0xc0},
    {0x4a, 0xd0},
    {0x4b, 0x48},

    // [cvbs_on]
    {0xf0, 0x36},
    {0x11, 0x00},
    {0xf0, 0x01},
    {0x79, 0xf1},

    // [cvbs_off]
    {0xf0, 0x36},
    {0x11, 0x80},
    {0xf0, 0x01},
    {0x79, 0xc1},
};

/*
[Sensor]
SensorName=SC031IOT
width=640
height=480
port=1
type=2
pin=3
SlaveID=0xd0
mode=0
FlagReg=0xf7
FlagMask=0xff
FlagData=0xfa
FlagReg1=0xf8
FlagMask1=0xff
FlagData1=0x46
outformat=0
mclk=27
avdd=2.80000
dovdd=2.800000
dvdd=1.5

Ext0=0
Ext1=0
Ext2=0
AFVCC=0.0000
VPP=0.000000
*/
/* 27M MCLK, 30fps
static const uint8_t sc030iot_default_init_regs[][2] = {
    {0xf0, 0x30},
    {0x01, 0xff},
    {0x02, 0xff},
    {0x22, 0x07},
    {0x19, 0xff},
    {0x3f, 0x82},
    {0x30, 0x02},
    {0xf0, 0x01},
    {0x70, 0x00},
    {0x71, 0x80},
    {0x72, 0x20},
    {0x73, 0x00},
    {0x74, 0xe0},
    {0x75, 0x10},
    {0x76, 0x81},
    {0x77, 0x88},
    {0x78, 0xe1},
    {0x79, 0x01},
    {0xf5, 0x01},
    {0xf4, 0x0a},
    {0xf0, 0x36},
    {0x37, 0x79},
    {0x31, 0x82},
    {0x3e, 0x60},
    {0x30, 0xf0},
    {0x33, 0x33},
    {0xf0, 0x32},
    {0x48, 0x02},
    {0xf0, 0x33},
    {0x02, 0x12},
    {0x7c, 0x02},
    {0x7d, 0x0e},
    {0xa2, 0x04},
    {0x5e, 0x06},
    {0x5f, 0x0a},
    {0x0b, 0x58},
    {0x06, 0x38},
    {0xf0, 0x32},
    {0x48, 0x02},
    {0xf0, 0x39},
    {0x02, 0x70},
    {0xf0, 0x45},
    {0x09, 0x1c},
    {0xf0, 0x37},
    {0x22, 0x0d},
    {0xf0, 0x33},
    {0x33, 0x10},
    {0xb1, 0x80},
    {0x34, 0x40},
    {0x0b, 0x54},
    {0xb2, 0x78},
    {0xf0, 0x36},
    {0x11, 0x80},
    {0xf0, 0x30},
    {0x38, 0x44},
    {0xf0, 0x33},
    {0xb3, 0x51},
    {0x01, 0x10},
    {0x0b, 0x6c},
    {0x06, 0x24},
    {0xf0, 0x36},
    {0x31, 0x82},
    {0x3e, 0x60},
    {0x30, 0xf0},
    {0x33, 0x33},
    {0xf0, 0x34},
    {0x9f, 0x02},
    {0xa6, 0x40},
    {0xa7, 0x47},
    {0xe8, 0x5f},
    {0xa8, 0x51},
    {0xa9, 0x44},
    {0xe9, 0x36},
    {0xf0, 0x33},
    {0xb3, 0x51},
    {0x64, 0x17},
    {0x90, 0x01},
    {0x91, 0x03},
    {0x92, 0x07},
    {0x01, 0x10},
    {0x93, 0x10},
    {0x94, 0x10},
    {0x95, 0x10},
    {0x96, 0x01},
    {0x97, 0x07},
    {0x98, 0x1f},
    {0x99, 0x10},
    {0x9a, 0x20},
    {0x9b, 0x28},
    {0x9c, 0x28},
    {0xf0, 0x36},
    {0x70, 0x54},
    {0xb6, 0x40},
    {0xb7, 0x41},
    {0xb8, 0x43},
    {0xb9, 0x47},
    {0xba, 0x4f},
    {0xb0, 0x8b},
    {0xb1, 0x8b},
    {0xb2, 0x8b},
    {0xb3, 0x9b},
    {0xb4, 0xb8},
    {0xb5, 0xf0},
    {0x7e, 0x41},
    {0x7f, 0x47},
    {0x77, 0x80},
    {0x78, 0x84},
    {0x79, 0x8a},
    {0xa0, 0x47},
    {0xa1, 0x5f},
    {0x96, 0x43},
    {0x97, 0x44},
    {0x98, 0x54},
    {0xf0, 0x00},
    {0xf0, 0x01},
    {0x73, 0x00},
    {0x74, 0xe0},
    {0x70, 0x00},
    {0x71, 0x80},
    {0xf0, 0x36},
    {0x37, 0x74},
    {0xf0, 0x3f},
    {0x03, 0x93},
    {0xf0, 0x36},//cvbs_off
    {0x11, 0x80},
    {0xf0, 0x01},
    {0x79, 0xc1},
    {0xf0, 0x37},
    {0x24, 0x21},
    {0xf0, 0x36},
    {0x41, 0x00},
    {0xe9, 0x2c},
    {0xf0, 0x33},
    {0x33, 0x00},
    {0x34, 0x00},
    {0xb1, 0x00},
    {0xf0, 0x00},
    {0xe0, 0x04},
    {0xf0, 0x01},
    {0x73, 0x00},
    {0x74, 0xe0},
    {0x70, 0x00},
    {0x71, 0x80},
    {0xf0, 0x36},
    {0x32, 0x44},
    {0xf0, 0x36},
    {0x3e, 0xe0},
    {0x70, 0x56},
    {0x7c, 0x43},
    {0x7d, 0x47},
    {0x74, 0x00},
    {0x75, 0x00},
    {0x76, 0x00},
    {0xa0, 0x47},
    {0xa1, 0x5f},
    {0x96, 0x22},
    {0x97, 0x22},
    {0x98, 0x22},
    {0xf0, 0x00},
    {0x72, 0x38},
    {0x7a, 0x80},
    {0x85, 0x18},
    {0x9b, 0x35},
    {0x9e, 0x20},
    {0xd0, 0x66},
    {0xd1, 0x34},
    {0Xd3, 0x44},
    {0xd6, 0x44},
    {0xb0, 0x41},
    {0xb2, 0x48},
    {0xb3, 0xf4},
    {0xb4, 0x0b},
    {0xb5, 0x78},
    {0xba, 0xff},
    {0xbb, 0xc0},
    {0xbc, 0x90},
    {0xbd, 0x3a},
    {0xc1, 0x67},
    {0xf0, 0x01},
    {0x20, 0x11},
    {0x23, 0x90},
    {0x24, 0x15},
    {0x25, 0x87},
    {0xbc, 0x9f},
    {0xbd, 0x3a},
    {0x48, 0xe6},
    {0x49, 0xc0},
    {0x4a, 0xd0},
    {0x4b, 0x48},

    // [cvbs_on]
    {0xf0, 0x36},
    {0x11, 0x00},
    {0xf0, 0x01},
    {0x79, 0xf1},

    // [cvbs_off]
    {0xf0, 0x36},
    {0x11, 0x80},
    {0xf0, 0x01},
    {0x79, 0xc1},
};

*/