# ESP LCD Touch Component

[![Component Registry](https://components.espressif.com/components/espressif/esp_lcd_touch/badge.svg)](https://components.espressif.com/components/espressif/esp_lcd_touch)

This componnent is main esp_lcd_touch component which defines main functions and types for easy adding specific touch controller component.

## Supported features

- [x] Read XY
- [x] Swap XY
- [x] Mirror X
- [x] Mirror Y
- [x] Interrupt callback
- [x] Sleep mode
- [ ] Calibration

