file(GLOB_RECURSE IMAGE_SOURCES images/*.c)

idf_component_register(SRCS "esp_lvgl_port.c" ${IMAGE_SOURCES} INCLUDE_DIRS "include" REQUIRES "esp_lcd" PRIV_REQUIRES "esp_timer")

idf_build_get_property(build_components BUILD_COMPONENTS)
if("espressif__button" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::espressif__button)
endif()
if("button" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::button)
endif()
if("espressif__esp_lcd_touch" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::espressif__esp_lcd_touch)
endif()
if("esp_lcd_touch" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::esp_lcd_touch)
endif()
if("espressif__knob" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::espressif__knob)
endif()
if("knob" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::knob)
endif()
if("espressif__usb_host_hid" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::espressif__usb_host_hid)
endif()
if("usb_host_hid" IN_LIST build_components)
    target_link_libraries(${COMPONENT_LIB} PRIVATE idf::usb_host_hid)
endif()
